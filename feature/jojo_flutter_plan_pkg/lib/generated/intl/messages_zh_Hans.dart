// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh_Hans locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh_Hans';

  static String m0(count) => "共${count}个收到的花花";

  static String m1(unFinishCount) => "${unFinishCount}个未完成";

  static String m2(beforeSkuName, changeSkuName) =>
      "仅可更换一次，请谨慎操作\n更换前：${beforeSkuName}\n更换后：${changeSkuName}";

  static String m3(days, hours) => "剩余${days}天${hours}小时";

  static String m4(hours, minutes) => "剩余${hours}:${minutes}";

  static String m5(days) => "在叫叫学习了${days}天";

  static String m6(days) => "累计学习${days}天";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "JoJo": MessageLookupByLibrary.simpleMessage("叫叫"),
        "aceptTask": MessageLookupByLibrary.simpleMessage("接受任务"),
        "achievement": MessageLookupByLibrary.simpleMessage("成就"),
        "achievement_detail": MessageLookupByLibrary.simpleMessage("成就详情"),
        "achievementsDetail": MessageLookupByLibrary.simpleMessage("成就详情"),
        "addPartner": MessageLookupByLibrary.simpleMessage("添加学伴"),
        "afterClass": MessageLookupByLibrary.simpleMessage("学后服务"),
        "afterClassSevice": MessageLookupByLibrary.simpleMessage("学后服务"),
        "all": MessageLookupByLibrary.simpleMessage("全部"),
        "all_courses": MessageLookupByLibrary.simpleMessage("全部计划"),
        "alreadyLight": MessageLookupByLibrary.simpleMessage("已点亮"),
        "alreadyPoked": MessageLookupByLibrary.simpleMessage("已戳"),
        "alreadySend": MessageLookupByLibrary.simpleMessage("已送出"),
        "babyProfileChildrenPrivacyPolicy":
            MessageLookupByLibrary.simpleMessage("《儿童隐私政策》"),
        "babyProfileJojoWill": MessageLookupByLibrary.simpleMessage("叫叫会基于"),
        "babyProfileLinkCard": MessageLookupByLibrary.simpleMessage("领取学习卡"),
        "babyProfileSaveInformation":
            MessageLookupByLibrary.simpleMessage("妥善存储隐私信息"),
        "babyProfileTimeFormat":
            MessageLookupByLibrary.simpleMessage("yyyy年MM月dd日"),
        "babyProfileTipText":
            MessageLookupByLibrary.simpleMessage("准确的信息帮助将宝贝获得\n更精准的指导和内容推荐"),
        "balance": MessageLookupByLibrary.simpleMessage("资产"),
        "basicKnowledgeRevise": MessageLookupByLibrary.simpleMessage("基础知识审校"),
        "beAStudyPartner": MessageLookupByLibrary.simpleMessage("成为学伴"),
        "beans": MessageLookupByLibrary.simpleMessage("成长豆"),
        "bestHistory": MessageLookupByLibrary.simpleMessage("历史最佳"),
        "birthday": MessageLookupByLibrary.simpleMessage("生日"),
        "buyAndUseCurrentProp":
            MessageLookupByLibrary.simpleMessage("购买并使用当前道具？"),
        "buyAndUseProp": MessageLookupByLibrary.simpleMessage("购买并使用"),
        "buyFailed": MessageLookupByLibrary.simpleMessage("购买失败"),
        "buyFailedAndBack": MessageLookupByLibrary.simpleMessage("道具购买失败，请返回"),
        "buyLock": MessageLookupByLibrary.simpleMessage("兑换限制中"),
        "cancel": MessageLookupByLibrary.simpleMessage("取消"),
        "changeThemeMonthDown":
            MessageLookupByLibrary.simpleMessage("上拉切换到下个主题"),
        "changeThemeMonthUp": MessageLookupByLibrary.simpleMessage("下拉切换到上个主题"),
        "classStart": MessageLookupByLibrary.simpleMessage("开始"),
        "classStartingSoon": MessageLookupByLibrary.simpleMessage("课程即将开始"),
        "classTxt": MessageLookupByLibrary.simpleMessage("次"),
        "clickAndContinue": MessageLookupByLibrary.simpleMessage("点击继续"),
        "clickAndOpen": MessageLookupByLibrary.simpleMessage("点击打开"),
        "completeAllCourses": MessageLookupByLibrary.simpleMessage("一键完成"),
        "completionStatus": MessageLookupByLibrary.simpleMessage("完成情况"),
        "confirm": MessageLookupByLibrary.simpleMessage("确定"),
        "congratulationsGetGoods":
            MessageLookupByLibrary.simpleMessage("恭喜获得物品！"),
        "consecutiveTitle": MessageLookupByLibrary.simpleMessage("连续坚持"),
        "consecutiveUnit": MessageLookupByLibrary.simpleMessage("天"),
        "consecutiveWin": MessageLookupByLibrary.simpleMessage("连胜"),
        "consecutiveWinCalendar": MessageLookupByLibrary.simpleMessage("连胜日历"),
        "consecutiveWinMessage": MessageLookupByLibrary.simpleMessage("连接连胜"),
        "consecutiveWinTool": MessageLookupByLibrary.simpleMessage("连胜道具"),
        "consecutiveWinToolMessage":
            MessageLookupByLibrary.simpleMessage("连结连续学天数"),
        "continueIceDayNotChangeTip":
            MessageLookupByLibrary.simpleMessage("连续坚持中断啦......"),
        "continueMaintain": MessageLookupByLibrary.simpleMessage("继续保持"),
        "continueMilestone": MessageLookupByLibrary.simpleMessage("连胜里程碑冲刺中！"),
        "continueNormalDayNotChangeTip":
            MessageLookupByLibrary.simpleMessage("超级赞！\n点亮一个属于你的学习火焰"),
        "continueTodayNoFinishTip":
            MessageLookupByLibrary.simpleMessage("什么！今天还没学习？"),
        "continuousPersistence": MessageLookupByLibrary.simpleMessage("连续坚持"),
        "continuousSprintOpen": MessageLookupByLibrary.simpleMessage("连胜冲刺开启"),
        "courseGift": MessageLookupByLibrary.simpleMessage("赠送内容"),
        "course_detail": MessageLookupByLibrary.simpleMessage("计划详情"),
        "currentStageUnlock":
            MessageLookupByLibrary.simpleMessage("当前主题还没有解锁哦"),
        "day": MessageLookupByLibrary.simpleMessage("日"),
        "deleteCancel": MessageLookupByLibrary.simpleMessage("再想想"),
        "deleteConfirm": MessageLookupByLibrary.simpleMessage("确认解除"),
        "deletePartnerDetails":
            MessageLookupByLibrary.simpleMessage("如果想要恢复关系，需要对方再次通过"),
        "deletePartnerTitle":
            MessageLookupByLibrary.simpleMessage("你确认要解除学伴关系吗？"),
        "diTxt": MessageLookupByLibrary.simpleMessage("第"),
        "dieBigTip": MessageLookupByLibrary.simpleMessage("就差一步，使用道具复活吧！"),
        "dynamicEmpty": MessageLookupByLibrary.simpleMessage("暂时没有新动态哦"),
        "editDress": MessageLookupByLibrary.simpleMessage("换装"),
        "emptyAllLessonData":
            MessageLookupByLibrary.simpleMessage("这个阶段没有课程内容哦～"),
        "emptyCoursesTip": MessageLookupByLibrary.simpleMessage("当前没有内容哦～"),
        "emptyData": MessageLookupByLibrary.simpleMessage("暂无数据"),
        "emptyPendingMakeupCoursesTip":
            MessageLookupByLibrary.simpleMessage("这个阶段没有要补读的内容哦！"),
        "emptyReviewLessonData":
            MessageLookupByLibrary.simpleMessage("这个阶段没有要补读的内容哦～"),
        "expand": MessageLookupByLibrary.simpleMessage("展开"),
        "eyeProtectionDesc": MessageLookupByLibrary.simpleMessage(
            "看视频的同时注意保护视力哦~开启护眼模式，叫叫陪你养成良好用眼习惯。"),
        "eyeProtectionNextTime": MessageLookupByLibrary.simpleMessage("下次再说"),
        "eyeProtectionOpen": MessageLookupByLibrary.simpleMessage("开启"),
        "eyeProtectionPrinciple":
            MessageLookupByLibrary.simpleMessage("查看护眼原理"),
        "eyeProtectionSettingTip":
            MessageLookupByLibrary.simpleMessage("可在“我的-设置”中开启/关闭护眼模式"),
        "eyeProtectionTitle": MessageLookupByLibrary.simpleMessage("开启护眼模式"),
        "feed": MessageLookupByLibrary.simpleMessage("动态"),
        "findPartner": MessageLookupByLibrary.simpleMessage("发现学伴"),
        "five": MessageLookupByLibrary.simpleMessage("五"),
        "fold": MessageLookupByLibrary.simpleMessage("收起"),
        "forceIgnore": MessageLookupByLibrary.simpleMessage("狠心忽略"),
        "forzenFinalBigTip":
            MessageLookupByLibrary.simpleMessage("点燃你的学习热情，现在就去营救~！"),
        "four": MessageLookupByLibrary.simpleMessage("四"),
        "frozenBigTip": MessageLookupByLibrary.simpleMessage("被你的学习热情融化了！"),
        "functionDes": MessageLookupByLibrary.simpleMessage("道具功能"),
        "gemstone": MessageLookupByLibrary.simpleMessage("宝石"),
        "getFlowers": m0,
        "getFuncDes": MessageLookupByLibrary.simpleMessage("获取途径"),
        "getGift": MessageLookupByLibrary.simpleMessage("去领取"),
        "getOneBox": MessageLookupByLibrary.simpleMessage("获得一个宝箱！"),
        "goAndSee": MessageLookupByLibrary.simpleMessage("去看看"),
        "goGetLesson": MessageLookupByLibrary.simpleMessage("去领课"),
        "goRaffle": MessageLookupByLibrary.simpleMessage("去抽奖"),
        "goScan": MessageLookupByLibrary.simpleMessage("去查看"),
        "goShare": MessageLookupByLibrary.simpleMessage("去分享"),
        "goStudy": MessageLookupByLibrary.simpleMessage("去学习"),
        "goToDress": MessageLookupByLibrary.simpleMessage("去装扮"),
        "goToLight": MessageLookupByLibrary.simpleMessage("点亮"),
        "grade": MessageLookupByLibrary.simpleMessage("年级"),
        "greenPulse": MessageLookupByLibrary.simpleMessage("绿豆"),
        "growthRecord": MessageLookupByLibrary.simpleMessage("成长记录"),
        "hidePastCourses": MessageLookupByLibrary.simpleMessage("收起往期计划服务"),
        "highestMedalMessage":
            MessageLookupByLibrary.simpleMessage("太棒了！已经是最高等级！"),
        "iAm": MessageLookupByLibrary.simpleMessage("我是"),
        "iKnow": MessageLookupByLibrary.simpleMessage("我知道了"),
        "iWillCome": MessageLookupByLibrary.simpleMessage("我一定来"),
        "iamGood": MessageLookupByLibrary.simpleMessage("我一定行"),
        "isMyPartner": MessageLookupByLibrary.simpleMessage("已成为学伴"),
        "knowMore": MessageLookupByLibrary.simpleMessage("了解一下"),
        "konw": MessageLookupByLibrary.simpleMessage("知道了"),
        "lackTool": MessageLookupByLibrary.simpleMessage("您的道具不足，现在就去获得道具吧"),
        "leanDataStatistics": MessageLookupByLibrary.simpleMessage("综合统计"),
        "learnAgain": MessageLookupByLibrary.simpleMessage("再学一次"),
        "learnedInJOJO": MessageLookupByLibrary.simpleMessage("已加入叫叫"),
        "learningLessonIsEmpty":
            MessageLookupByLibrary.simpleMessage("当前没有进行中的计划服务哦~"),
        "learningReady": MessageLookupByLibrary.simpleMessage("学前准备"),
        "learningReadyUnFinish": m1,
        "lesson": MessageLookupByLibrary.simpleMessage("计划"),
        "lessonAll": MessageLookupByLibrary.simpleMessage("全部内容"),
        "lessonChange": MessageLookupByLibrary.simpleMessage("更换"),
        "lessonChangeActionBackPlanHome":
            MessageLookupByLibrary.simpleMessage("返回学习页面"),
        "lessonChangeActionFail": MessageLookupByLibrary.simpleMessage("激活失败"),
        "lessonChangeActionFailDetail":
            MessageLookupByLibrary.simpleMessage("新年级激活失败，请联系客服处理!"),
        "lessonChangeActionTimeout":
            MessageLookupByLibrary.simpleMessage("更换超时"),
        "lessonChangeActionTimeoutDetail":
            MessageLookupByLibrary.simpleMessage("排队更换中，请稍后回来查看新年级!"),
        "lessonChangeCancel": MessageLookupByLibrary.simpleMessage("取消"),
        "lessonChangeDes": m2,
        "lessonChangeFail": MessageLookupByLibrary.simpleMessage("年级更换失败"),
        "lessonChangeFailContact": MessageLookupByLibrary.simpleMessage("联系客服"),
        "lessonChangeFailDeal": MessageLookupByLibrary.simpleMessage("处理"),
        "lessonChangeFailRetry":
            MessageLookupByLibrary.simpleMessage("请稍后重试，或"),
        "lessonChangeRetry": MessageLookupByLibrary.simpleMessage("重试"),
        "lessonChangeSuccess": MessageLookupByLibrary.simpleMessage("年级更换成功"),
        "lessonInterested": MessageLookupByLibrary.simpleMessage("更换年级"),
        "lessonInteresting": MessageLookupByLibrary.simpleMessage("权益生效中"),
        "lessonIsEmpty": MessageLookupByLibrary.simpleMessage("今天没有计划哦~"),
        "lessonLoadingText": MessageLookupByLibrary.simpleMessage("更换中…"),
        "lessonUnChange":
            MessageLookupByLibrary.simpleMessage("您已更换过一次年级，无法再次更换"),
        "lightUp": MessageLookupByLibrary.simpleMessage("点亮"),
        "loadingReadyTip": MessageLookupByLibrary.simpleMessage("精彩正在准备中~"),
        "lock": MessageLookupByLibrary.simpleMessage("未解锁"),
        "mathScheduleList": MessageLookupByLibrary.simpleMessage("思维计划表"),
        "medalDescription": MessageLookupByLibrary.simpleMessage("奖章介绍"),
        "milestoneAward": MessageLookupByLibrary.simpleMessage("里程碑奖励"),
        "milestoneEmpty": MessageLookupByLibrary.simpleMessage("暂未获得里程碑奖励哦"),
        "month": MessageLookupByLibrary.simpleMessage("月"),
        "more": MessageLookupByLibrary.simpleMessage("更多"),
        "moreLessonNeedLearn":
            MessageLookupByLibrary.simpleMessage("还有其他需要补学的内容哦~"),
        "multipleRevise": MessageLookupByLibrary.simpleMessage("多题审校"),
        "mutualCompanionship": MessageLookupByLibrary.simpleMessage("互为学伴"),
        "myAchievements": MessageLookupByLibrary.simpleMessage("我的成就"),
        "myConsecutiveWin": MessageLookupByLibrary.simpleMessage("我的连胜"),
        "myGift": MessageLookupByLibrary.simpleMessage("我的奖励"),
        "myPartner": MessageLookupByLibrary.simpleMessage("我的学伴"),
        "needToWait": MessageLookupByLibrary.simpleMessage("请问需要继续等待吗？"),
        "next": MessageLookupByLibrary.simpleMessage("下一个"),
        "nextStep": MessageLookupByLibrary.simpleMessage("下一步"),
        "noAchievementsYet": MessageLookupByLibrary.simpleMessage("暂未获得成就哦"),
        "noGift": MessageLookupByLibrary.simpleMessage("还没有获得奖励哦"),
        "noPlansAvailable": MessageLookupByLibrary.simpleMessage("暂无该类型计划"),
        "noSearchData": MessageLookupByLibrary.simpleMessage("没有查找到内容哦"),
        "num": MessageLookupByLibrary.simpleMessage("个"),
        "ok": MessageLookupByLibrary.simpleMessage("好的"),
        "one": MessageLookupByLibrary.simpleMessage("一"),
        "ownedCount": MessageLookupByLibrary.simpleMessage("当前剩余"),
        "parameterException": MessageLookupByLibrary.simpleMessage("参数异常"),
        "partner": MessageLookupByLibrary.simpleMessage("学伴"),
        "partnerEmpty": MessageLookupByLibrary.simpleMessage("还没有学伴，去添加一些吧"),
        "partnerMessage": MessageLookupByLibrary.simpleMessage("消息"),
        "pendingMakeupCourses_abbr": MessageLookupByLibrary.simpleMessage("待补"),
        "pictureGenerating": MessageLookupByLibrary.simpleMessage("图片生成中..."),
        "planLessonAutoSubscribe": MessageLookupByLibrary.simpleMessage("自动续订"),
        "planLessonClassTime": MessageLookupByLibrary.simpleMessage("开始时间"),
        "planLessonSubscribe": MessageLookupByLibrary.simpleMessage("学习预告"),
        "planLessonSupplementTag": MessageLookupByLibrary.simpleMessage("补"),
        "planLessonTodayTag": MessageLookupByLibrary.simpleMessage("今"),
        "pleaseChooseThemeMonth": MessageLookupByLibrary.simpleMessage("请选择主题"),
        "pleaseEditBabyName": MessageLookupByLibrary.simpleMessage("请填写宝贝昵称"),
        "pleaseSelect": MessageLookupByLibrary.simpleMessage("请选择"),
        "pleaseWait": MessageLookupByLibrary.simpleMessage("请稍候"),
        "poke": MessageLookupByLibrary.simpleMessage("戳一戳"),
        "propBuyTimeout": MessageLookupByLibrary.simpleMessage("道具购买暂无响应，"),
        "pureNewUserPageMessage":
            MessageLookupByLibrary.simpleMessage("学习之旅就像张空白画布将充满无限可能"),
        "qq": MessageLookupByLibrary.simpleMessage("QQ"),
        "qrcodeTip": MessageLookupByLibrary.simpleMessage("你想成为我的学习搭子吗？"),
        "receive": MessageLookupByLibrary.simpleMessage("收下"),
        "recentSearch": MessageLookupByLibrary.simpleMessage("最近搜索"),
        "relearnForAssistance": MessageLookupByLibrary.simpleMessage("补学救援"),
        "remain": MessageLookupByLibrary.simpleMessage("剩余"),
        "remainingDaysHours": m3,
        "remainingHoursMinutes": m4,
        "rememberGoStudyOnTime":
            MessageLookupByLibrary.simpleMessage("记得按时来学习哦～"),
        "repairStudy": MessageLookupByLibrary.simpleMessage("补学拯救"),
        "review": MessageLookupByLibrary.simpleMessage("回顾"),
        "reviewAssistant": MessageLookupByLibrary.simpleMessage("补学助手"),
        "reviewPageRequestError":
            MessageLookupByLibrary.simpleMessage("内容获取失败，请重试..."),
        "reviser": MessageLookupByLibrary.simpleMessage("审"),
        "revive": MessageLookupByLibrary.simpleMessage("复活"),
        "reviveRepair": MessageLookupByLibrary.simpleMessage("复活并拯救"),
        "reviveTool": MessageLookupByLibrary.simpleMessage("复活道具"),
        "ruleDesc": MessageLookupByLibrary.simpleMessage("规则说明"),
        "saveAlbum": MessageLookupByLibrary.simpleMessage("保存相册"),
        "saveFail": MessageLookupByLibrary.simpleMessage("保存失败"),
        "saveSuccess": MessageLookupByLibrary.simpleMessage("保存成功"),
        "scanLearnWithMe": MessageLookupByLibrary.simpleMessage("扫码和我一起学"),
        "scanMyGift": MessageLookupByLibrary.simpleMessage("查看我的奖励"),
        "scanWlInfo": MessageLookupByLibrary.simpleMessage("查看物流"),
        "search": MessageLookupByLibrary.simpleMessage("搜索"),
        "searchHintText": MessageLookupByLibrary.simpleMessage("请输入计划名称或编号"),
        "searchKeywordEmpty": MessageLookupByLibrary.simpleMessage("缺少搜索关键字"),
        "sendFlower": MessageLookupByLibrary.simpleMessage("送花花"),
        "serviceComment": MessageLookupByLibrary.simpleMessage("服务点评"),
        "serviceCommentTips":
            MessageLookupByLibrary.simpleMessage("提交作品后，班班会进行点评"),
        "serviceEmptyComment": MessageLookupByLibrary.simpleMessage("暂无服务点评"),
        "serviceGuide": MessageLookupByLibrary.simpleMessage("班班服务"),
        "serviceSuffix": MessageLookupByLibrary.simpleMessage("班班"),
        "shareCopyTips":
            MessageLookupByLibrary.simpleMessage("已复制文本，粘贴到朋友圈让大家为你点赞吧"),
        "shareDayCountFirstTip": MessageLookupByLibrary.simpleMessage("我在"),
        "shareDayCountSecondTip": MessageLookupByLibrary.simpleMessage("连续学习了"),
        "shareFail": MessageLookupByLibrary.simpleMessage("分享失败"),
        "shareMyAchievement": MessageLookupByLibrary.simpleMessage("分享成就"),
        "shareMyHighlightMoment":
            MessageLookupByLibrary.simpleMessage("分享我的高光时刻"),
        "six": MessageLookupByLibrary.simpleMessage("六"),
        "soGood": MessageLookupByLibrary.simpleMessage("太棒了"),
        "start": MessageLookupByLibrary.simpleMessage("开始"),
        "stduyAllCount": m5,
        "studyDays": m6,
        "studyEndMessage": MessageLookupByLibrary.simpleMessage("学习已结束"),
        "studyPartner": MessageLookupByLibrary.simpleMessage("学习搭子"),
        "studyPrepGuideMessage":
            MessageLookupByLibrary.simpleMessage("先做好学习准备吧～"),
        "sureUse": MessageLookupByLibrary.simpleMessage("确认使用"),
        "systemError": MessageLookupByLibrary.simpleMessage("系统繁忙，请稍后再试"),
        "takeIt": MessageLookupByLibrary.simpleMessage("直接收下"),
        "teamStudyRecruitDetail":
            MessageLookupByLibrary.simpleMessage("一起探索知识的海洋"),
        "teamStudyRecruitSelfSuffix":
            MessageLookupByLibrary.simpleMessage("(我)"),
        "teamStudyRecruitTipComplete":
            MessageLookupByLibrary.simpleMessage("组队完成！航海小队即将起航"),
        "teamStudyRecruitTipOneLeft":
            MessageLookupByLibrary.simpleMessage("还差一位船员就可以出发啦！加油哦！"),
        "teamStudyRecruitTipTwoLeft":
            MessageLookupByLibrary.simpleMessage("凑齐三人出发吧！"),
        "teamStudyRecruitTitle":
            MessageLookupByLibrary.simpleMessage("邀请伙伴加入学习号"),
        "thinkAgain": MessageLookupByLibrary.simpleMessage("我再想想"),
        "three": MessageLookupByLibrary.simpleMessage("三"),
        "toConsecutiveWin": MessageLookupByLibrary.simpleMessage("去连接"),
        "toLight": MessageLookupByLibrary.simpleMessage("去点亮"),
        "toRepirStudy": MessageLookupByLibrary.simpleMessage("去拯救"),
        "toRevive": MessageLookupByLibrary.simpleMessage("去复活"),
        "toolUseFailMsg": MessageLookupByLibrary.simpleMessage("道具使用失败，请稍后再试"),
        "trainAddTeacherPageTitle":
            MessageLookupByLibrary.simpleMessage("获取报告解读"),
        "two": MessageLookupByLibrary.simpleMessage("二"),
        "unlockRights": MessageLookupByLibrary.simpleMessage("立即解锁"),
        "useToolFailTip": MessageLookupByLibrary.simpleMessage("道具使用失败"),
        "videoLoading": MessageLookupByLibrary.simpleMessage("加载中"),
        "viewPastCourses": MessageLookupByLibrary.simpleMessage("查看往期计划服务"),
        "waitAgree": MessageLookupByLibrary.simpleMessage("等待同意"),
        "waitClassStart": MessageLookupByLibrary.simpleMessage("待开始"),
        "waitOpenNewPlanMessage":
            MessageLookupByLibrary.simpleMessage("等待开启新的学习计划哦～"),
        "waitPass": MessageLookupByLibrary.simpleMessage("等待通过"),
        "waitStart": MessageLookupByLibrary.simpleMessage("待开始"),
        "waitStartClass": MessageLookupByLibrary.simpleMessage("待开课"),
        "weAgingStudy": MessageLookupByLibrary.simpleMessage("我还要学"),
        "wxCircle": MessageLookupByLibrary.simpleMessage("朋友圈"),
        "wxFriend": MessageLookupByLibrary.simpleMessage("微信好友"),
        "year": MessageLookupByLibrary.simpleMessage("年"),
        "youConnected": MessageLookupByLibrary.simpleMessage("已连接")
      };
}
