import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/card_course_theme_info.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/course_utils.dart';

import '../../plan_home/customization/landscape_utils.dart';

class CourseLessonWeekWidget extends StatelessWidget {
  final CoursePlanBase? preCourseCard;
  final CourseUnitLesson unitLesson;

  const CourseLessonWeekWidget(
      {super.key, required this.preCourseCard, required this.unitLesson});

  @override
  Widget build(BuildContext context) {
    return _buildCourseWidget(context);
  }

  bool _nextUnLock() {
    return unitLesson.courseCard.isUnLock();
  }

  Widget _buildCourseWidget(BuildContext context) {
    bool hasIntroductory = unitLesson.courseCard.introductory != null &&
        unitLesson.courseCard.introductory?.isNotEmpty == true;
    bool preCourseFocus = (preCourseCard is CourseCard) &&
        ((preCourseCard as CourseCard).focus == true);
    return Container(
      margin: preCourseFocus ? EdgeInsets.only(top: 40.rdp) : null,
      height: hasIntroductory ? 60.rdp : 40.rdp,
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
            child: Row(
              children: [
                SizedBox(
                  width: LandscapeUtils.isLandscape() ? 38.rdp : 28.rdp,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildLeftCircle(context), //左边的小圆点
                        Expanded(
                            child: Container(
                                width: 2.rdp,
                                color: _nextUnLock()
                                    ? context.appColors.jColorGray2
                                    : CourseUtils().formatSubjectColor(
                                        context,
                                        1,
                                        unitLesson.courseCard.mainColor ?? "")))
                      ]), //左边的线
                ),
                SizedBox(width: 17.rdp),
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWeekName(context),
                    _buildIntroductory(context)
                  ],
                )) //单元-单元引导语
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildLeftCircle(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.rdp, bottom: 2.rdp),
      alignment: Alignment.topCenter,
      height: 10.rdp,
      width: LandscapeUtils.isLandscape() ? 38.rdp : 28.rdp,
      child: Container(
        width: 8.rdp,
        height: 8.rdp,
        decoration: BoxDecoration(
          color: _nextUnLock()
              ? context.appColors.jColorGray2
              : CourseUtils().formatSubjectColor(
                  context, 2, unitLesson.courseCard.mainColor ?? ""),
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildWeekName(BuildContext context) {
    return Text(
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      unitLesson.courseCard.weekName ?? "",
      style: TextStyle(
        color: _nextUnLock()
            ? context.appColors.jColorGray4
            : CourseUtils().formatSubjectColor(
                context, 5, unitLesson.courseCard.mainColor ?? ""),
        fontWeight: FontWeight.w400,
        fontStyle: FontStyle.normal,
        fontSize: LandscapeUtils.isLandscape() ? 20.rdp : 16.rdp,
        height: 1.5,
        // 相当于150%的行高
        letterSpacing: 0.rdp,
      ),
    );
  }

  Widget _buildIntroductory(BuildContext context) {
    if (unitLesson.courseCard.introductory.isNullOrEmpty()) {
      return const SizedBox();
    }
    return Text(
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      unitLesson.courseCard.introductory ?? "",
      style: TextStyle(
        color: context.appColors.jColorGray4,
        fontWeight: FontWeight.w400,
        fontStyle: FontStyle.normal,
        fontSize: LandscapeUtils.isLandscape() ? 20.rdp : 16.rdp,
        height: 1.5,
        letterSpacing: 0.rdp,
      ),
    );
  }
}
