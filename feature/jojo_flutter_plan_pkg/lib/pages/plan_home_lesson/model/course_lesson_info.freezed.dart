// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_lesson_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

CourseLessonInfo _$CourseLessonInfoFromJson(Map<String, dynamic> json) {
  return _CourseLessonInfo.fromJson(json);
}

/// @nodoc
mixin _$CourseLessonInfo {
  int? get classId => throw _privateConstructorUsedError;
  set classId(int? value) => throw _privateConstructorUsedError;
  String? get courseImg => throw _privateConstructorUsedError;
  set courseImg(String? value) => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  set courseId(int? value) => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  set courseKey(String? value) => throw _privateConstructorUsedError; //课程key
  String? get courseLabel => throw _privateConstructorUsedError; //课程key
  set courseLabel(String? value) => throw _privateConstructorUsedError;
  String? get userPageCourseLabel => throw _privateConstructorUsedError;
  set userPageCourseLabel(String? value) =>
      throw _privateConstructorUsedError; //课程全称
  String? get subjectColor => throw _privateConstructorUsedError; //课程全称
  set subjectColor(String? value) => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  set subjectType(int? value) => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  set subjectName(String? value) => throw _privateConstructorUsedError; //科目名称
  String? get courseSegment => throw _privateConstructorUsedError; //科目名称
  set courseSegment(String? value) =>
      throw _privateConstructorUsedError; //课程阶段 “L1,L2”等
  String? get startClassDateDesc =>
      throw _privateConstructorUsedError; //课程阶段 “L1,L2”等
  set startClassDateDesc(String? value) => throw _privateConstructorUsedError;
  int? get courseType => throw _privateConstructorUsedError;
  set courseType(int? value) =>
      throw _privateConstructorUsedError; //课程类型 “年课”、“训练营”、“主题包”、“试用包“
  int? get classStatus =>
      throw _privateConstructorUsedError; //课程类型 “年课”、“训练营”、“主题包”、“试用包“
  set classStatus(int? value) => throw _privateConstructorUsedError; //班期状态
  int? get startClassTime => throw _privateConstructorUsedError; //班期状态
  set startClassTime(int? value) => throw _privateConstructorUsedError;
  ClassActivitiesVo? get activities => throw _privateConstructorUsedError;
  set activities(ClassActivitiesVo? value) =>
      throw _privateConstructorUsedError; // 活动列表
  List<TimetableNodeList>? get timetableNodeList =>
      throw _privateConstructorUsedError; // 活动列表
  set timetableNodeList(List<TimetableNodeList>? value) =>
      throw _privateConstructorUsedError; //课时列表
  List<ClassFunctionList>? get classFunctionList =>
      throw _privateConstructorUsedError; //课时列表
  set classFunctionList(List<ClassFunctionList>? value) =>
      throw _privateConstructorUsedError;
  CourseStartPrepareVo? get courseStartPrepareVo =>
      throw _privateConstructorUsedError;
  set courseStartPrepareVo(CourseStartPrepareVo? value) =>
      throw _privateConstructorUsedError; //提前准备
  RenewalTimetableVo? get renewalTimetableVo =>
      throw _privateConstructorUsedError; //提前准备
  set renewalTimetableVo(RenewalTimetableVo? value) =>
      throw _privateConstructorUsedError; //课程预告
  List<UserGifCourse?>? get userGiftCourseVoList =>
      throw _privateConstructorUsedError; //课程预告
  set userGiftCourseVoList(List<UserGifCourse?>? value) =>
      throw _privateConstructorUsedError; //赠课
  BuyCourseVo? get buyCourseVo => throw _privateConstructorUsedError; //赠课
  set buyCourseVo(BuyCourseVo? value) =>
      throw _privateConstructorUsedError; //购买按钮
  AddTeacherVo? get addTeacherVo => throw _privateConstructorUsedError; //购买按钮
  set addTeacherVo(AddTeacherVo? value) =>
      throw _privateConstructorUsedError; //训练营加老师
  FriendCardInfo? get friendCardInfo =>
      throw _privateConstructorUsedError; //训练营加老师
  set friendCardInfo(FriendCardInfo? value) =>
      throw _privateConstructorUsedError; //好友卡头部
  int? get pageCount => throw _privateConstructorUsedError; //好友卡头部
  set pageCount(int? value) => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;
  set pageSize(int? value) => throw _privateConstructorUsedError;
  AllProductsEntranceClass? get allSkuEntranceVo =>
      throw _privateConstructorUsedError;
  set allSkuEntranceVo(AllProductsEntranceClass? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseLessonInfoCopyWith<CourseLessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseLessonInfoCopyWith<$Res> {
  factory $CourseLessonInfoCopyWith(
          CourseLessonInfo value, $Res Function(CourseLessonInfo) then) =
      _$CourseLessonInfoCopyWithImpl<$Res, CourseLessonInfo>;
  @useResult
  $Res call(
      {int? classId,
      String? courseImg,
      int? courseId,
      String? courseKey,
      String? courseLabel,
      String? userPageCourseLabel,
      String? subjectColor,
      int? subjectType,
      String? subjectName,
      String? courseSegment,
      String? startClassDateDesc,
      int? courseType,
      int? classStatus,
      int? startClassTime,
      ClassActivitiesVo? activities,
      List<TimetableNodeList>? timetableNodeList,
      List<ClassFunctionList>? classFunctionList,
      CourseStartPrepareVo? courseStartPrepareVo,
      RenewalTimetableVo? renewalTimetableVo,
      List<UserGifCourse?>? userGiftCourseVoList,
      BuyCourseVo? buyCourseVo,
      AddTeacherVo? addTeacherVo,
      FriendCardInfo? friendCardInfo,
      int? pageCount,
      int? pageSize,
      AllProductsEntranceClass? allSkuEntranceVo});

  $ClassActivitiesVoCopyWith<$Res>? get activities;
  $CourseStartPrepareVoCopyWith<$Res>? get courseStartPrepareVo;
  $RenewalTimetableVoCopyWith<$Res>? get renewalTimetableVo;
  $BuyCourseVoCopyWith<$Res>? get buyCourseVo;
  $AddTeacherVoCopyWith<$Res>? get addTeacherVo;
  $FriendCardInfoCopyWith<$Res>? get friendCardInfo;
  $AllProductsEntranceClassCopyWith<$Res>? get allSkuEntranceVo;
}

/// @nodoc
class _$CourseLessonInfoCopyWithImpl<$Res, $Val extends CourseLessonInfo>
    implements $CourseLessonInfoCopyWith<$Res> {
  _$CourseLessonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? courseImg = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseLabel = freezed,
    Object? userPageCourseLabel = freezed,
    Object? subjectColor = freezed,
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? courseSegment = freezed,
    Object? startClassDateDesc = freezed,
    Object? courseType = freezed,
    Object? classStatus = freezed,
    Object? startClassTime = freezed,
    Object? activities = freezed,
    Object? timetableNodeList = freezed,
    Object? classFunctionList = freezed,
    Object? courseStartPrepareVo = freezed,
    Object? renewalTimetableVo = freezed,
    Object? userGiftCourseVoList = freezed,
    Object? buyCourseVo = freezed,
    Object? addTeacherVo = freezed,
    Object? friendCardInfo = freezed,
    Object? pageCount = freezed,
    Object? pageSize = freezed,
    Object? allSkuEntranceVo = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseImg: freezed == courseImg
          ? _value.courseImg
          : courseImg // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      userPageCourseLabel: freezed == userPageCourseLabel
          ? _value.userPageCourseLabel
          : userPageCourseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      startClassDateDesc: freezed == startClassDateDesc
          ? _value.startClassDateDesc
          : startClassDateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      classStatus: freezed == classStatus
          ? _value.classStatus
          : classStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      activities: freezed == activities
          ? _value.activities
          : activities // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesVo?,
      timetableNodeList: freezed == timetableNodeList
          ? _value.timetableNodeList
          : timetableNodeList // ignore: cast_nullable_to_non_nullable
              as List<TimetableNodeList>?,
      classFunctionList: freezed == classFunctionList
          ? _value.classFunctionList
          : classFunctionList // ignore: cast_nullable_to_non_nullable
              as List<ClassFunctionList>?,
      courseStartPrepareVo: freezed == courseStartPrepareVo
          ? _value.courseStartPrepareVo
          : courseStartPrepareVo // ignore: cast_nullable_to_non_nullable
              as CourseStartPrepareVo?,
      renewalTimetableVo: freezed == renewalTimetableVo
          ? _value.renewalTimetableVo
          : renewalTimetableVo // ignore: cast_nullable_to_non_nullable
              as RenewalTimetableVo?,
      userGiftCourseVoList: freezed == userGiftCourseVoList
          ? _value.userGiftCourseVoList
          : userGiftCourseVoList // ignore: cast_nullable_to_non_nullable
              as List<UserGifCourse?>?,
      buyCourseVo: freezed == buyCourseVo
          ? _value.buyCourseVo
          : buyCourseVo // ignore: cast_nullable_to_non_nullable
              as BuyCourseVo?,
      addTeacherVo: freezed == addTeacherVo
          ? _value.addTeacherVo
          : addTeacherVo // ignore: cast_nullable_to_non_nullable
              as AddTeacherVo?,
      friendCardInfo: freezed == friendCardInfo
          ? _value.friendCardInfo
          : friendCardInfo // ignore: cast_nullable_to_non_nullable
              as FriendCardInfo?,
      pageCount: freezed == pageCount
          ? _value.pageCount
          : pageCount // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      allSkuEntranceVo: freezed == allSkuEntranceVo
          ? _value.allSkuEntranceVo
          : allSkuEntranceVo // ignore: cast_nullable_to_non_nullable
              as AllProductsEntranceClass?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesVoCopyWith<$Res>? get activities {
    if (_value.activities == null) {
      return null;
    }

    return $ClassActivitiesVoCopyWith<$Res>(_value.activities!, (value) {
      return _then(_value.copyWith(activities: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $CourseStartPrepareVoCopyWith<$Res>? get courseStartPrepareVo {
    if (_value.courseStartPrepareVo == null) {
      return null;
    }

    return $CourseStartPrepareVoCopyWith<$Res>(_value.courseStartPrepareVo!,
        (value) {
      return _then(_value.copyWith(courseStartPrepareVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $RenewalTimetableVoCopyWith<$Res>? get renewalTimetableVo {
    if (_value.renewalTimetableVo == null) {
      return null;
    }

    return $RenewalTimetableVoCopyWith<$Res>(_value.renewalTimetableVo!,
        (value) {
      return _then(_value.copyWith(renewalTimetableVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $BuyCourseVoCopyWith<$Res>? get buyCourseVo {
    if (_value.buyCourseVo == null) {
      return null;
    }

    return $BuyCourseVoCopyWith<$Res>(_value.buyCourseVo!, (value) {
      return _then(_value.copyWith(buyCourseVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AddTeacherVoCopyWith<$Res>? get addTeacherVo {
    if (_value.addTeacherVo == null) {
      return null;
    }

    return $AddTeacherVoCopyWith<$Res>(_value.addTeacherVo!, (value) {
      return _then(_value.copyWith(addTeacherVo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $FriendCardInfoCopyWith<$Res>? get friendCardInfo {
    if (_value.friendCardInfo == null) {
      return null;
    }

    return $FriendCardInfoCopyWith<$Res>(_value.friendCardInfo!, (value) {
      return _then(_value.copyWith(friendCardInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AllProductsEntranceClassCopyWith<$Res>? get allSkuEntranceVo {
    if (_value.allSkuEntranceVo == null) {
      return null;
    }

    return $AllProductsEntranceClassCopyWith<$Res>(_value.allSkuEntranceVo!,
        (value) {
      return _then(_value.copyWith(allSkuEntranceVo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_CourseLessonInfoCopyWith<$Res>
    implements $CourseLessonInfoCopyWith<$Res> {
  factory _$$_CourseLessonInfoCopyWith(
          _$_CourseLessonInfo value, $Res Function(_$_CourseLessonInfo) then) =
      __$$_CourseLessonInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? classId,
      String? courseImg,
      int? courseId,
      String? courseKey,
      String? courseLabel,
      String? userPageCourseLabel,
      String? subjectColor,
      int? subjectType,
      String? subjectName,
      String? courseSegment,
      String? startClassDateDesc,
      int? courseType,
      int? classStatus,
      int? startClassTime,
      ClassActivitiesVo? activities,
      List<TimetableNodeList>? timetableNodeList,
      List<ClassFunctionList>? classFunctionList,
      CourseStartPrepareVo? courseStartPrepareVo,
      RenewalTimetableVo? renewalTimetableVo,
      List<UserGifCourse?>? userGiftCourseVoList,
      BuyCourseVo? buyCourseVo,
      AddTeacherVo? addTeacherVo,
      FriendCardInfo? friendCardInfo,
      int? pageCount,
      int? pageSize,
      AllProductsEntranceClass? allSkuEntranceVo});

  @override
  $ClassActivitiesVoCopyWith<$Res>? get activities;
  @override
  $CourseStartPrepareVoCopyWith<$Res>? get courseStartPrepareVo;
  @override
  $RenewalTimetableVoCopyWith<$Res>? get renewalTimetableVo;
  @override
  $BuyCourseVoCopyWith<$Res>? get buyCourseVo;
  @override
  $AddTeacherVoCopyWith<$Res>? get addTeacherVo;
  @override
  $FriendCardInfoCopyWith<$Res>? get friendCardInfo;
  @override
  $AllProductsEntranceClassCopyWith<$Res>? get allSkuEntranceVo;
}

/// @nodoc
class __$$_CourseLessonInfoCopyWithImpl<$Res>
    extends _$CourseLessonInfoCopyWithImpl<$Res, _$_CourseLessonInfo>
    implements _$$_CourseLessonInfoCopyWith<$Res> {
  __$$_CourseLessonInfoCopyWithImpl(
      _$_CourseLessonInfo _value, $Res Function(_$_CourseLessonInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? courseImg = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? courseLabel = freezed,
    Object? userPageCourseLabel = freezed,
    Object? subjectColor = freezed,
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? courseSegment = freezed,
    Object? startClassDateDesc = freezed,
    Object? courseType = freezed,
    Object? classStatus = freezed,
    Object? startClassTime = freezed,
    Object? activities = freezed,
    Object? timetableNodeList = freezed,
    Object? classFunctionList = freezed,
    Object? courseStartPrepareVo = freezed,
    Object? renewalTimetableVo = freezed,
    Object? userGiftCourseVoList = freezed,
    Object? buyCourseVo = freezed,
    Object? addTeacherVo = freezed,
    Object? friendCardInfo = freezed,
    Object? pageCount = freezed,
    Object? pageSize = freezed,
    Object? allSkuEntranceVo = freezed,
  }) {
    return _then(_$_CourseLessonInfo(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseImg: freezed == courseImg
          ? _value.courseImg
          : courseImg // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseLabel: freezed == courseLabel
          ? _value.courseLabel
          : courseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      userPageCourseLabel: freezed == userPageCourseLabel
          ? _value.userPageCourseLabel
          : userPageCourseLabel // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSegment: freezed == courseSegment
          ? _value.courseSegment
          : courseSegment // ignore: cast_nullable_to_non_nullable
              as String?,
      startClassDateDesc: freezed == startClassDateDesc
          ? _value.startClassDateDesc
          : startClassDateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as int?,
      classStatus: freezed == classStatus
          ? _value.classStatus
          : classStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      startClassTime: freezed == startClassTime
          ? _value.startClassTime
          : startClassTime // ignore: cast_nullable_to_non_nullable
              as int?,
      activities: freezed == activities
          ? _value.activities
          : activities // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesVo?,
      timetableNodeList: freezed == timetableNodeList
          ? _value.timetableNodeList
          : timetableNodeList // ignore: cast_nullable_to_non_nullable
              as List<TimetableNodeList>?,
      classFunctionList: freezed == classFunctionList
          ? _value.classFunctionList
          : classFunctionList // ignore: cast_nullable_to_non_nullable
              as List<ClassFunctionList>?,
      courseStartPrepareVo: freezed == courseStartPrepareVo
          ? _value.courseStartPrepareVo
          : courseStartPrepareVo // ignore: cast_nullable_to_non_nullable
              as CourseStartPrepareVo?,
      renewalTimetableVo: freezed == renewalTimetableVo
          ? _value.renewalTimetableVo
          : renewalTimetableVo // ignore: cast_nullable_to_non_nullable
              as RenewalTimetableVo?,
      userGiftCourseVoList: freezed == userGiftCourseVoList
          ? _value.userGiftCourseVoList
          : userGiftCourseVoList // ignore: cast_nullable_to_non_nullable
              as List<UserGifCourse?>?,
      buyCourseVo: freezed == buyCourseVo
          ? _value.buyCourseVo
          : buyCourseVo // ignore: cast_nullable_to_non_nullable
              as BuyCourseVo?,
      addTeacherVo: freezed == addTeacherVo
          ? _value.addTeacherVo
          : addTeacherVo // ignore: cast_nullable_to_non_nullable
              as AddTeacherVo?,
      friendCardInfo: freezed == friendCardInfo
          ? _value.friendCardInfo
          : friendCardInfo // ignore: cast_nullable_to_non_nullable
              as FriendCardInfo?,
      pageCount: freezed == pageCount
          ? _value.pageCount
          : pageCount // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
      allSkuEntranceVo: freezed == allSkuEntranceVo
          ? _value.allSkuEntranceVo
          : allSkuEntranceVo // ignore: cast_nullable_to_non_nullable
              as AllProductsEntranceClass?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseLessonInfo implements _CourseLessonInfo {
  _$_CourseLessonInfo(
      {this.classId,
      this.courseImg,
      this.courseId,
      this.courseKey,
      this.courseLabel,
      this.userPageCourseLabel,
      this.subjectColor,
      this.subjectType,
      this.subjectName,
      this.courseSegment,
      this.startClassDateDesc,
      this.courseType,
      this.classStatus,
      this.startClassTime,
      this.activities,
      this.timetableNodeList,
      this.classFunctionList,
      this.courseStartPrepareVo,
      this.renewalTimetableVo,
      this.userGiftCourseVoList,
      this.buyCourseVo,
      this.addTeacherVo,
      this.friendCardInfo,
      this.pageCount,
      this.pageSize,
      this.allSkuEntranceVo});

  factory _$_CourseLessonInfo.fromJson(Map<String, dynamic> json) =>
      _$$_CourseLessonInfoFromJson(json);

  @override
  int? classId;
  @override
  String? courseImg;
  @override
  int? courseId;
  @override
  String? courseKey;
//课程key
  @override
  String? courseLabel;
  @override
  String? userPageCourseLabel;
//课程全称
  @override
  String? subjectColor;
  @override
  int? subjectType;
  @override
  String? subjectName;
//科目名称
  @override
  String? courseSegment;
//课程阶段 “L1,L2”等
  @override
  String? startClassDateDesc;
  @override
  int? courseType;
//课程类型 “年课”、“训练营”、“主题包”、“试用包“
  @override
  int? classStatus;
//班期状态
  @override
  int? startClassTime;
  @override
  ClassActivitiesVo? activities;
// 活动列表
  @override
  List<TimetableNodeList>? timetableNodeList;
//课时列表
  @override
  List<ClassFunctionList>? classFunctionList;
  @override
  CourseStartPrepareVo? courseStartPrepareVo;
//提前准备
  @override
  RenewalTimetableVo? renewalTimetableVo;
//课程预告
  @override
  List<UserGifCourse?>? userGiftCourseVoList;
//赠课
  @override
  BuyCourseVo? buyCourseVo;
//购买按钮
  @override
  AddTeacherVo? addTeacherVo;
//训练营加老师
  @override
  FriendCardInfo? friendCardInfo;
//好友卡头部
  @override
  int? pageCount;
  @override
  int? pageSize;
  @override
  AllProductsEntranceClass? allSkuEntranceVo;

  @override
  String toString() {
    return 'CourseLessonInfo(classId: $classId, courseImg: $courseImg, courseId: $courseId, courseKey: $courseKey, courseLabel: $courseLabel, userPageCourseLabel: $userPageCourseLabel, subjectColor: $subjectColor, subjectType: $subjectType, subjectName: $subjectName, courseSegment: $courseSegment, startClassDateDesc: $startClassDateDesc, courseType: $courseType, classStatus: $classStatus, startClassTime: $startClassTime, activities: $activities, timetableNodeList: $timetableNodeList, classFunctionList: $classFunctionList, courseStartPrepareVo: $courseStartPrepareVo, renewalTimetableVo: $renewalTimetableVo, userGiftCourseVoList: $userGiftCourseVoList, buyCourseVo: $buyCourseVo, addTeacherVo: $addTeacherVo, friendCardInfo: $friendCardInfo, pageCount: $pageCount, pageSize: $pageSize, allSkuEntranceVo: $allSkuEntranceVo)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseLessonInfoCopyWith<_$_CourseLessonInfo> get copyWith =>
      __$$_CourseLessonInfoCopyWithImpl<_$_CourseLessonInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseLessonInfoToJson(
      this,
    );
  }
}

abstract class _CourseLessonInfo implements CourseLessonInfo {
  factory _CourseLessonInfo(
      {int? classId,
      String? courseImg,
      int? courseId,
      String? courseKey,
      String? courseLabel,
      String? userPageCourseLabel,
      String? subjectColor,
      int? subjectType,
      String? subjectName,
      String? courseSegment,
      String? startClassDateDesc,
      int? courseType,
      int? classStatus,
      int? startClassTime,
      ClassActivitiesVo? activities,
      List<TimetableNodeList>? timetableNodeList,
      List<ClassFunctionList>? classFunctionList,
      CourseStartPrepareVo? courseStartPrepareVo,
      RenewalTimetableVo? renewalTimetableVo,
      List<UserGifCourse?>? userGiftCourseVoList,
      BuyCourseVo? buyCourseVo,
      AddTeacherVo? addTeacherVo,
      FriendCardInfo? friendCardInfo,
      int? pageCount,
      int? pageSize,
      AllProductsEntranceClass? allSkuEntranceVo}) = _$_CourseLessonInfo;

  factory _CourseLessonInfo.fromJson(Map<String, dynamic> json) =
      _$_CourseLessonInfo.fromJson;

  @override
  int? get classId;
  set classId(int? value);
  @override
  String? get courseImg;
  set courseImg(String? value);
  @override
  int? get courseId;
  set courseId(int? value);
  @override
  String? get courseKey;
  set courseKey(String? value);
  @override //课程key
  String? get courseLabel; //课程key
  set courseLabel(String? value);
  @override
  String? get userPageCourseLabel;
  set userPageCourseLabel(String? value);
  @override //课程全称
  String? get subjectColor; //课程全称
  set subjectColor(String? value);
  @override
  int? get subjectType;
  set subjectType(int? value);
  @override
  String? get subjectName;
  set subjectName(String? value);
  @override //科目名称
  String? get courseSegment; //科目名称
  set courseSegment(String? value);
  @override //课程阶段 “L1,L2”等
  String? get startClassDateDesc; //课程阶段 “L1,L2”等
  set startClassDateDesc(String? value);
  @override
  int? get courseType;
  set courseType(int? value);
  @override //课程类型 “年课”、“训练营”、“主题包”、“试用包“
  int? get classStatus; //课程类型 “年课”、“训练营”、“主题包”、“试用包“
  set classStatus(int? value);
  @override //班期状态
  int? get startClassTime; //班期状态
  set startClassTime(int? value);
  @override
  ClassActivitiesVo? get activities;
  set activities(ClassActivitiesVo? value);
  @override // 活动列表
  List<TimetableNodeList>? get timetableNodeList; // 活动列表
  set timetableNodeList(List<TimetableNodeList>? value);
  @override //课时列表
  List<ClassFunctionList>? get classFunctionList; //课时列表
  set classFunctionList(List<ClassFunctionList>? value);
  @override
  CourseStartPrepareVo? get courseStartPrepareVo;
  set courseStartPrepareVo(CourseStartPrepareVo? value);
  @override //提前准备
  RenewalTimetableVo? get renewalTimetableVo; //提前准备
  set renewalTimetableVo(RenewalTimetableVo? value);
  @override //课程预告
  List<UserGifCourse?>? get userGiftCourseVoList; //课程预告
  set userGiftCourseVoList(List<UserGifCourse?>? value);
  @override //赠课
  BuyCourseVo? get buyCourseVo; //赠课
  set buyCourseVo(BuyCourseVo? value);
  @override //购买按钮
  AddTeacherVo? get addTeacherVo; //购买按钮
  set addTeacherVo(AddTeacherVo? value);
  @override //训练营加老师
  FriendCardInfo? get friendCardInfo; //训练营加老师
  set friendCardInfo(FriendCardInfo? value);
  @override //好友卡头部
  int? get pageCount; //好友卡头部
  set pageCount(int? value);
  @override
  int? get pageSize;
  set pageSize(int? value);
  @override
  AllProductsEntranceClass? get allSkuEntranceVo;
  set allSkuEntranceVo(AllProductsEntranceClass? value);
  @override
  @JsonKey(ignore: true)
  _$$_CourseLessonInfoCopyWith<_$_CourseLessonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

AllProductsEntranceClass _$AllProductsEntranceClassFromJson(
    Map<String, dynamic> json) {
  return _AllProductsEntranceClass.fromJson(json);
}

/// @nodoc
mixin _$AllProductsEntranceClass {
  String? get title => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AllProductsEntranceClassCopyWith<AllProductsEntranceClass> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AllProductsEntranceClassCopyWith<$Res> {
  factory $AllProductsEntranceClassCopyWith(AllProductsEntranceClass value,
          $Res Function(AllProductsEntranceClass) then) =
      _$AllProductsEntranceClassCopyWithImpl<$Res, AllProductsEntranceClass>;
  @useResult
  $Res call({String? title, String? route});
}

/// @nodoc
class _$AllProductsEntranceClassCopyWithImpl<$Res,
        $Val extends AllProductsEntranceClass>
    implements $AllProductsEntranceClassCopyWith<$Res> {
  _$AllProductsEntranceClassCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AllProductsEntranceClassCopyWith<$Res>
    implements $AllProductsEntranceClassCopyWith<$Res> {
  factory _$$_AllProductsEntranceClassCopyWith(
          _$_AllProductsEntranceClass value,
          $Res Function(_$_AllProductsEntranceClass) then) =
      __$$_AllProductsEntranceClassCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? title, String? route});
}

/// @nodoc
class __$$_AllProductsEntranceClassCopyWithImpl<$Res>
    extends _$AllProductsEntranceClassCopyWithImpl<$Res,
        _$_AllProductsEntranceClass>
    implements _$$_AllProductsEntranceClassCopyWith<$Res> {
  __$$_AllProductsEntranceClassCopyWithImpl(_$_AllProductsEntranceClass _value,
      $Res Function(_$_AllProductsEntranceClass) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_AllProductsEntranceClass(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AllProductsEntranceClass implements _AllProductsEntranceClass {
  const _$_AllProductsEntranceClass({this.title, this.route});

  factory _$_AllProductsEntranceClass.fromJson(Map<String, dynamic> json) =>
      _$$_AllProductsEntranceClassFromJson(json);

  @override
  final String? title;
  @override
  final String? route;

  @override
  String toString() {
    return 'AllProductsEntranceClass(title: $title, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AllProductsEntranceClass &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, title, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AllProductsEntranceClassCopyWith<_$_AllProductsEntranceClass>
      get copyWith => __$$_AllProductsEntranceClassCopyWithImpl<
          _$_AllProductsEntranceClass>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AllProductsEntranceClassToJson(
      this,
    );
  }
}

abstract class _AllProductsEntranceClass implements AllProductsEntranceClass {
  const factory _AllProductsEntranceClass(
      {final String? title, final String? route}) = _$_AllProductsEntranceClass;

  factory _AllProductsEntranceClass.fromJson(Map<String, dynamic> json) =
      _$_AllProductsEntranceClass.fromJson;

  @override
  String? get title;
  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_AllProductsEntranceClassCopyWith<_$_AllProductsEntranceClass>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesVo _$ClassActivitiesVoFromJson(Map<String, dynamic> json) {
  return _ClassActivitiesVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesVo {
  int? get activityId => throw _privateConstructorUsedError;
  int? get advanceStartTime => throw _privateConstructorUsedError;
  int? get advanceEndTime => throw _privateConstructorUsedError;
  int? get activityStartTime => throw _privateConstructorUsedError;
  int? get activityEndTime => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  int? get hasLesson => throw _privateConstructorUsedError;
  String? get nodeRewardVoice => throw _privateConstructorUsedError;
  String? get postIcon => throw _privateConstructorUsedError;
  ClassActivitiesThemeResVo? get themeRes => throw _privateConstructorUsedError;
  List<ClassActivitiesTaskVo?>? get taskVo =>
      throw _privateConstructorUsedError;
  ClassActivitiesPopupVo? get madelGetPopup =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesVoCopyWith<ClassActivitiesVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesVoCopyWith<$Res> {
  factory $ClassActivitiesVoCopyWith(
          ClassActivitiesVo value, $Res Function(ClassActivitiesVo) then) =
      _$ClassActivitiesVoCopyWithImpl<$Res, ClassActivitiesVo>;
  @useResult
  $Res call(
      {int? activityId,
      int? advanceStartTime,
      int? advanceEndTime,
      int? activityStartTime,
      int? activityEndTime,
      int? status,
      int? hasLesson,
      String? nodeRewardVoice,
      String? postIcon,
      ClassActivitiesThemeResVo? themeRes,
      List<ClassActivitiesTaskVo?>? taskVo,
      ClassActivitiesPopupVo? madelGetPopup});

  $ClassActivitiesThemeResVoCopyWith<$Res>? get themeRes;
  $ClassActivitiesPopupVoCopyWith<$Res>? get madelGetPopup;
}

/// @nodoc
class _$ClassActivitiesVoCopyWithImpl<$Res, $Val extends ClassActivitiesVo>
    implements $ClassActivitiesVoCopyWith<$Res> {
  _$ClassActivitiesVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activityId = freezed,
    Object? advanceStartTime = freezed,
    Object? advanceEndTime = freezed,
    Object? activityStartTime = freezed,
    Object? activityEndTime = freezed,
    Object? status = freezed,
    Object? hasLesson = freezed,
    Object? nodeRewardVoice = freezed,
    Object? postIcon = freezed,
    Object? themeRes = freezed,
    Object? taskVo = freezed,
    Object? madelGetPopup = freezed,
  }) {
    return _then(_value.copyWith(
      activityId: freezed == activityId
          ? _value.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as int?,
      advanceStartTime: freezed == advanceStartTime
          ? _value.advanceStartTime
          : advanceStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      advanceEndTime: freezed == advanceEndTime
          ? _value.advanceEndTime
          : advanceEndTime // ignore: cast_nullable_to_non_nullable
              as int?,
      activityStartTime: freezed == activityStartTime
          ? _value.activityStartTime
          : activityStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      activityEndTime: freezed == activityEndTime
          ? _value.activityEndTime
          : activityEndTime // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      hasLesson: freezed == hasLesson
          ? _value.hasLesson
          : hasLesson // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeRewardVoice: freezed == nodeRewardVoice
          ? _value.nodeRewardVoice
          : nodeRewardVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      postIcon: freezed == postIcon
          ? _value.postIcon
          : postIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      themeRes: freezed == themeRes
          ? _value.themeRes
          : themeRes // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesThemeResVo?,
      taskVo: freezed == taskVo
          ? _value.taskVo
          : taskVo // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskVo?>?,
      madelGetPopup: freezed == madelGetPopup
          ? _value.madelGetPopup
          : madelGetPopup // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesPopupVo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesThemeResVoCopyWith<$Res>? get themeRes {
    if (_value.themeRes == null) {
      return null;
    }

    return $ClassActivitiesThemeResVoCopyWith<$Res>(_value.themeRes!, (value) {
      return _then(_value.copyWith(themeRes: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesPopupVoCopyWith<$Res>? get madelGetPopup {
    if (_value.madelGetPopup == null) {
      return null;
    }

    return $ClassActivitiesPopupVoCopyWith<$Res>(_value.madelGetPopup!,
        (value) {
      return _then(_value.copyWith(madelGetPopup: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesVoCopyWith<$Res>
    implements $ClassActivitiesVoCopyWith<$Res> {
  factory _$$_ClassActivitiesVoCopyWith(_$_ClassActivitiesVo value,
          $Res Function(_$_ClassActivitiesVo) then) =
      __$$_ClassActivitiesVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? activityId,
      int? advanceStartTime,
      int? advanceEndTime,
      int? activityStartTime,
      int? activityEndTime,
      int? status,
      int? hasLesson,
      String? nodeRewardVoice,
      String? postIcon,
      ClassActivitiesThemeResVo? themeRes,
      List<ClassActivitiesTaskVo?>? taskVo,
      ClassActivitiesPopupVo? madelGetPopup});

  @override
  $ClassActivitiesThemeResVoCopyWith<$Res>? get themeRes;
  @override
  $ClassActivitiesPopupVoCopyWith<$Res>? get madelGetPopup;
}

/// @nodoc
class __$$_ClassActivitiesVoCopyWithImpl<$Res>
    extends _$ClassActivitiesVoCopyWithImpl<$Res, _$_ClassActivitiesVo>
    implements _$$_ClassActivitiesVoCopyWith<$Res> {
  __$$_ClassActivitiesVoCopyWithImpl(
      _$_ClassActivitiesVo _value, $Res Function(_$_ClassActivitiesVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activityId = freezed,
    Object? advanceStartTime = freezed,
    Object? advanceEndTime = freezed,
    Object? activityStartTime = freezed,
    Object? activityEndTime = freezed,
    Object? status = freezed,
    Object? hasLesson = freezed,
    Object? nodeRewardVoice = freezed,
    Object? postIcon = freezed,
    Object? themeRes = freezed,
    Object? taskVo = freezed,
    Object? madelGetPopup = freezed,
  }) {
    return _then(_$_ClassActivitiesVo(
      activityId: freezed == activityId
          ? _value.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as int?,
      advanceStartTime: freezed == advanceStartTime
          ? _value.advanceStartTime
          : advanceStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      advanceEndTime: freezed == advanceEndTime
          ? _value.advanceEndTime
          : advanceEndTime // ignore: cast_nullable_to_non_nullable
              as int?,
      activityStartTime: freezed == activityStartTime
          ? _value.activityStartTime
          : activityStartTime // ignore: cast_nullable_to_non_nullable
              as int?,
      activityEndTime: freezed == activityEndTime
          ? _value.activityEndTime
          : activityEndTime // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      hasLesson: freezed == hasLesson
          ? _value.hasLesson
          : hasLesson // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeRewardVoice: freezed == nodeRewardVoice
          ? _value.nodeRewardVoice
          : nodeRewardVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      postIcon: freezed == postIcon
          ? _value.postIcon
          : postIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      themeRes: freezed == themeRes
          ? _value.themeRes
          : themeRes // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesThemeResVo?,
      taskVo: freezed == taskVo
          ? _value._taskVo
          : taskVo // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskVo?>?,
      madelGetPopup: freezed == madelGetPopup
          ? _value.madelGetPopup
          : madelGetPopup // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesPopupVo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesVo implements _ClassActivitiesVo {
  const _$_ClassActivitiesVo(
      {this.activityId,
      this.advanceStartTime,
      this.advanceEndTime,
      this.activityStartTime,
      this.activityEndTime,
      this.status,
      this.hasLesson,
      this.nodeRewardVoice,
      this.postIcon,
      this.themeRes,
      final List<ClassActivitiesTaskVo?>? taskVo,
      this.madelGetPopup})
      : _taskVo = taskVo;

  factory _$_ClassActivitiesVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesVoFromJson(json);

  @override
  final int? activityId;
  @override
  final int? advanceStartTime;
  @override
  final int? advanceEndTime;
  @override
  final int? activityStartTime;
  @override
  final int? activityEndTime;
  @override
  final int? status;
  @override
  final int? hasLesson;
  @override
  final String? nodeRewardVoice;
  @override
  final String? postIcon;
  @override
  final ClassActivitiesThemeResVo? themeRes;
  final List<ClassActivitiesTaskVo?>? _taskVo;
  @override
  List<ClassActivitiesTaskVo?>? get taskVo {
    final value = _taskVo;
    if (value == null) return null;
    if (_taskVo is EqualUnmodifiableListView) return _taskVo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ClassActivitiesPopupVo? madelGetPopup;

  @override
  String toString() {
    return 'ClassActivitiesVo(activityId: $activityId, advanceStartTime: $advanceStartTime, advanceEndTime: $advanceEndTime, activityStartTime: $activityStartTime, activityEndTime: $activityEndTime, status: $status, hasLesson: $hasLesson, nodeRewardVoice: $nodeRewardVoice, postIcon: $postIcon, themeRes: $themeRes, taskVo: $taskVo, madelGetPopup: $madelGetPopup)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesVo &&
            (identical(other.activityId, activityId) ||
                other.activityId == activityId) &&
            (identical(other.advanceStartTime, advanceStartTime) ||
                other.advanceStartTime == advanceStartTime) &&
            (identical(other.advanceEndTime, advanceEndTime) ||
                other.advanceEndTime == advanceEndTime) &&
            (identical(other.activityStartTime, activityStartTime) ||
                other.activityStartTime == activityStartTime) &&
            (identical(other.activityEndTime, activityEndTime) ||
                other.activityEndTime == activityEndTime) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.hasLesson, hasLesson) ||
                other.hasLesson == hasLesson) &&
            (identical(other.nodeRewardVoice, nodeRewardVoice) ||
                other.nodeRewardVoice == nodeRewardVoice) &&
            (identical(other.postIcon, postIcon) ||
                other.postIcon == postIcon) &&
            (identical(other.themeRes, themeRes) ||
                other.themeRes == themeRes) &&
            const DeepCollectionEquality().equals(other._taskVo, _taskVo) &&
            (identical(other.madelGetPopup, madelGetPopup) ||
                other.madelGetPopup == madelGetPopup));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      activityId,
      advanceStartTime,
      advanceEndTime,
      activityStartTime,
      activityEndTime,
      status,
      hasLesson,
      nodeRewardVoice,
      postIcon,
      themeRes,
      const DeepCollectionEquality().hash(_taskVo),
      madelGetPopup);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesVoCopyWith<_$_ClassActivitiesVo> get copyWith =>
      __$$_ClassActivitiesVoCopyWithImpl<_$_ClassActivitiesVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesVo implements ClassActivitiesVo {
  const factory _ClassActivitiesVo(
      {final int? activityId,
      final int? advanceStartTime,
      final int? advanceEndTime,
      final int? activityStartTime,
      final int? activityEndTime,
      final int? status,
      final int? hasLesson,
      final String? nodeRewardVoice,
      final String? postIcon,
      final ClassActivitiesThemeResVo? themeRes,
      final List<ClassActivitiesTaskVo?>? taskVo,
      final ClassActivitiesPopupVo? madelGetPopup}) = _$_ClassActivitiesVo;

  factory _ClassActivitiesVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesVo.fromJson;

  @override
  int? get activityId;
  @override
  int? get advanceStartTime;
  @override
  int? get advanceEndTime;
  @override
  int? get activityStartTime;
  @override
  int? get activityEndTime;
  @override
  int? get status;
  @override
  int? get hasLesson;
  @override
  String? get nodeRewardVoice;
  @override
  String? get postIcon;
  @override
  ClassActivitiesThemeResVo? get themeRes;
  @override
  List<ClassActivitiesTaskVo?>? get taskVo;
  @override
  ClassActivitiesPopupVo? get madelGetPopup;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesVoCopyWith<_$_ClassActivitiesVo> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassActivitiesThemeResVo _$ClassActivitiesThemeResVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesThemeResVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesThemeResVo {
  ClassActivitiesthemeHeadVo? get themeHead =>
      throw _privateConstructorUsedError;
  ClassActivitiesThemeActivityCardVo? get themeActivityCard =>
      throw _privateConstructorUsedError;
  ClassActivitiesPopupVo? get themeActivityPopup =>
      throw _privateConstructorUsedError;
  ClassActivitiesThemeLessonCardVo? get themeLessonCard =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesThemeResVoCopyWith<ClassActivitiesThemeResVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesThemeResVoCopyWith<$Res> {
  factory $ClassActivitiesThemeResVoCopyWith(ClassActivitiesThemeResVo value,
          $Res Function(ClassActivitiesThemeResVo) then) =
      _$ClassActivitiesThemeResVoCopyWithImpl<$Res, ClassActivitiesThemeResVo>;
  @useResult
  $Res call(
      {ClassActivitiesthemeHeadVo? themeHead,
      ClassActivitiesThemeActivityCardVo? themeActivityCard,
      ClassActivitiesPopupVo? themeActivityPopup,
      ClassActivitiesThemeLessonCardVo? themeLessonCard});

  $ClassActivitiesthemeHeadVoCopyWith<$Res>? get themeHead;
  $ClassActivitiesThemeActivityCardVoCopyWith<$Res>? get themeActivityCard;
  $ClassActivitiesPopupVoCopyWith<$Res>? get themeActivityPopup;
  $ClassActivitiesThemeLessonCardVoCopyWith<$Res>? get themeLessonCard;
}

/// @nodoc
class _$ClassActivitiesThemeResVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesThemeResVo>
    implements $ClassActivitiesThemeResVoCopyWith<$Res> {
  _$ClassActivitiesThemeResVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? themeHead = freezed,
    Object? themeActivityCard = freezed,
    Object? themeActivityPopup = freezed,
    Object? themeLessonCard = freezed,
  }) {
    return _then(_value.copyWith(
      themeHead: freezed == themeHead
          ? _value.themeHead
          : themeHead // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesthemeHeadVo?,
      themeActivityCard: freezed == themeActivityCard
          ? _value.themeActivityCard
          : themeActivityCard // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesThemeActivityCardVo?,
      themeActivityPopup: freezed == themeActivityPopup
          ? _value.themeActivityPopup
          : themeActivityPopup // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesPopupVo?,
      themeLessonCard: freezed == themeLessonCard
          ? _value.themeLessonCard
          : themeLessonCard // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesThemeLessonCardVo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesthemeHeadVoCopyWith<$Res>? get themeHead {
    if (_value.themeHead == null) {
      return null;
    }

    return $ClassActivitiesthemeHeadVoCopyWith<$Res>(_value.themeHead!,
        (value) {
      return _then(_value.copyWith(themeHead: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesThemeActivityCardVoCopyWith<$Res>? get themeActivityCard {
    if (_value.themeActivityCard == null) {
      return null;
    }

    return $ClassActivitiesThemeActivityCardVoCopyWith<$Res>(
        _value.themeActivityCard!, (value) {
      return _then(_value.copyWith(themeActivityCard: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesPopupVoCopyWith<$Res>? get themeActivityPopup {
    if (_value.themeActivityPopup == null) {
      return null;
    }

    return $ClassActivitiesPopupVoCopyWith<$Res>(_value.themeActivityPopup!,
        (value) {
      return _then(_value.copyWith(themeActivityPopup: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesThemeLessonCardVoCopyWith<$Res>? get themeLessonCard {
    if (_value.themeLessonCard == null) {
      return null;
    }

    return $ClassActivitiesThemeLessonCardVoCopyWith<$Res>(
        _value.themeLessonCard!, (value) {
      return _then(_value.copyWith(themeLessonCard: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesThemeResVoCopyWith<$Res>
    implements $ClassActivitiesThemeResVoCopyWith<$Res> {
  factory _$$_ClassActivitiesThemeResVoCopyWith(
          _$_ClassActivitiesThemeResVo value,
          $Res Function(_$_ClassActivitiesThemeResVo) then) =
      __$$_ClassActivitiesThemeResVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ClassActivitiesthemeHeadVo? themeHead,
      ClassActivitiesThemeActivityCardVo? themeActivityCard,
      ClassActivitiesPopupVo? themeActivityPopup,
      ClassActivitiesThemeLessonCardVo? themeLessonCard});

  @override
  $ClassActivitiesthemeHeadVoCopyWith<$Res>? get themeHead;
  @override
  $ClassActivitiesThemeActivityCardVoCopyWith<$Res>? get themeActivityCard;
  @override
  $ClassActivitiesPopupVoCopyWith<$Res>? get themeActivityPopup;
  @override
  $ClassActivitiesThemeLessonCardVoCopyWith<$Res>? get themeLessonCard;
}

/// @nodoc
class __$$_ClassActivitiesThemeResVoCopyWithImpl<$Res>
    extends _$ClassActivitiesThemeResVoCopyWithImpl<$Res,
        _$_ClassActivitiesThemeResVo>
    implements _$$_ClassActivitiesThemeResVoCopyWith<$Res> {
  __$$_ClassActivitiesThemeResVoCopyWithImpl(
      _$_ClassActivitiesThemeResVo _value,
      $Res Function(_$_ClassActivitiesThemeResVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? themeHead = freezed,
    Object? themeActivityCard = freezed,
    Object? themeActivityPopup = freezed,
    Object? themeLessonCard = freezed,
  }) {
    return _then(_$_ClassActivitiesThemeResVo(
      themeHead: freezed == themeHead
          ? _value.themeHead
          : themeHead // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesthemeHeadVo?,
      themeActivityCard: freezed == themeActivityCard
          ? _value.themeActivityCard
          : themeActivityCard // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesThemeActivityCardVo?,
      themeActivityPopup: freezed == themeActivityPopup
          ? _value.themeActivityPopup
          : themeActivityPopup // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesPopupVo?,
      themeLessonCard: freezed == themeLessonCard
          ? _value.themeLessonCard
          : themeLessonCard // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesThemeLessonCardVo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesThemeResVo implements _ClassActivitiesThemeResVo {
  const _$_ClassActivitiesThemeResVo(
      {this.themeHead,
      this.themeActivityCard,
      this.themeActivityPopup,
      this.themeLessonCard});

  factory _$_ClassActivitiesThemeResVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesThemeResVoFromJson(json);

  @override
  final ClassActivitiesthemeHeadVo? themeHead;
  @override
  final ClassActivitiesThemeActivityCardVo? themeActivityCard;
  @override
  final ClassActivitiesPopupVo? themeActivityPopup;
  @override
  final ClassActivitiesThemeLessonCardVo? themeLessonCard;

  @override
  String toString() {
    return 'ClassActivitiesThemeResVo(themeHead: $themeHead, themeActivityCard: $themeActivityCard, themeActivityPopup: $themeActivityPopup, themeLessonCard: $themeLessonCard)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesThemeResVo &&
            (identical(other.themeHead, themeHead) ||
                other.themeHead == themeHead) &&
            (identical(other.themeActivityCard, themeActivityCard) ||
                other.themeActivityCard == themeActivityCard) &&
            (identical(other.themeActivityPopup, themeActivityPopup) ||
                other.themeActivityPopup == themeActivityPopup) &&
            (identical(other.themeLessonCard, themeLessonCard) ||
                other.themeLessonCard == themeLessonCard));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, themeHead, themeActivityCard,
      themeActivityPopup, themeLessonCard);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesThemeResVoCopyWith<_$_ClassActivitiesThemeResVo>
      get copyWith => __$$_ClassActivitiesThemeResVoCopyWithImpl<
          _$_ClassActivitiesThemeResVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesThemeResVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesThemeResVo implements ClassActivitiesThemeResVo {
  const factory _ClassActivitiesThemeResVo(
          {final ClassActivitiesthemeHeadVo? themeHead,
          final ClassActivitiesThemeActivityCardVo? themeActivityCard,
          final ClassActivitiesPopupVo? themeActivityPopup,
          final ClassActivitiesThemeLessonCardVo? themeLessonCard}) =
      _$_ClassActivitiesThemeResVo;

  factory _ClassActivitiesThemeResVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesThemeResVo.fromJson;

  @override
  ClassActivitiesthemeHeadVo? get themeHead;
  @override
  ClassActivitiesThemeActivityCardVo? get themeActivityCard;
  @override
  ClassActivitiesPopupVo? get themeActivityPopup;
  @override
  ClassActivitiesThemeLessonCardVo? get themeLessonCard;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesThemeResVoCopyWith<_$_ClassActivitiesThemeResVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesPopupVo _$ClassActivitiesPopupVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesPopupVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesPopupVo {
  String? get mainTitle => throw _privateConstructorUsedError;
  String? get subTitle => throw _privateConstructorUsedError;
  String? get headImg => throw _privateConstructorUsedError;
  String? get btnTitle => throw _privateConstructorUsedError;
  String? get guidingVideo => throw _privateConstructorUsedError;
  String? get guidingAudio => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesPopupVoCopyWith<ClassActivitiesPopupVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesPopupVoCopyWith<$Res> {
  factory $ClassActivitiesPopupVoCopyWith(ClassActivitiesPopupVo value,
          $Res Function(ClassActivitiesPopupVo) then) =
      _$ClassActivitiesPopupVoCopyWithImpl<$Res, ClassActivitiesPopupVo>;
  @useResult
  $Res call(
      {String? mainTitle,
      String? subTitle,
      String? headImg,
      String? btnTitle,
      String? guidingVideo,
      String? guidingAudio});
}

/// @nodoc
class _$ClassActivitiesPopupVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesPopupVo>
    implements $ClassActivitiesPopupVoCopyWith<$Res> {
  _$ClassActivitiesPopupVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mainTitle = freezed,
    Object? subTitle = freezed,
    Object? headImg = freezed,
    Object? btnTitle = freezed,
    Object? guidingVideo = freezed,
    Object? guidingAudio = freezed,
  }) {
    return _then(_value.copyWith(
      mainTitle: freezed == mainTitle
          ? _value.mainTitle
          : mainTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      headImg: freezed == headImg
          ? _value.headImg
          : headImg // ignore: cast_nullable_to_non_nullable
              as String?,
      btnTitle: freezed == btnTitle
          ? _value.btnTitle
          : btnTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      guidingVideo: freezed == guidingVideo
          ? _value.guidingVideo
          : guidingVideo // ignore: cast_nullable_to_non_nullable
              as String?,
      guidingAudio: freezed == guidingAudio
          ? _value.guidingAudio
          : guidingAudio // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesPopupVoCopyWith<$Res>
    implements $ClassActivitiesPopupVoCopyWith<$Res> {
  factory _$$_ClassActivitiesPopupVoCopyWith(_$_ClassActivitiesPopupVo value,
          $Res Function(_$_ClassActivitiesPopupVo) then) =
      __$$_ClassActivitiesPopupVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? mainTitle,
      String? subTitle,
      String? headImg,
      String? btnTitle,
      String? guidingVideo,
      String? guidingAudio});
}

/// @nodoc
class __$$_ClassActivitiesPopupVoCopyWithImpl<$Res>
    extends _$ClassActivitiesPopupVoCopyWithImpl<$Res,
        _$_ClassActivitiesPopupVo>
    implements _$$_ClassActivitiesPopupVoCopyWith<$Res> {
  __$$_ClassActivitiesPopupVoCopyWithImpl(_$_ClassActivitiesPopupVo _value,
      $Res Function(_$_ClassActivitiesPopupVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mainTitle = freezed,
    Object? subTitle = freezed,
    Object? headImg = freezed,
    Object? btnTitle = freezed,
    Object? guidingVideo = freezed,
    Object? guidingAudio = freezed,
  }) {
    return _then(_$_ClassActivitiesPopupVo(
      mainTitle: freezed == mainTitle
          ? _value.mainTitle
          : mainTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      headImg: freezed == headImg
          ? _value.headImg
          : headImg // ignore: cast_nullable_to_non_nullable
              as String?,
      btnTitle: freezed == btnTitle
          ? _value.btnTitle
          : btnTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      guidingVideo: freezed == guidingVideo
          ? _value.guidingVideo
          : guidingVideo // ignore: cast_nullable_to_non_nullable
              as String?,
      guidingAudio: freezed == guidingAudio
          ? _value.guidingAudio
          : guidingAudio // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesPopupVo implements _ClassActivitiesPopupVo {
  const _$_ClassActivitiesPopupVo(
      {this.mainTitle,
      this.subTitle,
      this.headImg,
      this.btnTitle,
      this.guidingVideo,
      this.guidingAudio});

  factory _$_ClassActivitiesPopupVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesPopupVoFromJson(json);

  @override
  final String? mainTitle;
  @override
  final String? subTitle;
  @override
  final String? headImg;
  @override
  final String? btnTitle;
  @override
  final String? guidingVideo;
  @override
  final String? guidingAudio;

  @override
  String toString() {
    return 'ClassActivitiesPopupVo(mainTitle: $mainTitle, subTitle: $subTitle, headImg: $headImg, btnTitle: $btnTitle, guidingVideo: $guidingVideo, guidingAudio: $guidingAudio)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesPopupVo &&
            (identical(other.mainTitle, mainTitle) ||
                other.mainTitle == mainTitle) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.headImg, headImg) || other.headImg == headImg) &&
            (identical(other.btnTitle, btnTitle) ||
                other.btnTitle == btnTitle) &&
            (identical(other.guidingVideo, guidingVideo) ||
                other.guidingVideo == guidingVideo) &&
            (identical(other.guidingAudio, guidingAudio) ||
                other.guidingAudio == guidingAudio));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, mainTitle, subTitle, headImg,
      btnTitle, guidingVideo, guidingAudio);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesPopupVoCopyWith<_$_ClassActivitiesPopupVo> get copyWith =>
      __$$_ClassActivitiesPopupVoCopyWithImpl<_$_ClassActivitiesPopupVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesPopupVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesPopupVo implements ClassActivitiesPopupVo {
  const factory _ClassActivitiesPopupVo(
      {final String? mainTitle,
      final String? subTitle,
      final String? headImg,
      final String? btnTitle,
      final String? guidingVideo,
      final String? guidingAudio}) = _$_ClassActivitiesPopupVo;

  factory _ClassActivitiesPopupVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesPopupVo.fromJson;

  @override
  String? get mainTitle;
  @override
  String? get subTitle;
  @override
  String? get headImg;
  @override
  String? get btnTitle;
  @override
  String? get guidingVideo;
  @override
  String? get guidingAudio;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesPopupVoCopyWith<_$_ClassActivitiesPopupVo> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassActivitiesthemeHeadVo _$ClassActivitiesthemeHeadVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesthemeHeadVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesthemeHeadVo {
  String? get phoneBackgroundImg => throw _privateConstructorUsedError;
  String? get padBackgroundImg => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesthemeHeadVoCopyWith<ClassActivitiesthemeHeadVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesthemeHeadVoCopyWith<$Res> {
  factory $ClassActivitiesthemeHeadVoCopyWith(ClassActivitiesthemeHeadVo value,
          $Res Function(ClassActivitiesthemeHeadVo) then) =
      _$ClassActivitiesthemeHeadVoCopyWithImpl<$Res,
          ClassActivitiesthemeHeadVo>;
  @useResult
  $Res call({String? phoneBackgroundImg, String? padBackgroundImg});
}

/// @nodoc
class _$ClassActivitiesthemeHeadVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesthemeHeadVo>
    implements $ClassActivitiesthemeHeadVoCopyWith<$Res> {
  _$ClassActivitiesthemeHeadVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneBackgroundImg = freezed,
    Object? padBackgroundImg = freezed,
  }) {
    return _then(_value.copyWith(
      phoneBackgroundImg: freezed == phoneBackgroundImg
          ? _value.phoneBackgroundImg
          : phoneBackgroundImg // ignore: cast_nullable_to_non_nullable
              as String?,
      padBackgroundImg: freezed == padBackgroundImg
          ? _value.padBackgroundImg
          : padBackgroundImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesthemeHeadVoCopyWith<$Res>
    implements $ClassActivitiesthemeHeadVoCopyWith<$Res> {
  factory _$$_ClassActivitiesthemeHeadVoCopyWith(
          _$_ClassActivitiesthemeHeadVo value,
          $Res Function(_$_ClassActivitiesthemeHeadVo) then) =
      __$$_ClassActivitiesthemeHeadVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? phoneBackgroundImg, String? padBackgroundImg});
}

/// @nodoc
class __$$_ClassActivitiesthemeHeadVoCopyWithImpl<$Res>
    extends _$ClassActivitiesthemeHeadVoCopyWithImpl<$Res,
        _$_ClassActivitiesthemeHeadVo>
    implements _$$_ClassActivitiesthemeHeadVoCopyWith<$Res> {
  __$$_ClassActivitiesthemeHeadVoCopyWithImpl(
      _$_ClassActivitiesthemeHeadVo _value,
      $Res Function(_$_ClassActivitiesthemeHeadVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneBackgroundImg = freezed,
    Object? padBackgroundImg = freezed,
  }) {
    return _then(_$_ClassActivitiesthemeHeadVo(
      phoneBackgroundImg: freezed == phoneBackgroundImg
          ? _value.phoneBackgroundImg
          : phoneBackgroundImg // ignore: cast_nullable_to_non_nullable
              as String?,
      padBackgroundImg: freezed == padBackgroundImg
          ? _value.padBackgroundImg
          : padBackgroundImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesthemeHeadVo implements _ClassActivitiesthemeHeadVo {
  const _$_ClassActivitiesthemeHeadVo(
      {this.phoneBackgroundImg, this.padBackgroundImg});

  factory _$_ClassActivitiesthemeHeadVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesthemeHeadVoFromJson(json);

  @override
  final String? phoneBackgroundImg;
  @override
  final String? padBackgroundImg;

  @override
  String toString() {
    return 'ClassActivitiesthemeHeadVo(phoneBackgroundImg: $phoneBackgroundImg, padBackgroundImg: $padBackgroundImg)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesthemeHeadVo &&
            (identical(other.phoneBackgroundImg, phoneBackgroundImg) ||
                other.phoneBackgroundImg == phoneBackgroundImg) &&
            (identical(other.padBackgroundImg, padBackgroundImg) ||
                other.padBackgroundImg == padBackgroundImg));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, phoneBackgroundImg, padBackgroundImg);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesthemeHeadVoCopyWith<_$_ClassActivitiesthemeHeadVo>
      get copyWith => __$$_ClassActivitiesthemeHeadVoCopyWithImpl<
          _$_ClassActivitiesthemeHeadVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesthemeHeadVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesthemeHeadVo
    implements ClassActivitiesthemeHeadVo {
  const factory _ClassActivitiesthemeHeadVo(
      {final String? phoneBackgroundImg,
      final String? padBackgroundImg}) = _$_ClassActivitiesthemeHeadVo;

  factory _ClassActivitiesthemeHeadVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesthemeHeadVo.fromJson;

  @override
  String? get phoneBackgroundImg;
  @override
  String? get padBackgroundImg;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesthemeHeadVoCopyWith<_$_ClassActivitiesthemeHeadVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesThemeLessonCardVo _$ClassActivitiesThemeLessonCardVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesThemeLessonCardVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesThemeLessonCardVo {
  String? get collectRes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesThemeLessonCardVoCopyWith<ClassActivitiesThemeLessonCardVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesThemeLessonCardVoCopyWith<$Res> {
  factory $ClassActivitiesThemeLessonCardVoCopyWith(
          ClassActivitiesThemeLessonCardVo value,
          $Res Function(ClassActivitiesThemeLessonCardVo) then) =
      _$ClassActivitiesThemeLessonCardVoCopyWithImpl<$Res,
          ClassActivitiesThemeLessonCardVo>;
  @useResult
  $Res call({String? collectRes});
}

/// @nodoc
class _$ClassActivitiesThemeLessonCardVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesThemeLessonCardVo>
    implements $ClassActivitiesThemeLessonCardVoCopyWith<$Res> {
  _$ClassActivitiesThemeLessonCardVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? collectRes = freezed,
  }) {
    return _then(_value.copyWith(
      collectRes: freezed == collectRes
          ? _value.collectRes
          : collectRes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesThemeLessonCardVoCopyWith<$Res>
    implements $ClassActivitiesThemeLessonCardVoCopyWith<$Res> {
  factory _$$_ClassActivitiesThemeLessonCardVoCopyWith(
          _$_ClassActivitiesThemeLessonCardVo value,
          $Res Function(_$_ClassActivitiesThemeLessonCardVo) then) =
      __$$_ClassActivitiesThemeLessonCardVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? collectRes});
}

/// @nodoc
class __$$_ClassActivitiesThemeLessonCardVoCopyWithImpl<$Res>
    extends _$ClassActivitiesThemeLessonCardVoCopyWithImpl<$Res,
        _$_ClassActivitiesThemeLessonCardVo>
    implements _$$_ClassActivitiesThemeLessonCardVoCopyWith<$Res> {
  __$$_ClassActivitiesThemeLessonCardVoCopyWithImpl(
      _$_ClassActivitiesThemeLessonCardVo _value,
      $Res Function(_$_ClassActivitiesThemeLessonCardVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? collectRes = freezed,
  }) {
    return _then(_$_ClassActivitiesThemeLessonCardVo(
      collectRes: freezed == collectRes
          ? _value.collectRes
          : collectRes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesThemeLessonCardVo
    implements _ClassActivitiesThemeLessonCardVo {
  const _$_ClassActivitiesThemeLessonCardVo({this.collectRes});

  factory _$_ClassActivitiesThemeLessonCardVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_ClassActivitiesThemeLessonCardVoFromJson(json);

  @override
  final String? collectRes;

  @override
  String toString() {
    return 'ClassActivitiesThemeLessonCardVo(collectRes: $collectRes)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesThemeLessonCardVo &&
            (identical(other.collectRes, collectRes) ||
                other.collectRes == collectRes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, collectRes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesThemeLessonCardVoCopyWith<
          _$_ClassActivitiesThemeLessonCardVo>
      get copyWith => __$$_ClassActivitiesThemeLessonCardVoCopyWithImpl<
          _$_ClassActivitiesThemeLessonCardVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesThemeLessonCardVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesThemeLessonCardVo
    implements ClassActivitiesThemeLessonCardVo {
  const factory _ClassActivitiesThemeLessonCardVo({final String? collectRes}) =
      _$_ClassActivitiesThemeLessonCardVo;

  factory _ClassActivitiesThemeLessonCardVo.fromJson(
      Map<String, dynamic> json) = _$_ClassActivitiesThemeLessonCardVo.fromJson;

  @override
  String? get collectRes;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesThemeLessonCardVoCopyWith<
          _$_ClassActivitiesThemeLessonCardVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesThemeActivityCardVo _$ClassActivitiesThemeActivityCardVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesThemeActivityCardVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesThemeActivityCardVo {
  String? get icon => throw _privateConstructorUsedError;
  String? get progressRes => throw _privateConstructorUsedError;
  String? get backgroundRes => throw _privateConstructorUsedError;
  String? get progressHeadColor => throw _privateConstructorUsedError;
  String? get progressTailColor => throw _privateConstructorUsedError;
  String? get advanceText => throw _privateConstructorUsedError;
  String? get advancePage => throw _privateConstructorUsedError;
  String? get activityText => throw _privateConstructorUsedError;
  String? get activityPage => throw _privateConstructorUsedError;
  int? get advanceRedirectPageId => throw _privateConstructorUsedError;
  int? get activityRedirectPageId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesThemeActivityCardVoCopyWith<
          ClassActivitiesThemeActivityCardVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesThemeActivityCardVoCopyWith<$Res> {
  factory $ClassActivitiesThemeActivityCardVoCopyWith(
          ClassActivitiesThemeActivityCardVo value,
          $Res Function(ClassActivitiesThemeActivityCardVo) then) =
      _$ClassActivitiesThemeActivityCardVoCopyWithImpl<$Res,
          ClassActivitiesThemeActivityCardVo>;
  @useResult
  $Res call(
      {String? icon,
      String? progressRes,
      String? backgroundRes,
      String? progressHeadColor,
      String? progressTailColor,
      String? advanceText,
      String? advancePage,
      String? activityText,
      String? activityPage,
      int? advanceRedirectPageId,
      int? activityRedirectPageId});
}

/// @nodoc
class _$ClassActivitiesThemeActivityCardVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesThemeActivityCardVo>
    implements $ClassActivitiesThemeActivityCardVoCopyWith<$Res> {
  _$ClassActivitiesThemeActivityCardVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? progressRes = freezed,
    Object? backgroundRes = freezed,
    Object? progressHeadColor = freezed,
    Object? progressTailColor = freezed,
    Object? advanceText = freezed,
    Object? advancePage = freezed,
    Object? activityText = freezed,
    Object? activityPage = freezed,
    Object? advanceRedirectPageId = freezed,
    Object? activityRedirectPageId = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      progressRes: freezed == progressRes
          ? _value.progressRes
          : progressRes // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundRes: freezed == backgroundRes
          ? _value.backgroundRes
          : backgroundRes // ignore: cast_nullable_to_non_nullable
              as String?,
      progressHeadColor: freezed == progressHeadColor
          ? _value.progressHeadColor
          : progressHeadColor // ignore: cast_nullable_to_non_nullable
              as String?,
      progressTailColor: freezed == progressTailColor
          ? _value.progressTailColor
          : progressTailColor // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceText: freezed == advanceText
          ? _value.advanceText
          : advanceText // ignore: cast_nullable_to_non_nullable
              as String?,
      advancePage: freezed == advancePage
          ? _value.advancePage
          : advancePage // ignore: cast_nullable_to_non_nullable
              as String?,
      activityText: freezed == activityText
          ? _value.activityText
          : activityText // ignore: cast_nullable_to_non_nullable
              as String?,
      activityPage: freezed == activityPage
          ? _value.activityPage
          : activityPage // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceRedirectPageId: freezed == advanceRedirectPageId
          ? _value.advanceRedirectPageId
          : advanceRedirectPageId // ignore: cast_nullable_to_non_nullable
              as int?,
      activityRedirectPageId: freezed == activityRedirectPageId
          ? _value.activityRedirectPageId
          : activityRedirectPageId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesThemeActivityCardVoCopyWith<$Res>
    implements $ClassActivitiesThemeActivityCardVoCopyWith<$Res> {
  factory _$$_ClassActivitiesThemeActivityCardVoCopyWith(
          _$_ClassActivitiesThemeActivityCardVo value,
          $Res Function(_$_ClassActivitiesThemeActivityCardVo) then) =
      __$$_ClassActivitiesThemeActivityCardVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? icon,
      String? progressRes,
      String? backgroundRes,
      String? progressHeadColor,
      String? progressTailColor,
      String? advanceText,
      String? advancePage,
      String? activityText,
      String? activityPage,
      int? advanceRedirectPageId,
      int? activityRedirectPageId});
}

/// @nodoc
class __$$_ClassActivitiesThemeActivityCardVoCopyWithImpl<$Res>
    extends _$ClassActivitiesThemeActivityCardVoCopyWithImpl<$Res,
        _$_ClassActivitiesThemeActivityCardVo>
    implements _$$_ClassActivitiesThemeActivityCardVoCopyWith<$Res> {
  __$$_ClassActivitiesThemeActivityCardVoCopyWithImpl(
      _$_ClassActivitiesThemeActivityCardVo _value,
      $Res Function(_$_ClassActivitiesThemeActivityCardVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? progressRes = freezed,
    Object? backgroundRes = freezed,
    Object? progressHeadColor = freezed,
    Object? progressTailColor = freezed,
    Object? advanceText = freezed,
    Object? advancePage = freezed,
    Object? activityText = freezed,
    Object? activityPage = freezed,
    Object? advanceRedirectPageId = freezed,
    Object? activityRedirectPageId = freezed,
  }) {
    return _then(_$_ClassActivitiesThemeActivityCardVo(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      progressRes: freezed == progressRes
          ? _value.progressRes
          : progressRes // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundRes: freezed == backgroundRes
          ? _value.backgroundRes
          : backgroundRes // ignore: cast_nullable_to_non_nullable
              as String?,
      progressHeadColor: freezed == progressHeadColor
          ? _value.progressHeadColor
          : progressHeadColor // ignore: cast_nullable_to_non_nullable
              as String?,
      progressTailColor: freezed == progressTailColor
          ? _value.progressTailColor
          : progressTailColor // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceText: freezed == advanceText
          ? _value.advanceText
          : advanceText // ignore: cast_nullable_to_non_nullable
              as String?,
      advancePage: freezed == advancePage
          ? _value.advancePage
          : advancePage // ignore: cast_nullable_to_non_nullable
              as String?,
      activityText: freezed == activityText
          ? _value.activityText
          : activityText // ignore: cast_nullable_to_non_nullable
              as String?,
      activityPage: freezed == activityPage
          ? _value.activityPage
          : activityPage // ignore: cast_nullable_to_non_nullable
              as String?,
      advanceRedirectPageId: freezed == advanceRedirectPageId
          ? _value.advanceRedirectPageId
          : advanceRedirectPageId // ignore: cast_nullable_to_non_nullable
              as int?,
      activityRedirectPageId: freezed == activityRedirectPageId
          ? _value.activityRedirectPageId
          : activityRedirectPageId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesThemeActivityCardVo
    implements _ClassActivitiesThemeActivityCardVo {
  const _$_ClassActivitiesThemeActivityCardVo(
      {this.icon,
      this.progressRes,
      this.backgroundRes,
      this.progressHeadColor,
      this.progressTailColor,
      this.advanceText,
      this.advancePage,
      this.activityText,
      this.activityPage,
      this.advanceRedirectPageId,
      this.activityRedirectPageId});

  factory _$_ClassActivitiesThemeActivityCardVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_ClassActivitiesThemeActivityCardVoFromJson(json);

  @override
  final String? icon;
  @override
  final String? progressRes;
  @override
  final String? backgroundRes;
  @override
  final String? progressHeadColor;
  @override
  final String? progressTailColor;
  @override
  final String? advanceText;
  @override
  final String? advancePage;
  @override
  final String? activityText;
  @override
  final String? activityPage;
  @override
  final int? advanceRedirectPageId;
  @override
  final int? activityRedirectPageId;

  @override
  String toString() {
    return 'ClassActivitiesThemeActivityCardVo(icon: $icon, progressRes: $progressRes, backgroundRes: $backgroundRes, progressHeadColor: $progressHeadColor, progressTailColor: $progressTailColor, advanceText: $advanceText, advancePage: $advancePage, activityText: $activityText, activityPage: $activityPage, advanceRedirectPageId: $advanceRedirectPageId, activityRedirectPageId: $activityRedirectPageId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesThemeActivityCardVo &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.progressRes, progressRes) ||
                other.progressRes == progressRes) &&
            (identical(other.backgroundRes, backgroundRes) ||
                other.backgroundRes == backgroundRes) &&
            (identical(other.progressHeadColor, progressHeadColor) ||
                other.progressHeadColor == progressHeadColor) &&
            (identical(other.progressTailColor, progressTailColor) ||
                other.progressTailColor == progressTailColor) &&
            (identical(other.advanceText, advanceText) ||
                other.advanceText == advanceText) &&
            (identical(other.advancePage, advancePage) ||
                other.advancePage == advancePage) &&
            (identical(other.activityText, activityText) ||
                other.activityText == activityText) &&
            (identical(other.activityPage, activityPage) ||
                other.activityPage == activityPage) &&
            (identical(other.advanceRedirectPageId, advanceRedirectPageId) ||
                other.advanceRedirectPageId == advanceRedirectPageId) &&
            (identical(other.activityRedirectPageId, activityRedirectPageId) ||
                other.activityRedirectPageId == activityRedirectPageId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      icon,
      progressRes,
      backgroundRes,
      progressHeadColor,
      progressTailColor,
      advanceText,
      advancePage,
      activityText,
      activityPage,
      advanceRedirectPageId,
      activityRedirectPageId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesThemeActivityCardVoCopyWith<
          _$_ClassActivitiesThemeActivityCardVo>
      get copyWith => __$$_ClassActivitiesThemeActivityCardVoCopyWithImpl<
          _$_ClassActivitiesThemeActivityCardVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesThemeActivityCardVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesThemeActivityCardVo
    implements ClassActivitiesThemeActivityCardVo {
  const factory _ClassActivitiesThemeActivityCardVo(
          {final String? icon,
          final String? progressRes,
          final String? backgroundRes,
          final String? progressHeadColor,
          final String? progressTailColor,
          final String? advanceText,
          final String? advancePage,
          final String? activityText,
          final String? activityPage,
          final int? advanceRedirectPageId,
          final int? activityRedirectPageId}) =
      _$_ClassActivitiesThemeActivityCardVo;

  factory _ClassActivitiesThemeActivityCardVo.fromJson(
          Map<String, dynamic> json) =
      _$_ClassActivitiesThemeActivityCardVo.fromJson;

  @override
  String? get icon;
  @override
  String? get progressRes;
  @override
  String? get backgroundRes;
  @override
  String? get progressHeadColor;
  @override
  String? get progressTailColor;
  @override
  String? get advanceText;
  @override
  String? get advancePage;
  @override
  String? get activityText;
  @override
  String? get activityPage;
  @override
  int? get advanceRedirectPageId;
  @override
  int? get activityRedirectPageId;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesThemeActivityCardVoCopyWith<
          _$_ClassActivitiesThemeActivityCardVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesTaskVo _$ClassActivitiesTaskVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesTaskVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskVo {
  int? get taskId => throw _privateConstructorUsedError;
  int? get finishTime => throw _privateConstructorUsedError;
  String? get taskType => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  List<ClassActivitiesTaskConditionsVo?>? get conditions =>
      throw _privateConstructorUsedError;
  List<ClassActivitiesTaskRewardsVo?>? get rewards =>
      throw _privateConstructorUsedError;
  ClassActivitiesTaskExtendResourceVo? get taskExtendResource =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskVoCopyWith<ClassActivitiesTaskVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskVoCopyWith<$Res> {
  factory $ClassActivitiesTaskVoCopyWith(ClassActivitiesTaskVo value,
          $Res Function(ClassActivitiesTaskVo) then) =
      _$ClassActivitiesTaskVoCopyWithImpl<$Res, ClassActivitiesTaskVo>;
  @useResult
  $Res call(
      {int? taskId,
      int? finishTime,
      String? taskType,
      String? name,
      List<ClassActivitiesTaskConditionsVo?>? conditions,
      List<ClassActivitiesTaskRewardsVo?>? rewards,
      ClassActivitiesTaskExtendResourceVo? taskExtendResource});

  $ClassActivitiesTaskExtendResourceVoCopyWith<$Res>? get taskExtendResource;
}

/// @nodoc
class _$ClassActivitiesTaskVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskVo>
    implements $ClassActivitiesTaskVoCopyWith<$Res> {
  _$ClassActivitiesTaskVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? finishTime = freezed,
    Object? taskType = freezed,
    Object? name = freezed,
    Object? conditions = freezed,
    Object? rewards = freezed,
    Object? taskExtendResource = freezed,
  }) {
    return _then(_value.copyWith(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as int?,
      finishTime: freezed == finishTime
          ? _value.finishTime
          : finishTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      conditions: freezed == conditions
          ? _value.conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskConditionsVo?>?,
      rewards: freezed == rewards
          ? _value.rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskRewardsVo?>?,
      taskExtendResource: freezed == taskExtendResource
          ? _value.taskExtendResource
          : taskExtendResource // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesTaskExtendResourceVo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesTaskExtendResourceVoCopyWith<$Res>? get taskExtendResource {
    if (_value.taskExtendResource == null) {
      return null;
    }

    return $ClassActivitiesTaskExtendResourceVoCopyWith<$Res>(
        _value.taskExtendResource!, (value) {
      return _then(_value.copyWith(taskExtendResource: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskVoCopyWith<$Res>
    implements $ClassActivitiesTaskVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskVoCopyWith(_$_ClassActivitiesTaskVo value,
          $Res Function(_$_ClassActivitiesTaskVo) then) =
      __$$_ClassActivitiesTaskVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? taskId,
      int? finishTime,
      String? taskType,
      String? name,
      List<ClassActivitiesTaskConditionsVo?>? conditions,
      List<ClassActivitiesTaskRewardsVo?>? rewards,
      ClassActivitiesTaskExtendResourceVo? taskExtendResource});

  @override
  $ClassActivitiesTaskExtendResourceVoCopyWith<$Res>? get taskExtendResource;
}

/// @nodoc
class __$$_ClassActivitiesTaskVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskVoCopyWithImpl<$Res, _$_ClassActivitiesTaskVo>
    implements _$$_ClassActivitiesTaskVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskVoCopyWithImpl(_$_ClassActivitiesTaskVo _value,
      $Res Function(_$_ClassActivitiesTaskVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? finishTime = freezed,
    Object? taskType = freezed,
    Object? name = freezed,
    Object? conditions = freezed,
    Object? rewards = freezed,
    Object? taskExtendResource = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskVo(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as int?,
      finishTime: freezed == finishTime
          ? _value.finishTime
          : finishTime // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      conditions: freezed == conditions
          ? _value._conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskConditionsVo?>?,
      rewards: freezed == rewards
          ? _value._rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskRewardsVo?>?,
      taskExtendResource: freezed == taskExtendResource
          ? _value.taskExtendResource
          : taskExtendResource // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesTaskExtendResourceVo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskVo implements _ClassActivitiesTaskVo {
  const _$_ClassActivitiesTaskVo(
      {this.taskId,
      this.finishTime,
      this.taskType,
      this.name,
      final List<ClassActivitiesTaskConditionsVo?>? conditions,
      final List<ClassActivitiesTaskRewardsVo?>? rewards,
      this.taskExtendResource})
      : _conditions = conditions,
        _rewards = rewards;

  factory _$_ClassActivitiesTaskVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskVoFromJson(json);

  @override
  final int? taskId;
  @override
  final int? finishTime;
  @override
  final String? taskType;
  @override
  final String? name;
  final List<ClassActivitiesTaskConditionsVo?>? _conditions;
  @override
  List<ClassActivitiesTaskConditionsVo?>? get conditions {
    final value = _conditions;
    if (value == null) return null;
    if (_conditions is EqualUnmodifiableListView) return _conditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ClassActivitiesTaskRewardsVo?>? _rewards;
  @override
  List<ClassActivitiesTaskRewardsVo?>? get rewards {
    final value = _rewards;
    if (value == null) return null;
    if (_rewards is EqualUnmodifiableListView) return _rewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ClassActivitiesTaskExtendResourceVo? taskExtendResource;

  @override
  String toString() {
    return 'ClassActivitiesTaskVo(taskId: $taskId, finishTime: $finishTime, taskType: $taskType, name: $name, conditions: $conditions, rewards: $rewards, taskExtendResource: $taskExtendResource)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskVo &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.finishTime, finishTime) ||
                other.finishTime == finishTime) &&
            (identical(other.taskType, taskType) ||
                other.taskType == taskType) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality()
                .equals(other._conditions, _conditions) &&
            const DeepCollectionEquality().equals(other._rewards, _rewards) &&
            (identical(other.taskExtendResource, taskExtendResource) ||
                other.taskExtendResource == taskExtendResource));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      taskId,
      finishTime,
      taskType,
      name,
      const DeepCollectionEquality().hash(_conditions),
      const DeepCollectionEquality().hash(_rewards),
      taskExtendResource);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskVoCopyWith<_$_ClassActivitiesTaskVo> get copyWith =>
      __$$_ClassActivitiesTaskVoCopyWithImpl<_$_ClassActivitiesTaskVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskVo implements ClassActivitiesTaskVo {
  const factory _ClassActivitiesTaskVo(
          {final int? taskId,
          final int? finishTime,
          final String? taskType,
          final String? name,
          final List<ClassActivitiesTaskConditionsVo?>? conditions,
          final List<ClassActivitiesTaskRewardsVo?>? rewards,
          final ClassActivitiesTaskExtendResourceVo? taskExtendResource}) =
      _$_ClassActivitiesTaskVo;

  factory _ClassActivitiesTaskVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesTaskVo.fromJson;

  @override
  int? get taskId;
  @override
  int? get finishTime;
  @override
  String? get taskType;
  @override
  String? get name;
  @override
  List<ClassActivitiesTaskConditionsVo?>? get conditions;
  @override
  List<ClassActivitiesTaskRewardsVo?>? get rewards;
  @override
  ClassActivitiesTaskExtendResourceVo? get taskExtendResource;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskVoCopyWith<_$_ClassActivitiesTaskVo> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassActivitiesTaskConditionsVo _$ClassActivitiesTaskConditionsVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesTaskConditionsVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskConditionsVo {
  int? get currentValue => throw _privateConstructorUsedError;
  int? get targetValue => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskConditionsVoCopyWith<ClassActivitiesTaskConditionsVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskConditionsVoCopyWith<$Res> {
  factory $ClassActivitiesTaskConditionsVoCopyWith(
          ClassActivitiesTaskConditionsVo value,
          $Res Function(ClassActivitiesTaskConditionsVo) then) =
      _$ClassActivitiesTaskConditionsVoCopyWithImpl<$Res,
          ClassActivitiesTaskConditionsVo>;
  @useResult
  $Res call({int? currentValue, int? targetValue});
}

/// @nodoc
class _$ClassActivitiesTaskConditionsVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskConditionsVo>
    implements $ClassActivitiesTaskConditionsVoCopyWith<$Res> {
  _$ClassActivitiesTaskConditionsVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentValue = freezed,
    Object? targetValue = freezed,
  }) {
    return _then(_value.copyWith(
      currentValue: freezed == currentValue
          ? _value.currentValue
          : currentValue // ignore: cast_nullable_to_non_nullable
              as int?,
      targetValue: freezed == targetValue
          ? _value.targetValue
          : targetValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskConditionsVoCopyWith<$Res>
    implements $ClassActivitiesTaskConditionsVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskConditionsVoCopyWith(
          _$_ClassActivitiesTaskConditionsVo value,
          $Res Function(_$_ClassActivitiesTaskConditionsVo) then) =
      __$$_ClassActivitiesTaskConditionsVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? currentValue, int? targetValue});
}

/// @nodoc
class __$$_ClassActivitiesTaskConditionsVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskConditionsVoCopyWithImpl<$Res,
        _$_ClassActivitiesTaskConditionsVo>
    implements _$$_ClassActivitiesTaskConditionsVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskConditionsVoCopyWithImpl(
      _$_ClassActivitiesTaskConditionsVo _value,
      $Res Function(_$_ClassActivitiesTaskConditionsVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentValue = freezed,
    Object? targetValue = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskConditionsVo(
      currentValue: freezed == currentValue
          ? _value.currentValue
          : currentValue // ignore: cast_nullable_to_non_nullable
              as int?,
      targetValue: freezed == targetValue
          ? _value.targetValue
          : targetValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskConditionsVo
    implements _ClassActivitiesTaskConditionsVo {
  const _$_ClassActivitiesTaskConditionsVo(
      {this.currentValue, this.targetValue});

  factory _$_ClassActivitiesTaskConditionsVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskConditionsVoFromJson(json);

  @override
  final int? currentValue;
  @override
  final int? targetValue;

  @override
  String toString() {
    return 'ClassActivitiesTaskConditionsVo(currentValue: $currentValue, targetValue: $targetValue)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskConditionsVo &&
            (identical(other.currentValue, currentValue) ||
                other.currentValue == currentValue) &&
            (identical(other.targetValue, targetValue) ||
                other.targetValue == targetValue));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, currentValue, targetValue);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskConditionsVoCopyWith<
          _$_ClassActivitiesTaskConditionsVo>
      get copyWith => __$$_ClassActivitiesTaskConditionsVoCopyWithImpl<
          _$_ClassActivitiesTaskConditionsVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskConditionsVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskConditionsVo
    implements ClassActivitiesTaskConditionsVo {
  const factory _ClassActivitiesTaskConditionsVo(
      {final int? currentValue,
      final int? targetValue}) = _$_ClassActivitiesTaskConditionsVo;

  factory _ClassActivitiesTaskConditionsVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesTaskConditionsVo.fromJson;

  @override
  int? get currentValue;
  @override
  int? get targetValue;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskConditionsVoCopyWith<
          _$_ClassActivitiesTaskConditionsVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesTaskRewardsVo _$ClassActivitiesTaskRewardsVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesTaskRewardsVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskRewardsVo {
  int? get rewardId => throw _privateConstructorUsedError;
  int? get isGet => throw _privateConstructorUsedError;
  int? get isPopup => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  int? get getTime => throw _privateConstructorUsedError;
  String? get lockImage => throw _privateConstructorUsedError;
  String? get unlockImage => throw _privateConstructorUsedError;
  String? get resourceFlutter => throw _privateConstructorUsedError;
  String? get bizId => throw _privateConstructorUsedError;
  String? get rewardBizUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskRewardsVoCopyWith<ClassActivitiesTaskRewardsVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskRewardsVoCopyWith<$Res> {
  factory $ClassActivitiesTaskRewardsVoCopyWith(
          ClassActivitiesTaskRewardsVo value,
          $Res Function(ClassActivitiesTaskRewardsVo) then) =
      _$ClassActivitiesTaskRewardsVoCopyWithImpl<$Res,
          ClassActivitiesTaskRewardsVo>;
  @useResult
  $Res call(
      {int? rewardId,
      int? isGet,
      int? isPopup,
      int? type,
      int? getTime,
      String? lockImage,
      String? unlockImage,
      String? resourceFlutter,
      String? bizId,
      String? rewardBizUrl});
}

/// @nodoc
class _$ClassActivitiesTaskRewardsVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskRewardsVo>
    implements $ClassActivitiesTaskRewardsVoCopyWith<$Res> {
  _$ClassActivitiesTaskRewardsVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardId = freezed,
    Object? isGet = freezed,
    Object? isPopup = freezed,
    Object? type = freezed,
    Object? getTime = freezed,
    Object? lockImage = freezed,
    Object? unlockImage = freezed,
    Object? resourceFlutter = freezed,
    Object? bizId = freezed,
    Object? rewardBizUrl = freezed,
  }) {
    return _then(_value.copyWith(
      rewardId: freezed == rewardId
          ? _value.rewardId
          : rewardId // ignore: cast_nullable_to_non_nullable
              as int?,
      isGet: freezed == isGet
          ? _value.isGet
          : isGet // ignore: cast_nullable_to_non_nullable
              as int?,
      isPopup: freezed == isPopup
          ? _value.isPopup
          : isPopup // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lockImage: freezed == lockImage
          ? _value.lockImage
          : lockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockImage: freezed == unlockImage
          ? _value.unlockImage
          : unlockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceFlutter: freezed == resourceFlutter
          ? _value.resourceFlutter
          : resourceFlutter // ignore: cast_nullable_to_non_nullable
              as String?,
      bizId: freezed == bizId
          ? _value.bizId
          : bizId // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardBizUrl: freezed == rewardBizUrl
          ? _value.rewardBizUrl
          : rewardBizUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskRewardsVoCopyWith<$Res>
    implements $ClassActivitiesTaskRewardsVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskRewardsVoCopyWith(
          _$_ClassActivitiesTaskRewardsVo value,
          $Res Function(_$_ClassActivitiesTaskRewardsVo) then) =
      __$$_ClassActivitiesTaskRewardsVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? rewardId,
      int? isGet,
      int? isPopup,
      int? type,
      int? getTime,
      String? lockImage,
      String? unlockImage,
      String? resourceFlutter,
      String? bizId,
      String? rewardBizUrl});
}

/// @nodoc
class __$$_ClassActivitiesTaskRewardsVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskRewardsVoCopyWithImpl<$Res,
        _$_ClassActivitiesTaskRewardsVo>
    implements _$$_ClassActivitiesTaskRewardsVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskRewardsVoCopyWithImpl(
      _$_ClassActivitiesTaskRewardsVo _value,
      $Res Function(_$_ClassActivitiesTaskRewardsVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardId = freezed,
    Object? isGet = freezed,
    Object? isPopup = freezed,
    Object? type = freezed,
    Object? getTime = freezed,
    Object? lockImage = freezed,
    Object? unlockImage = freezed,
    Object? resourceFlutter = freezed,
    Object? bizId = freezed,
    Object? rewardBizUrl = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskRewardsVo(
      rewardId: freezed == rewardId
          ? _value.rewardId
          : rewardId // ignore: cast_nullable_to_non_nullable
              as int?,
      isGet: freezed == isGet
          ? _value.isGet
          : isGet // ignore: cast_nullable_to_non_nullable
              as int?,
      isPopup: freezed == isPopup
          ? _value.isPopup
          : isPopup // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lockImage: freezed == lockImage
          ? _value.lockImage
          : lockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockImage: freezed == unlockImage
          ? _value.unlockImage
          : unlockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceFlutter: freezed == resourceFlutter
          ? _value.resourceFlutter
          : resourceFlutter // ignore: cast_nullable_to_non_nullable
              as String?,
      bizId: freezed == bizId
          ? _value.bizId
          : bizId // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardBizUrl: freezed == rewardBizUrl
          ? _value.rewardBizUrl
          : rewardBizUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskRewardsVo implements _ClassActivitiesTaskRewardsVo {
  const _$_ClassActivitiesTaskRewardsVo(
      {this.rewardId,
      this.isGet,
      this.isPopup,
      this.type,
      this.getTime,
      this.lockImage,
      this.unlockImage,
      this.resourceFlutter,
      this.bizId,
      this.rewardBizUrl});

  factory _$_ClassActivitiesTaskRewardsVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskRewardsVoFromJson(json);

  @override
  final int? rewardId;
  @override
  final int? isGet;
  @override
  final int? isPopup;
  @override
  final int? type;
  @override
  final int? getTime;
  @override
  final String? lockImage;
  @override
  final String? unlockImage;
  @override
  final String? resourceFlutter;
  @override
  final String? bizId;
  @override
  final String? rewardBizUrl;

  @override
  String toString() {
    return 'ClassActivitiesTaskRewardsVo(rewardId: $rewardId, isGet: $isGet, isPopup: $isPopup, type: $type, getTime: $getTime, lockImage: $lockImage, unlockImage: $unlockImage, resourceFlutter: $resourceFlutter, bizId: $bizId, rewardBizUrl: $rewardBizUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskRewardsVo &&
            (identical(other.rewardId, rewardId) ||
                other.rewardId == rewardId) &&
            (identical(other.isGet, isGet) || other.isGet == isGet) &&
            (identical(other.isPopup, isPopup) || other.isPopup == isPopup) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.getTime, getTime) || other.getTime == getTime) &&
            (identical(other.lockImage, lockImage) ||
                other.lockImage == lockImage) &&
            (identical(other.unlockImage, unlockImage) ||
                other.unlockImage == unlockImage) &&
            (identical(other.resourceFlutter, resourceFlutter) ||
                other.resourceFlutter == resourceFlutter) &&
            (identical(other.bizId, bizId) || other.bizId == bizId) &&
            (identical(other.rewardBizUrl, rewardBizUrl) ||
                other.rewardBizUrl == rewardBizUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, rewardId, isGet, isPopup, type,
      getTime, lockImage, unlockImage, resourceFlutter, bizId, rewardBizUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskRewardsVoCopyWith<_$_ClassActivitiesTaskRewardsVo>
      get copyWith => __$$_ClassActivitiesTaskRewardsVoCopyWithImpl<
          _$_ClassActivitiesTaskRewardsVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskRewardsVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskRewardsVo
    implements ClassActivitiesTaskRewardsVo {
  const factory _ClassActivitiesTaskRewardsVo(
      {final int? rewardId,
      final int? isGet,
      final int? isPopup,
      final int? type,
      final int? getTime,
      final String? lockImage,
      final String? unlockImage,
      final String? resourceFlutter,
      final String? bizId,
      final String? rewardBizUrl}) = _$_ClassActivitiesTaskRewardsVo;

  factory _ClassActivitiesTaskRewardsVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesTaskRewardsVo.fromJson;

  @override
  int? get rewardId;
  @override
  int? get isGet;
  @override
  int? get isPopup;
  @override
  int? get type;
  @override
  int? get getTime;
  @override
  String? get lockImage;
  @override
  String? get unlockImage;
  @override
  String? get resourceFlutter;
  @override
  String? get bizId;
  @override
  String? get rewardBizUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskRewardsVoCopyWith<_$_ClassActivitiesTaskRewardsVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesTaskExtendResourceVo
    _$ClassActivitiesTaskExtendResourceVoFromJson(Map<String, dynamic> json) {
  return _ClassActivitiesTaskExtendResourceVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskExtendResourceVo {
  String? get rewardDisplayUrl => throw _privateConstructorUsedError;
  String? get mainText => throw _privateConstructorUsedError;
  String? get subText => throw _privateConstructorUsedError;
  List<ClassActivitiesTaskRewardNodeTextVo?>? get rewardNodeTexts =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskExtendResourceVoCopyWith<
          ClassActivitiesTaskExtendResourceVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskExtendResourceVoCopyWith<$Res> {
  factory $ClassActivitiesTaskExtendResourceVoCopyWith(
          ClassActivitiesTaskExtendResourceVo value,
          $Res Function(ClassActivitiesTaskExtendResourceVo) then) =
      _$ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res,
          ClassActivitiesTaskExtendResourceVo>;
  @useResult
  $Res call(
      {String? rewardDisplayUrl,
      String? mainText,
      String? subText,
      List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts});
}

/// @nodoc
class _$ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskExtendResourceVo>
    implements $ClassActivitiesTaskExtendResourceVoCopyWith<$Res> {
  _$ClassActivitiesTaskExtendResourceVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardDisplayUrl = freezed,
    Object? mainText = freezed,
    Object? subText = freezed,
    Object? rewardNodeTexts = freezed,
  }) {
    return _then(_value.copyWith(
      rewardDisplayUrl: freezed == rewardDisplayUrl
          ? _value.rewardDisplayUrl
          : rewardDisplayUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      subText: freezed == subText
          ? _value.subText
          : subText // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardNodeTexts: freezed == rewardNodeTexts
          ? _value.rewardNodeTexts
          : rewardNodeTexts // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskRewardNodeTextVo?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskExtendResourceVoCopyWith<$Res>
    implements $ClassActivitiesTaskExtendResourceVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskExtendResourceVoCopyWith(
          _$_ClassActivitiesTaskExtendResourceVo value,
          $Res Function(_$_ClassActivitiesTaskExtendResourceVo) then) =
      __$$_ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? rewardDisplayUrl,
      String? mainText,
      String? subText,
      List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts});
}

/// @nodoc
class __$$_ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res,
        _$_ClassActivitiesTaskExtendResourceVo>
    implements _$$_ClassActivitiesTaskExtendResourceVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskExtendResourceVoCopyWithImpl(
      _$_ClassActivitiesTaskExtendResourceVo _value,
      $Res Function(_$_ClassActivitiesTaskExtendResourceVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardDisplayUrl = freezed,
    Object? mainText = freezed,
    Object? subText = freezed,
    Object? rewardNodeTexts = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskExtendResourceVo(
      rewardDisplayUrl: freezed == rewardDisplayUrl
          ? _value.rewardDisplayUrl
          : rewardDisplayUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      subText: freezed == subText
          ? _value.subText
          : subText // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardNodeTexts: freezed == rewardNodeTexts
          ? _value._rewardNodeTexts
          : rewardNodeTexts // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskRewardNodeTextVo?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskExtendResourceVo
    implements _ClassActivitiesTaskExtendResourceVo {
  const _$_ClassActivitiesTaskExtendResourceVo(
      {this.rewardDisplayUrl,
      this.mainText,
      this.subText,
      final List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts})
      : _rewardNodeTexts = rewardNodeTexts;

  factory _$_ClassActivitiesTaskExtendResourceVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskExtendResourceVoFromJson(json);

  @override
  final String? rewardDisplayUrl;
  @override
  final String? mainText;
  @override
  final String? subText;
  final List<ClassActivitiesTaskRewardNodeTextVo?>? _rewardNodeTexts;
  @override
  List<ClassActivitiesTaskRewardNodeTextVo?>? get rewardNodeTexts {
    final value = _rewardNodeTexts;
    if (value == null) return null;
    if (_rewardNodeTexts is EqualUnmodifiableListView) return _rewardNodeTexts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ClassActivitiesTaskExtendResourceVo(rewardDisplayUrl: $rewardDisplayUrl, mainText: $mainText, subText: $subText, rewardNodeTexts: $rewardNodeTexts)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskExtendResourceVo &&
            (identical(other.rewardDisplayUrl, rewardDisplayUrl) ||
                other.rewardDisplayUrl == rewardDisplayUrl) &&
            (identical(other.mainText, mainText) ||
                other.mainText == mainText) &&
            (identical(other.subText, subText) || other.subText == subText) &&
            const DeepCollectionEquality()
                .equals(other._rewardNodeTexts, _rewardNodeTexts));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, rewardDisplayUrl, mainText,
      subText, const DeepCollectionEquality().hash(_rewardNodeTexts));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskExtendResourceVoCopyWith<
          _$_ClassActivitiesTaskExtendResourceVo>
      get copyWith => __$$_ClassActivitiesTaskExtendResourceVoCopyWithImpl<
          _$_ClassActivitiesTaskExtendResourceVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskExtendResourceVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskExtendResourceVo
    implements ClassActivitiesTaskExtendResourceVo {
  const factory _ClassActivitiesTaskExtendResourceVo(
          {final String? rewardDisplayUrl,
          final String? mainText,
          final String? subText,
          final List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts}) =
      _$_ClassActivitiesTaskExtendResourceVo;

  factory _ClassActivitiesTaskExtendResourceVo.fromJson(
          Map<String, dynamic> json) =
      _$_ClassActivitiesTaskExtendResourceVo.fromJson;

  @override
  String? get rewardDisplayUrl;
  @override
  String? get mainText;
  @override
  String? get subText;
  @override
  List<ClassActivitiesTaskRewardNodeTextVo?>? get rewardNodeTexts;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskExtendResourceVoCopyWith<
          _$_ClassActivitiesTaskExtendResourceVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesTaskRewardNodeTextVo
    _$ClassActivitiesTaskRewardNodeTextVoFromJson(Map<String, dynamic> json) {
  return _ClassActivitiesTaskRewardNodeTextVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskRewardNodeTextVo {
  int? get rewardType => throw _privateConstructorUsedError;
  String? get mainText => throw _privateConstructorUsedError;
  String? get subText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskRewardNodeTextVoCopyWith<
          ClassActivitiesTaskRewardNodeTextVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res> {
  factory $ClassActivitiesTaskRewardNodeTextVoCopyWith(
          ClassActivitiesTaskRewardNodeTextVo value,
          $Res Function(ClassActivitiesTaskRewardNodeTextVo) then) =
      _$ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res,
          ClassActivitiesTaskRewardNodeTextVo>;
  @useResult
  $Res call({int? rewardType, String? mainText, String? subText});
}

/// @nodoc
class _$ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskRewardNodeTextVo>
    implements $ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res> {
  _$ClassActivitiesTaskRewardNodeTextVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardType = freezed,
    Object? mainText = freezed,
    Object? subText = freezed,
  }) {
    return _then(_value.copyWith(
      rewardType: freezed == rewardType
          ? _value.rewardType
          : rewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      subText: freezed == subText
          ? _value.subText
          : subText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res>
    implements $ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith(
          _$_ClassActivitiesTaskRewardNodeTextVo value,
          $Res Function(_$_ClassActivitiesTaskRewardNodeTextVo) then) =
      __$$_ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? rewardType, String? mainText, String? subText});
}

/// @nodoc
class __$$_ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res,
        _$_ClassActivitiesTaskRewardNodeTextVo>
    implements _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskRewardNodeTextVoCopyWithImpl(
      _$_ClassActivitiesTaskRewardNodeTextVo _value,
      $Res Function(_$_ClassActivitiesTaskRewardNodeTextVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardType = freezed,
    Object? mainText = freezed,
    Object? subText = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskRewardNodeTextVo(
      rewardType: freezed == rewardType
          ? _value.rewardType
          : rewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      subText: freezed == subText
          ? _value.subText
          : subText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskRewardNodeTextVo
    implements _ClassActivitiesTaskRewardNodeTextVo {
  const _$_ClassActivitiesTaskRewardNodeTextVo(
      {this.rewardType, this.mainText, this.subText});

  factory _$_ClassActivitiesTaskRewardNodeTextVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskRewardNodeTextVoFromJson(json);

  @override
  final int? rewardType;
  @override
  final String? mainText;
  @override
  final String? subText;

  @override
  String toString() {
    return 'ClassActivitiesTaskRewardNodeTextVo(rewardType: $rewardType, mainText: $mainText, subText: $subText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskRewardNodeTextVo &&
            (identical(other.rewardType, rewardType) ||
                other.rewardType == rewardType) &&
            (identical(other.mainText, mainText) ||
                other.mainText == mainText) &&
            (identical(other.subText, subText) || other.subText == subText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, rewardType, mainText, subText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith<
          _$_ClassActivitiesTaskRewardNodeTextVo>
      get copyWith => __$$_ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<
          _$_ClassActivitiesTaskRewardNodeTextVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskRewardNodeTextVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskRewardNodeTextVo
    implements ClassActivitiesTaskRewardNodeTextVo {
  const factory _ClassActivitiesTaskRewardNodeTextVo(
      {final int? rewardType,
      final String? mainText,
      final String? subText}) = _$_ClassActivitiesTaskRewardNodeTextVo;

  factory _ClassActivitiesTaskRewardNodeTextVo.fromJson(
          Map<String, dynamic> json) =
      _$_ClassActivitiesTaskRewardNodeTextVo.fromJson;

  @override
  int? get rewardType;
  @override
  String? get mainText;
  @override
  String? get subText;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith<
          _$_ClassActivitiesTaskRewardNodeTextVo>
      get copyWith => throw _privateConstructorUsedError;
}

AddTeacherVo _$AddTeacherVoFromJson(Map<String, dynamic> json) {
  return _AddTeacherVo.fromJson(json);
}

/// @nodoc
mixin _$AddTeacherVo {
  String? get bannerImage => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  String? get customState => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AddTeacherVoCopyWith<AddTeacherVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddTeacherVoCopyWith<$Res> {
  factory $AddTeacherVoCopyWith(
          AddTeacherVo value, $Res Function(AddTeacherVo) then) =
      _$AddTeacherVoCopyWithImpl<$Res, AddTeacherVo>;
  @useResult
  $Res call({String? bannerImage, String? route, String? customState});
}

/// @nodoc
class _$AddTeacherVoCopyWithImpl<$Res, $Val extends AddTeacherVo>
    implements $AddTeacherVoCopyWith<$Res> {
  _$AddTeacherVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bannerImage = freezed,
    Object? route = freezed,
    Object? customState = freezed,
  }) {
    return _then(_value.copyWith(
      bannerImage: freezed == bannerImage
          ? _value.bannerImage
          : bannerImage // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      customState: freezed == customState
          ? _value.customState
          : customState // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AddTeacherVoCopyWith<$Res>
    implements $AddTeacherVoCopyWith<$Res> {
  factory _$$_AddTeacherVoCopyWith(
          _$_AddTeacherVo value, $Res Function(_$_AddTeacherVo) then) =
      __$$_AddTeacherVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? bannerImage, String? route, String? customState});
}

/// @nodoc
class __$$_AddTeacherVoCopyWithImpl<$Res>
    extends _$AddTeacherVoCopyWithImpl<$Res, _$_AddTeacherVo>
    implements _$$_AddTeacherVoCopyWith<$Res> {
  __$$_AddTeacherVoCopyWithImpl(
      _$_AddTeacherVo _value, $Res Function(_$_AddTeacherVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bannerImage = freezed,
    Object? route = freezed,
    Object? customState = freezed,
  }) {
    return _then(_$_AddTeacherVo(
      bannerImage: freezed == bannerImage
          ? _value.bannerImage
          : bannerImage // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      customState: freezed == customState
          ? _value.customState
          : customState // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AddTeacherVo implements _AddTeacherVo {
  const _$_AddTeacherVo({this.bannerImage, this.route, this.customState});

  factory _$_AddTeacherVo.fromJson(Map<String, dynamic> json) =>
      _$$_AddTeacherVoFromJson(json);

  @override
  final String? bannerImage;
  @override
  final String? route;
  @override
  final String? customState;

  @override
  String toString() {
    return 'AddTeacherVo(bannerImage: $bannerImage, route: $route, customState: $customState)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AddTeacherVo &&
            (identical(other.bannerImage, bannerImage) ||
                other.bannerImage == bannerImage) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.customState, customState) ||
                other.customState == customState));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, bannerImage, route, customState);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AddTeacherVoCopyWith<_$_AddTeacherVo> get copyWith =>
      __$$_AddTeacherVoCopyWithImpl<_$_AddTeacherVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AddTeacherVoToJson(
      this,
    );
  }
}

abstract class _AddTeacherVo implements AddTeacherVo {
  const factory _AddTeacherVo(
      {final String? bannerImage,
      final String? route,
      final String? customState}) = _$_AddTeacherVo;

  factory _AddTeacherVo.fromJson(Map<String, dynamic> json) =
      _$_AddTeacherVo.fromJson;

  @override
  String? get bannerImage;
  @override
  String? get route;
  @override
  String? get customState;
  @override
  @JsonKey(ignore: true)
  _$$_AddTeacherVoCopyWith<_$_AddTeacherVo> get copyWith =>
      throw _privateConstructorUsedError;
}

BuyCourseVo _$BuyCourseVoFromJson(Map<String, dynamic> json) {
  return _BuyCourseVo.fromJson(json);
}

/// @nodoc
mixin _$BuyCourseVo {
  int? get buyCourseSegmentCode => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BuyCourseVoCopyWith<BuyCourseVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyCourseVoCopyWith<$Res> {
  factory $BuyCourseVoCopyWith(
          BuyCourseVo value, $Res Function(BuyCourseVo) then) =
      _$BuyCourseVoCopyWithImpl<$Res, BuyCourseVo>;
  @useResult
  $Res call({int? buyCourseSegmentCode, String? buttonText, String? route});
}

/// @nodoc
class _$BuyCourseVoCopyWithImpl<$Res, $Val extends BuyCourseVo>
    implements $BuyCourseVoCopyWith<$Res> {
  _$BuyCourseVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyCourseSegmentCode = freezed,
    Object? buttonText = freezed,
    Object? route = freezed,
  }) {
    return _then(_value.copyWith(
      buyCourseSegmentCode: freezed == buyCourseSegmentCode
          ? _value.buyCourseSegmentCode
          : buyCourseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BuyCourseVoCopyWith<$Res>
    implements $BuyCourseVoCopyWith<$Res> {
  factory _$$_BuyCourseVoCopyWith(
          _$_BuyCourseVo value, $Res Function(_$_BuyCourseVo) then) =
      __$$_BuyCourseVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? buyCourseSegmentCode, String? buttonText, String? route});
}

/// @nodoc
class __$$_BuyCourseVoCopyWithImpl<$Res>
    extends _$BuyCourseVoCopyWithImpl<$Res, _$_BuyCourseVo>
    implements _$$_BuyCourseVoCopyWith<$Res> {
  __$$_BuyCourseVoCopyWithImpl(
      _$_BuyCourseVo _value, $Res Function(_$_BuyCourseVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyCourseSegmentCode = freezed,
    Object? buttonText = freezed,
    Object? route = freezed,
  }) {
    return _then(_$_BuyCourseVo(
      buyCourseSegmentCode: freezed == buyCourseSegmentCode
          ? _value.buyCourseSegmentCode
          : buyCourseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BuyCourseVo implements _BuyCourseVo {
  const _$_BuyCourseVo(
      {this.buyCourseSegmentCode, this.buttonText, this.route});

  factory _$_BuyCourseVo.fromJson(Map<String, dynamic> json) =>
      _$$_BuyCourseVoFromJson(json);

  @override
  final int? buyCourseSegmentCode;
  @override
  final String? buttonText;
  @override
  final String? route;

  @override
  String toString() {
    return 'BuyCourseVo(buyCourseSegmentCode: $buyCourseSegmentCode, buttonText: $buttonText, route: $route)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BuyCourseVo &&
            (identical(other.buyCourseSegmentCode, buyCourseSegmentCode) ||
                other.buyCourseSegmentCode == buyCourseSegmentCode) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            (identical(other.route, route) || other.route == route));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, buyCourseSegmentCode, buttonText, route);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BuyCourseVoCopyWith<_$_BuyCourseVo> get copyWith =>
      __$$_BuyCourseVoCopyWithImpl<_$_BuyCourseVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BuyCourseVoToJson(
      this,
    );
  }
}

abstract class _BuyCourseVo implements BuyCourseVo {
  const factory _BuyCourseVo(
      {final int? buyCourseSegmentCode,
      final String? buttonText,
      final String? route}) = _$_BuyCourseVo;

  factory _BuyCourseVo.fromJson(Map<String, dynamic> json) =
      _$_BuyCourseVo.fromJson;

  @override
  int? get buyCourseSegmentCode;
  @override
  String? get buttonText;
  @override
  String? get route;
  @override
  @JsonKey(ignore: true)
  _$$_BuyCourseVoCopyWith<_$_BuyCourseVo> get copyWith =>
      throw _privateConstructorUsedError;
}

CourseStartPrepareVo _$CourseStartPrepareVoFromJson(Map<String, dynamic> json) {
  return _CourseStartPrepareVo.fromJson(json);
}

/// @nodoc
mixin _$CourseStartPrepareVo {
  String? get courseImg => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get startClassDateDesc => throw _privateConstructorUsedError;
  List<TimetableNodeList>? get prepareNodeList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CourseStartPrepareVoCopyWith<CourseStartPrepareVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseStartPrepareVoCopyWith<$Res> {
  factory $CourseStartPrepareVoCopyWith(CourseStartPrepareVo value,
          $Res Function(CourseStartPrepareVo) then) =
      _$CourseStartPrepareVoCopyWithImpl<$Res, CourseStartPrepareVo>;
  @useResult
  $Res call(
      {String? courseImg,
      String? title,
      String? startClassDateDesc,
      List<TimetableNodeList>? prepareNodeList});
}

/// @nodoc
class _$CourseStartPrepareVoCopyWithImpl<$Res,
        $Val extends CourseStartPrepareVo>
    implements $CourseStartPrepareVoCopyWith<$Res> {
  _$CourseStartPrepareVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseImg = freezed,
    Object? title = freezed,
    Object? startClassDateDesc = freezed,
    Object? prepareNodeList = freezed,
  }) {
    return _then(_value.copyWith(
      courseImg: freezed == courseImg
          ? _value.courseImg
          : courseImg // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      startClassDateDesc: freezed == startClassDateDesc
          ? _value.startClassDateDesc
          : startClassDateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      prepareNodeList: freezed == prepareNodeList
          ? _value.prepareNodeList
          : prepareNodeList // ignore: cast_nullable_to_non_nullable
              as List<TimetableNodeList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CourseStartPrepareVoCopyWith<$Res>
    implements $CourseStartPrepareVoCopyWith<$Res> {
  factory _$$_CourseStartPrepareVoCopyWith(_$_CourseStartPrepareVo value,
          $Res Function(_$_CourseStartPrepareVo) then) =
      __$$_CourseStartPrepareVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseImg,
      String? title,
      String? startClassDateDesc,
      List<TimetableNodeList>? prepareNodeList});
}

/// @nodoc
class __$$_CourseStartPrepareVoCopyWithImpl<$Res>
    extends _$CourseStartPrepareVoCopyWithImpl<$Res, _$_CourseStartPrepareVo>
    implements _$$_CourseStartPrepareVoCopyWith<$Res> {
  __$$_CourseStartPrepareVoCopyWithImpl(_$_CourseStartPrepareVo _value,
      $Res Function(_$_CourseStartPrepareVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseImg = freezed,
    Object? title = freezed,
    Object? startClassDateDesc = freezed,
    Object? prepareNodeList = freezed,
  }) {
    return _then(_$_CourseStartPrepareVo(
      courseImg: freezed == courseImg
          ? _value.courseImg
          : courseImg // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      startClassDateDesc: freezed == startClassDateDesc
          ? _value.startClassDateDesc
          : startClassDateDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      prepareNodeList: freezed == prepareNodeList
          ? _value._prepareNodeList
          : prepareNodeList // ignore: cast_nullable_to_non_nullable
              as List<TimetableNodeList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_CourseStartPrepareVo implements _CourseStartPrepareVo {
  const _$_CourseStartPrepareVo(
      {this.courseImg,
      this.title,
      this.startClassDateDesc,
      final List<TimetableNodeList>? prepareNodeList})
      : _prepareNodeList = prepareNodeList;

  factory _$_CourseStartPrepareVo.fromJson(Map<String, dynamic> json) =>
      _$$_CourseStartPrepareVoFromJson(json);

  @override
  final String? courseImg;
  @override
  final String? title;
  @override
  final String? startClassDateDesc;
  final List<TimetableNodeList>? _prepareNodeList;
  @override
  List<TimetableNodeList>? get prepareNodeList {
    final value = _prepareNodeList;
    if (value == null) return null;
    if (_prepareNodeList is EqualUnmodifiableListView) return _prepareNodeList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'CourseStartPrepareVo(courseImg: $courseImg, title: $title, startClassDateDesc: $startClassDateDesc, prepareNodeList: $prepareNodeList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_CourseStartPrepareVo &&
            (identical(other.courseImg, courseImg) ||
                other.courseImg == courseImg) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.startClassDateDesc, startClassDateDesc) ||
                other.startClassDateDesc == startClassDateDesc) &&
            const DeepCollectionEquality()
                .equals(other._prepareNodeList, _prepareNodeList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      courseImg,
      title,
      startClassDateDesc,
      const DeepCollectionEquality().hash(_prepareNodeList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CourseStartPrepareVoCopyWith<_$_CourseStartPrepareVo> get copyWith =>
      __$$_CourseStartPrepareVoCopyWithImpl<_$_CourseStartPrepareVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CourseStartPrepareVoToJson(
      this,
    );
  }
}

abstract class _CourseStartPrepareVo implements CourseStartPrepareVo {
  const factory _CourseStartPrepareVo(
          {final String? courseImg,
          final String? title,
          final String? startClassDateDesc,
          final List<TimetableNodeList>? prepareNodeList}) =
      _$_CourseStartPrepareVo;

  factory _CourseStartPrepareVo.fromJson(Map<String, dynamic> json) =
      _$_CourseStartPrepareVo.fromJson;

  @override
  String? get courseImg;
  @override
  String? get title;
  @override
  String? get startClassDateDesc;
  @override
  List<TimetableNodeList>? get prepareNodeList;
  @override
  @JsonKey(ignore: true)
  _$$_CourseStartPrepareVoCopyWith<_$_CourseStartPrepareVo> get copyWith =>
      throw _privateConstructorUsedError;
}

FriendCardInfo _$FriendCardInfoFromJson(Map<String, dynamic> json) {
  return _FriendCardInfo.fromJson(json);
}

/// @nodoc
mixin _$FriendCardInfo {
  String? get icon => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  int? get validPeriod => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FriendCardInfoCopyWith<FriendCardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FriendCardInfoCopyWith<$Res> {
  factory $FriendCardInfoCopyWith(
          FriendCardInfo value, $Res Function(FriendCardInfo) then) =
      _$FriendCardInfoCopyWithImpl<$Res, FriendCardInfo>;
  @useResult
  $Res call({String? icon, String? title, int? validPeriod, String? jumpRoute});
}

/// @nodoc
class _$FriendCardInfoCopyWithImpl<$Res, $Val extends FriendCardInfo>
    implements $FriendCardInfoCopyWith<$Res> {
  _$FriendCardInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? title = freezed,
    Object? validPeriod = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      validPeriod: freezed == validPeriod
          ? _value.validPeriod
          : validPeriod // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FriendCardInfoCopyWith<$Res>
    implements $FriendCardInfoCopyWith<$Res> {
  factory _$$_FriendCardInfoCopyWith(
          _$_FriendCardInfo value, $Res Function(_$_FriendCardInfo) then) =
      __$$_FriendCardInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? icon, String? title, int? validPeriod, String? jumpRoute});
}

/// @nodoc
class __$$_FriendCardInfoCopyWithImpl<$Res>
    extends _$FriendCardInfoCopyWithImpl<$Res, _$_FriendCardInfo>
    implements _$$_FriendCardInfoCopyWith<$Res> {
  __$$_FriendCardInfoCopyWithImpl(
      _$_FriendCardInfo _value, $Res Function(_$_FriendCardInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? icon = freezed,
    Object? title = freezed,
    Object? validPeriod = freezed,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_FriendCardInfo(
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      validPeriod: freezed == validPeriod
          ? _value.validPeriod
          : validPeriod // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_FriendCardInfo implements _FriendCardInfo {
  const _$_FriendCardInfo(
      {this.icon, this.title, this.validPeriod, this.jumpRoute});

  factory _$_FriendCardInfo.fromJson(Map<String, dynamic> json) =>
      _$$_FriendCardInfoFromJson(json);

  @override
  final String? icon;
  @override
  final String? title;
  @override
  final int? validPeriod;
  @override
  final String? jumpRoute;

  @override
  String toString() {
    return 'FriendCardInfo(icon: $icon, title: $title, validPeriod: $validPeriod, jumpRoute: $jumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FriendCardInfo &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.validPeriod, validPeriod) ||
                other.validPeriod == validPeriod) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, icon, title, validPeriod, jumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FriendCardInfoCopyWith<_$_FriendCardInfo> get copyWith =>
      __$$_FriendCardInfoCopyWithImpl<_$_FriendCardInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_FriendCardInfoToJson(
      this,
    );
  }
}

abstract class _FriendCardInfo implements FriendCardInfo {
  const factory _FriendCardInfo(
      {final String? icon,
      final String? title,
      final int? validPeriod,
      final String? jumpRoute}) = _$_FriendCardInfo;

  factory _FriendCardInfo.fromJson(Map<String, dynamic> json) =
      _$_FriendCardInfo.fromJson;

  @override
  String? get icon;
  @override
  String? get title;
  @override
  int? get validPeriod;
  @override
  String? get jumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_FriendCardInfoCopyWith<_$_FriendCardInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassFunctionList _$ClassFunctionListFromJson(Map<String, dynamic> json) {
  return _ClassFunctionList.fromJson(json);
}

/// @nodoc
mixin _$ClassFunctionList {
  int? get functionType => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  List<String>? get toastList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassFunctionListCopyWith<ClassFunctionList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassFunctionListCopyWith<$Res> {
  factory $ClassFunctionListCopyWith(
          ClassFunctionList value, $Res Function(ClassFunctionList) then) =
      _$ClassFunctionListCopyWithImpl<$Res, ClassFunctionList>;
  @useResult
  $Res call(
      {int? functionType,
      String? title,
      String? icon,
      String? route,
      List<String>? toastList});
}

/// @nodoc
class _$ClassFunctionListCopyWithImpl<$Res, $Val extends ClassFunctionList>
    implements $ClassFunctionListCopyWith<$Res> {
  _$ClassFunctionListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? functionType = freezed,
    Object? title = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? toastList = freezed,
  }) {
    return _then(_value.copyWith(
      functionType: freezed == functionType
          ? _value.functionType
          : functionType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      toastList: freezed == toastList
          ? _value.toastList
          : toastList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassFunctionListCopyWith<$Res>
    implements $ClassFunctionListCopyWith<$Res> {
  factory _$$_ClassFunctionListCopyWith(_$_ClassFunctionList value,
          $Res Function(_$_ClassFunctionList) then) =
      __$$_ClassFunctionListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? functionType,
      String? title,
      String? icon,
      String? route,
      List<String>? toastList});
}

/// @nodoc
class __$$_ClassFunctionListCopyWithImpl<$Res>
    extends _$ClassFunctionListCopyWithImpl<$Res, _$_ClassFunctionList>
    implements _$$_ClassFunctionListCopyWith<$Res> {
  __$$_ClassFunctionListCopyWithImpl(
      _$_ClassFunctionList _value, $Res Function(_$_ClassFunctionList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? functionType = freezed,
    Object? title = freezed,
    Object? icon = freezed,
    Object? route = freezed,
    Object? toastList = freezed,
  }) {
    return _then(_$_ClassFunctionList(
      functionType: freezed == functionType
          ? _value.functionType
          : functionType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      toastList: freezed == toastList
          ? _value._toastList
          : toastList // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassFunctionList implements _ClassFunctionList {
  const _$_ClassFunctionList(
      {this.functionType,
      this.title,
      this.icon,
      this.route,
      final List<String>? toastList})
      : _toastList = toastList;

  factory _$_ClassFunctionList.fromJson(Map<String, dynamic> json) =>
      _$$_ClassFunctionListFromJson(json);

  @override
  final int? functionType;
  @override
  final String? title;
  @override
  final String? icon;
  @override
  final String? route;
  final List<String>? _toastList;
  @override
  List<String>? get toastList {
    final value = _toastList;
    if (value == null) return null;
    if (_toastList is EqualUnmodifiableListView) return _toastList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ClassFunctionList(functionType: $functionType, title: $title, icon: $icon, route: $route, toastList: $toastList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassFunctionList &&
            (identical(other.functionType, functionType) ||
                other.functionType == functionType) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.route, route) || other.route == route) &&
            const DeepCollectionEquality()
                .equals(other._toastList, _toastList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, functionType, title, icon, route,
      const DeepCollectionEquality().hash(_toastList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassFunctionListCopyWith<_$_ClassFunctionList> get copyWith =>
      __$$_ClassFunctionListCopyWithImpl<_$_ClassFunctionList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassFunctionListToJson(
      this,
    );
  }
}

abstract class _ClassFunctionList implements ClassFunctionList {
  const factory _ClassFunctionList(
      {final int? functionType,
      final String? title,
      final String? icon,
      final String? route,
      final List<String>? toastList}) = _$_ClassFunctionList;

  factory _ClassFunctionList.fromJson(Map<String, dynamic> json) =
      _$_ClassFunctionList.fromJson;

  @override
  int? get functionType;
  @override
  String? get title;
  @override
  String? get icon;
  @override
  String? get route;
  @override
  List<String>? get toastList;
  @override
  @JsonKey(ignore: true)
  _$$_ClassFunctionListCopyWith<_$_ClassFunctionList> get copyWith =>
      throw _privateConstructorUsedError;
}

TimetableNodeList _$TimetableNodeListFromJson(Map<String, dynamic> json) {
  return _TimetableNodeList.fromJson(json);
}

/// @nodoc
mixin _$TimetableNodeList {
  int? get showDateTime => throw _privateConstructorUsedError; //显示日期 当天0点毫秒时间戳
  String? get showDate => throw _privateConstructorUsedError; //显示日期 yyyy-MM-dd
  bool? get focus => throw _privateConstructorUsedError; //是否聚焦
  bool? get today => throw _privateConstructorUsedError; //是否今天
  bool? get gif => throw _privateConstructorUsedError; //是否是gif
  bool? get position => throw _privateConstructorUsedError; //用于默认定位
  String? get unfinishLottieIcon =>
      throw _privateConstructorUsedError; //gif==true时，卡片图片取该字段，和是否聚焦无关
  int? get nodeStatus =>
      throw _privateConstructorUsedError; //节点状态,可用值:0:未解锁,1:已解锁未完成,2:已解锁已完成,3:待补学
  int? get nodeType => throw _privateConstructorUsedError; //节点类型,可用值:1:课时
  String? get title => throw _privateConstructorUsedError; //标题
  String? get route => throw _privateConstructorUsedError; //跳转地址
  String? get toast => throw _privateConstructorUsedError; //语音文案
  int? get nodeId => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError; //课时id
  int? get lessonOrder => throw _privateConstructorUsedError; //课时排序
  String? get segmentName => throw _privateConstructorUsedError; //主题名称
  int? get segmentId => throw _privateConstructorUsedError; //主题id
  String? get weekName => throw _privateConstructorUsedError; //单元名称
  int? get weekId => throw _privateConstructorUsedError; //单元id
  int? get collectStatus =>
      throw _privateConstructorUsedError; // 0 :不展示收集物 1： 展示收集物
  String? get introductory => throw _privateConstructorUsedError; //单元引导语
  int? get courseChildType =>
      throw _privateConstructorUsedError; //是否属于滚动排期 1滚动排期
  String? get studyDesc => throw _privateConstructorUsedError; //学习描述(去学习)
  int? get studyStatus => throw _privateConstructorUsedError; //学习状态(0不可学, 1可学)
  String? get studyTips => throw _privateConstructorUsedError; //不可学提示
  String? get studyTipsVoice => throw _privateConstructorUsedError; //不可学提示语音
  ///非课卡片才会处理以下字段(nodeType)
  String? get lockIcon => throw _privateConstructorUsedError; // 未解锁图标
  String? get unfinishIcon => throw _privateConstructorUsedError; //未完成icon
  String? get finishIcon => throw _privateConstructorUsedError; //已完成icon
  String? get lockVoice => throw _privateConstructorUsedError; // 未解锁语音
  String? get unfinishVoice => throw _privateConstructorUsedError; //未完成语音
  String? get finishVoice => throw _privateConstructorUsedError; //已完成语音
  int? get lessonGrade => throw _privateConstructorUsedError;
  String? get lessonGradeResourceUrl => throw _privateConstructorUsedError;
  int? get unFinishedNum => throw _privateConstructorUsedError;
  String? get reviewRoute => throw _privateConstructorUsedError;
  bool? get makeupUIExpired =>
      throw _privateConstructorUsedError; //补读是否过期true表示过期默认false
  int? get lessonSubjectType => throw _privateConstructorUsedError;
  String? get subjectNodeIcon => throw _privateConstructorUsedError;
  List<RewardVo>? get rewardVoList =>
      throw _privateConstructorUsedError; //阶段奖励数据
  PopupInfo? get popupInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TimetableNodeListCopyWith<TimetableNodeList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimetableNodeListCopyWith<$Res> {
  factory $TimetableNodeListCopyWith(
          TimetableNodeList value, $Res Function(TimetableNodeList) then) =
      _$TimetableNodeListCopyWithImpl<$Res, TimetableNodeList>;
  @useResult
  $Res call(
      {int? showDateTime,
      String? showDate,
      bool? focus,
      bool? today,
      bool? gif,
      bool? position,
      String? unfinishLottieIcon,
      int? nodeStatus,
      int? nodeType,
      String? title,
      String? route,
      String? toast,
      int? nodeId,
      int? lessonId,
      int? lessonOrder,
      String? segmentName,
      int? segmentId,
      String? weekName,
      int? weekId,
      int? collectStatus,
      String? introductory,
      int? courseChildType,
      String? studyDesc,
      int? studyStatus,
      String? studyTips,
      String? studyTipsVoice,
      String? lockIcon,
      String? unfinishIcon,
      String? finishIcon,
      String? lockVoice,
      String? unfinishVoice,
      String? finishVoice,
      int? lessonGrade,
      String? lessonGradeResourceUrl,
      int? unFinishedNum,
      String? reviewRoute,
      bool? makeupUIExpired,
      int? lessonSubjectType,
      String? subjectNodeIcon,
      List<RewardVo>? rewardVoList,
      PopupInfo? popupInfo});

  $PopupInfoCopyWith<$Res>? get popupInfo;
}

/// @nodoc
class _$TimetableNodeListCopyWithImpl<$Res, $Val extends TimetableNodeList>
    implements $TimetableNodeListCopyWith<$Res> {
  _$TimetableNodeListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDateTime = freezed,
    Object? showDate = freezed,
    Object? focus = freezed,
    Object? today = freezed,
    Object? gif = freezed,
    Object? position = freezed,
    Object? unfinishLottieIcon = freezed,
    Object? nodeStatus = freezed,
    Object? nodeType = freezed,
    Object? title = freezed,
    Object? route = freezed,
    Object? toast = freezed,
    Object? nodeId = freezed,
    Object? lessonId = freezed,
    Object? lessonOrder = freezed,
    Object? segmentName = freezed,
    Object? segmentId = freezed,
    Object? weekName = freezed,
    Object? weekId = freezed,
    Object? collectStatus = freezed,
    Object? introductory = freezed,
    Object? courseChildType = freezed,
    Object? studyDesc = freezed,
    Object? studyStatus = freezed,
    Object? studyTips = freezed,
    Object? studyTipsVoice = freezed,
    Object? lockIcon = freezed,
    Object? unfinishIcon = freezed,
    Object? finishIcon = freezed,
    Object? lockVoice = freezed,
    Object? unfinishVoice = freezed,
    Object? finishVoice = freezed,
    Object? lessonGrade = freezed,
    Object? lessonGradeResourceUrl = freezed,
    Object? unFinishedNum = freezed,
    Object? reviewRoute = freezed,
    Object? makeupUIExpired = freezed,
    Object? lessonSubjectType = freezed,
    Object? subjectNodeIcon = freezed,
    Object? rewardVoList = freezed,
    Object? popupInfo = freezed,
  }) {
    return _then(_value.copyWith(
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      showDate: freezed == showDate
          ? _value.showDate
          : showDate // ignore: cast_nullable_to_non_nullable
              as String?,
      focus: freezed == focus
          ? _value.focus
          : focus // ignore: cast_nullable_to_non_nullable
              as bool?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      gif: freezed == gif
          ? _value.gif
          : gif // ignore: cast_nullable_to_non_nullable
              as bool?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as bool?,
      unfinishLottieIcon: freezed == unfinishLottieIcon
          ? _value.unfinishLottieIcon
          : unfinishLottieIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      nodeStatus: freezed == nodeStatus
          ? _value.nodeStatus
          : nodeStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeType: freezed == nodeType
          ? _value.nodeType
          : nodeType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      nodeId: freezed == nodeId
          ? _value.nodeId
          : nodeId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      collectStatus: freezed == collectStatus
          ? _value.collectStatus
          : collectStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      introductory: freezed == introductory
          ? _value.introductory
          : introductory // ignore: cast_nullable_to_non_nullable
              as String?,
      courseChildType: freezed == courseChildType
          ? _value.courseChildType
          : courseChildType // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDesc: freezed == studyDesc
          ? _value.studyDesc
          : studyDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      studyStatus: freezed == studyStatus
          ? _value.studyStatus
          : studyStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      studyTips: freezed == studyTips
          ? _value.studyTips
          : studyTips // ignore: cast_nullable_to_non_nullable
              as String?,
      studyTipsVoice: freezed == studyTipsVoice
          ? _value.studyTipsVoice
          : studyTipsVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lockIcon: freezed == lockIcon
          ? _value.lockIcon
          : lockIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      unfinishIcon: freezed == unfinishIcon
          ? _value.unfinishIcon
          : unfinishIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      finishIcon: freezed == finishIcon
          ? _value.finishIcon
          : finishIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      lockVoice: freezed == lockVoice
          ? _value.lockVoice
          : lockVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      unfinishVoice: freezed == unfinishVoice
          ? _value.unfinishVoice
          : unfinishVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      finishVoice: freezed == finishVoice
          ? _value.finishVoice
          : finishVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonGrade: freezed == lessonGrade
          ? _value.lessonGrade
          : lessonGrade // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonGradeResourceUrl: freezed == lessonGradeResourceUrl
          ? _value.lessonGradeResourceUrl
          : lessonGradeResourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      unFinishedNum: freezed == unFinishedNum
          ? _value.unFinishedNum
          : unFinishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      reviewRoute: freezed == reviewRoute
          ? _value.reviewRoute
          : reviewRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      makeupUIExpired: freezed == makeupUIExpired
          ? _value.makeupUIExpired
          : makeupUIExpired // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonSubjectType: freezed == lessonSubjectType
          ? _value.lessonSubjectType
          : lessonSubjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectNodeIcon: freezed == subjectNodeIcon
          ? _value.subjectNodeIcon
          : subjectNodeIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardVoList: freezed == rewardVoList
          ? _value.rewardVoList
          : rewardVoList // ignore: cast_nullable_to_non_nullable
              as List<RewardVo>?,
      popupInfo: freezed == popupInfo
          ? _value.popupInfo
          : popupInfo // ignore: cast_nullable_to_non_nullable
              as PopupInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $PopupInfoCopyWith<$Res>? get popupInfo {
    if (_value.popupInfo == null) {
      return null;
    }

    return $PopupInfoCopyWith<$Res>(_value.popupInfo!, (value) {
      return _then(_value.copyWith(popupInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TimetableNodeListCopyWith<$Res>
    implements $TimetableNodeListCopyWith<$Res> {
  factory _$$_TimetableNodeListCopyWith(_$_TimetableNodeList value,
          $Res Function(_$_TimetableNodeList) then) =
      __$$_TimetableNodeListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? showDateTime,
      String? showDate,
      bool? focus,
      bool? today,
      bool? gif,
      bool? position,
      String? unfinishLottieIcon,
      int? nodeStatus,
      int? nodeType,
      String? title,
      String? route,
      String? toast,
      int? nodeId,
      int? lessonId,
      int? lessonOrder,
      String? segmentName,
      int? segmentId,
      String? weekName,
      int? weekId,
      int? collectStatus,
      String? introductory,
      int? courseChildType,
      String? studyDesc,
      int? studyStatus,
      String? studyTips,
      String? studyTipsVoice,
      String? lockIcon,
      String? unfinishIcon,
      String? finishIcon,
      String? lockVoice,
      String? unfinishVoice,
      String? finishVoice,
      int? lessonGrade,
      String? lessonGradeResourceUrl,
      int? unFinishedNum,
      String? reviewRoute,
      bool? makeupUIExpired,
      int? lessonSubjectType,
      String? subjectNodeIcon,
      List<RewardVo>? rewardVoList,
      PopupInfo? popupInfo});

  @override
  $PopupInfoCopyWith<$Res>? get popupInfo;
}

/// @nodoc
class __$$_TimetableNodeListCopyWithImpl<$Res>
    extends _$TimetableNodeListCopyWithImpl<$Res, _$_TimetableNodeList>
    implements _$$_TimetableNodeListCopyWith<$Res> {
  __$$_TimetableNodeListCopyWithImpl(
      _$_TimetableNodeList _value, $Res Function(_$_TimetableNodeList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showDateTime = freezed,
    Object? showDate = freezed,
    Object? focus = freezed,
    Object? today = freezed,
    Object? gif = freezed,
    Object? position = freezed,
    Object? unfinishLottieIcon = freezed,
    Object? nodeStatus = freezed,
    Object? nodeType = freezed,
    Object? title = freezed,
    Object? route = freezed,
    Object? toast = freezed,
    Object? nodeId = freezed,
    Object? lessonId = freezed,
    Object? lessonOrder = freezed,
    Object? segmentName = freezed,
    Object? segmentId = freezed,
    Object? weekName = freezed,
    Object? weekId = freezed,
    Object? collectStatus = freezed,
    Object? introductory = freezed,
    Object? courseChildType = freezed,
    Object? studyDesc = freezed,
    Object? studyStatus = freezed,
    Object? studyTips = freezed,
    Object? studyTipsVoice = freezed,
    Object? lockIcon = freezed,
    Object? unfinishIcon = freezed,
    Object? finishIcon = freezed,
    Object? lockVoice = freezed,
    Object? unfinishVoice = freezed,
    Object? finishVoice = freezed,
    Object? lessonGrade = freezed,
    Object? lessonGradeResourceUrl = freezed,
    Object? unFinishedNum = freezed,
    Object? reviewRoute = freezed,
    Object? makeupUIExpired = freezed,
    Object? lessonSubjectType = freezed,
    Object? subjectNodeIcon = freezed,
    Object? rewardVoList = freezed,
    Object? popupInfo = freezed,
  }) {
    return _then(_$_TimetableNodeList(
      showDateTime: freezed == showDateTime
          ? _value.showDateTime
          : showDateTime // ignore: cast_nullable_to_non_nullable
              as int?,
      showDate: freezed == showDate
          ? _value.showDate
          : showDate // ignore: cast_nullable_to_non_nullable
              as String?,
      focus: freezed == focus
          ? _value.focus
          : focus // ignore: cast_nullable_to_non_nullable
              as bool?,
      today: freezed == today
          ? _value.today
          : today // ignore: cast_nullable_to_non_nullable
              as bool?,
      gif: freezed == gif
          ? _value.gif
          : gif // ignore: cast_nullable_to_non_nullable
              as bool?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as bool?,
      unfinishLottieIcon: freezed == unfinishLottieIcon
          ? _value.unfinishLottieIcon
          : unfinishLottieIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      nodeStatus: freezed == nodeStatus
          ? _value.nodeStatus
          : nodeStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeType: freezed == nodeType
          ? _value.nodeType
          : nodeType // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      toast: freezed == toast
          ? _value.toast
          : toast // ignore: cast_nullable_to_non_nullable
              as String?,
      nodeId: freezed == nodeId
          ? _value.nodeId
          : nodeId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonOrder: freezed == lessonOrder
          ? _value.lessonOrder
          : lessonOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentName: freezed == segmentName
          ? _value.segmentName
          : segmentName // ignore: cast_nullable_to_non_nullable
              as String?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      weekName: freezed == weekName
          ? _value.weekName
          : weekName // ignore: cast_nullable_to_non_nullable
              as String?,
      weekId: freezed == weekId
          ? _value.weekId
          : weekId // ignore: cast_nullable_to_non_nullable
              as int?,
      collectStatus: freezed == collectStatus
          ? _value.collectStatus
          : collectStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      introductory: freezed == introductory
          ? _value.introductory
          : introductory // ignore: cast_nullable_to_non_nullable
              as String?,
      courseChildType: freezed == courseChildType
          ? _value.courseChildType
          : courseChildType // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDesc: freezed == studyDesc
          ? _value.studyDesc
          : studyDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      studyStatus: freezed == studyStatus
          ? _value.studyStatus
          : studyStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      studyTips: freezed == studyTips
          ? _value.studyTips
          : studyTips // ignore: cast_nullable_to_non_nullable
              as String?,
      studyTipsVoice: freezed == studyTipsVoice
          ? _value.studyTipsVoice
          : studyTipsVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lockIcon: freezed == lockIcon
          ? _value.lockIcon
          : lockIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      unfinishIcon: freezed == unfinishIcon
          ? _value.unfinishIcon
          : unfinishIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      finishIcon: freezed == finishIcon
          ? _value.finishIcon
          : finishIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      lockVoice: freezed == lockVoice
          ? _value.lockVoice
          : lockVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      unfinishVoice: freezed == unfinishVoice
          ? _value.unfinishVoice
          : unfinishVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      finishVoice: freezed == finishVoice
          ? _value.finishVoice
          : finishVoice // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonGrade: freezed == lessonGrade
          ? _value.lessonGrade
          : lessonGrade // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonGradeResourceUrl: freezed == lessonGradeResourceUrl
          ? _value.lessonGradeResourceUrl
          : lessonGradeResourceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      unFinishedNum: freezed == unFinishedNum
          ? _value.unFinishedNum
          : unFinishedNum // ignore: cast_nullable_to_non_nullable
              as int?,
      reviewRoute: freezed == reviewRoute
          ? _value.reviewRoute
          : reviewRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      makeupUIExpired: freezed == makeupUIExpired
          ? _value.makeupUIExpired
          : makeupUIExpired // ignore: cast_nullable_to_non_nullable
              as bool?,
      lessonSubjectType: freezed == lessonSubjectType
          ? _value.lessonSubjectType
          : lessonSubjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectNodeIcon: freezed == subjectNodeIcon
          ? _value.subjectNodeIcon
          : subjectNodeIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardVoList: freezed == rewardVoList
          ? _value._rewardVoList
          : rewardVoList // ignore: cast_nullable_to_non_nullable
              as List<RewardVo>?,
      popupInfo: freezed == popupInfo
          ? _value.popupInfo
          : popupInfo // ignore: cast_nullable_to_non_nullable
              as PopupInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TimetableNodeList implements _TimetableNodeList {
  const _$_TimetableNodeList(
      {this.showDateTime,
      this.showDate,
      this.focus,
      this.today,
      this.gif,
      this.position,
      this.unfinishLottieIcon,
      this.nodeStatus,
      this.nodeType,
      this.title,
      this.route,
      this.toast,
      this.nodeId,
      this.lessonId,
      this.lessonOrder,
      this.segmentName,
      this.segmentId,
      this.weekName,
      this.weekId,
      this.collectStatus,
      this.introductory,
      this.courseChildType,
      this.studyDesc,
      this.studyStatus,
      this.studyTips,
      this.studyTipsVoice,
      this.lockIcon,
      this.unfinishIcon,
      this.finishIcon,
      this.lockVoice,
      this.unfinishVoice,
      this.finishVoice,
      this.lessonGrade,
      this.lessonGradeResourceUrl,
      this.unFinishedNum,
      this.reviewRoute,
      this.makeupUIExpired,
      this.lessonSubjectType,
      this.subjectNodeIcon,
      final List<RewardVo>? rewardVoList,
      this.popupInfo})
      : _rewardVoList = rewardVoList;

  factory _$_TimetableNodeList.fromJson(Map<String, dynamic> json) =>
      _$$_TimetableNodeListFromJson(json);

  @override
  final int? showDateTime;
//显示日期 当天0点毫秒时间戳
  @override
  final String? showDate;
//显示日期 yyyy-MM-dd
  @override
  final bool? focus;
//是否聚焦
  @override
  final bool? today;
//是否今天
  @override
  final bool? gif;
//是否是gif
  @override
  final bool? position;
//用于默认定位
  @override
  final String? unfinishLottieIcon;
//gif==true时，卡片图片取该字段，和是否聚焦无关
  @override
  final int? nodeStatus;
//节点状态,可用值:0:未解锁,1:已解锁未完成,2:已解锁已完成,3:待补学
  @override
  final int? nodeType;
//节点类型,可用值:1:课时
  @override
  final String? title;
//标题
  @override
  final String? route;
//跳转地址
  @override
  final String? toast;
//语音文案
  @override
  final int? nodeId;
  @override
  final int? lessonId;
//课时id
  @override
  final int? lessonOrder;
//课时排序
  @override
  final String? segmentName;
//主题名称
  @override
  final int? segmentId;
//主题id
  @override
  final String? weekName;
//单元名称
  @override
  final int? weekId;
//单元id
  @override
  final int? collectStatus;
// 0 :不展示收集物 1： 展示收集物
  @override
  final String? introductory;
//单元引导语
  @override
  final int? courseChildType;
//是否属于滚动排期 1滚动排期
  @override
  final String? studyDesc;
//学习描述(去学习)
  @override
  final int? studyStatus;
//学习状态(0不可学, 1可学)
  @override
  final String? studyTips;
//不可学提示
  @override
  final String? studyTipsVoice;
//不可学提示语音
  ///非课卡片才会处理以下字段(nodeType)
  @override
  final String? lockIcon;
// 未解锁图标
  @override
  final String? unfinishIcon;
//未完成icon
  @override
  final String? finishIcon;
//已完成icon
  @override
  final String? lockVoice;
// 未解锁语音
  @override
  final String? unfinishVoice;
//未完成语音
  @override
  final String? finishVoice;
//已完成语音
  @override
  final int? lessonGrade;
  @override
  final String? lessonGradeResourceUrl;
  @override
  final int? unFinishedNum;
  @override
  final String? reviewRoute;
  @override
  final bool? makeupUIExpired;
//补读是否过期true表示过期默认false
  @override
  final int? lessonSubjectType;
  @override
  final String? subjectNodeIcon;
  final List<RewardVo>? _rewardVoList;
  @override
  List<RewardVo>? get rewardVoList {
    final value = _rewardVoList;
    if (value == null) return null;
    if (_rewardVoList is EqualUnmodifiableListView) return _rewardVoList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//阶段奖励数据
  @override
  final PopupInfo? popupInfo;

  @override
  String toString() {
    return 'TimetableNodeList(showDateTime: $showDateTime, showDate: $showDate, focus: $focus, today: $today, gif: $gif, position: $position, unfinishLottieIcon: $unfinishLottieIcon, nodeStatus: $nodeStatus, nodeType: $nodeType, title: $title, route: $route, toast: $toast, nodeId: $nodeId, lessonId: $lessonId, lessonOrder: $lessonOrder, segmentName: $segmentName, segmentId: $segmentId, weekName: $weekName, weekId: $weekId, collectStatus: $collectStatus, introductory: $introductory, courseChildType: $courseChildType, studyDesc: $studyDesc, studyStatus: $studyStatus, studyTips: $studyTips, studyTipsVoice: $studyTipsVoice, lockIcon: $lockIcon, unfinishIcon: $unfinishIcon, finishIcon: $finishIcon, lockVoice: $lockVoice, unfinishVoice: $unfinishVoice, finishVoice: $finishVoice, lessonGrade: $lessonGrade, lessonGradeResourceUrl: $lessonGradeResourceUrl, unFinishedNum: $unFinishedNum, reviewRoute: $reviewRoute, makeupUIExpired: $makeupUIExpired, lessonSubjectType: $lessonSubjectType, subjectNodeIcon: $subjectNodeIcon, rewardVoList: $rewardVoList, popupInfo: $popupInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TimetableNodeList &&
            (identical(other.showDateTime, showDateTime) ||
                other.showDateTime == showDateTime) &&
            (identical(other.showDate, showDate) ||
                other.showDate == showDate) &&
            (identical(other.focus, focus) || other.focus == focus) &&
            (identical(other.today, today) || other.today == today) &&
            (identical(other.gif, gif) || other.gif == gif) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.unfinishLottieIcon, unfinishLottieIcon) ||
                other.unfinishLottieIcon == unfinishLottieIcon) &&
            (identical(other.nodeStatus, nodeStatus) ||
                other.nodeStatus == nodeStatus) &&
            (identical(other.nodeType, nodeType) ||
                other.nodeType == nodeType) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.toast, toast) || other.toast == toast) &&
            (identical(other.nodeId, nodeId) || other.nodeId == nodeId) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.lessonOrder, lessonOrder) ||
                other.lessonOrder == lessonOrder) &&
            (identical(other.segmentName, segmentName) ||
                other.segmentName == segmentName) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.weekName, weekName) ||
                other.weekName == weekName) &&
            (identical(other.weekId, weekId) || other.weekId == weekId) &&
            (identical(other.collectStatus, collectStatus) ||
                other.collectStatus == collectStatus) &&
            (identical(other.introductory, introductory) ||
                other.introductory == introductory) &&
            (identical(other.courseChildType, courseChildType) ||
                other.courseChildType == courseChildType) &&
            (identical(other.studyDesc, studyDesc) ||
                other.studyDesc == studyDesc) &&
            (identical(other.studyStatus, studyStatus) ||
                other.studyStatus == studyStatus) &&
            (identical(other.studyTips, studyTips) ||
                other.studyTips == studyTips) &&
            (identical(other.studyTipsVoice, studyTipsVoice) ||
                other.studyTipsVoice == studyTipsVoice) &&
            (identical(other.lockIcon, lockIcon) ||
                other.lockIcon == lockIcon) &&
            (identical(other.unfinishIcon, unfinishIcon) ||
                other.unfinishIcon == unfinishIcon) &&
            (identical(other.finishIcon, finishIcon) ||
                other.finishIcon == finishIcon) &&
            (identical(other.lockVoice, lockVoice) ||
                other.lockVoice == lockVoice) &&
            (identical(other.unfinishVoice, unfinishVoice) ||
                other.unfinishVoice == unfinishVoice) &&
            (identical(other.finishVoice, finishVoice) ||
                other.finishVoice == finishVoice) &&
            (identical(other.lessonGrade, lessonGrade) ||
                other.lessonGrade == lessonGrade) &&
            (identical(other.lessonGradeResourceUrl, lessonGradeResourceUrl) ||
                other.lessonGradeResourceUrl == lessonGradeResourceUrl) &&
            (identical(other.unFinishedNum, unFinishedNum) ||
                other.unFinishedNum == unFinishedNum) &&
            (identical(other.reviewRoute, reviewRoute) ||
                other.reviewRoute == reviewRoute) &&
            (identical(other.makeupUIExpired, makeupUIExpired) ||
                other.makeupUIExpired == makeupUIExpired) &&
            (identical(other.lessonSubjectType, lessonSubjectType) ||
                other.lessonSubjectType == lessonSubjectType) &&
            (identical(other.subjectNodeIcon, subjectNodeIcon) ||
                other.subjectNodeIcon == subjectNodeIcon) &&
            const DeepCollectionEquality()
                .equals(other._rewardVoList, _rewardVoList) &&
            (identical(other.popupInfo, popupInfo) ||
                other.popupInfo == popupInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        showDateTime,
        showDate,
        focus,
        today,
        gif,
        position,
        unfinishLottieIcon,
        nodeStatus,
        nodeType,
        title,
        route,
        toast,
        nodeId,
        lessonId,
        lessonOrder,
        segmentName,
        segmentId,
        weekName,
        weekId,
        collectStatus,
        introductory,
        courseChildType,
        studyDesc,
        studyStatus,
        studyTips,
        studyTipsVoice,
        lockIcon,
        unfinishIcon,
        finishIcon,
        lockVoice,
        unfinishVoice,
        finishVoice,
        lessonGrade,
        lessonGradeResourceUrl,
        unFinishedNum,
        reviewRoute,
        makeupUIExpired,
        lessonSubjectType,
        subjectNodeIcon,
        const DeepCollectionEquality().hash(_rewardVoList),
        popupInfo
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TimetableNodeListCopyWith<_$_TimetableNodeList> get copyWith =>
      __$$_TimetableNodeListCopyWithImpl<_$_TimetableNodeList>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TimetableNodeListToJson(
      this,
    );
  }
}

abstract class _TimetableNodeList implements TimetableNodeList {
  const factory _TimetableNodeList(
      {final int? showDateTime,
      final String? showDate,
      final bool? focus,
      final bool? today,
      final bool? gif,
      final bool? position,
      final String? unfinishLottieIcon,
      final int? nodeStatus,
      final int? nodeType,
      final String? title,
      final String? route,
      final String? toast,
      final int? nodeId,
      final int? lessonId,
      final int? lessonOrder,
      final String? segmentName,
      final int? segmentId,
      final String? weekName,
      final int? weekId,
      final int? collectStatus,
      final String? introductory,
      final int? courseChildType,
      final String? studyDesc,
      final int? studyStatus,
      final String? studyTips,
      final String? studyTipsVoice,
      final String? lockIcon,
      final String? unfinishIcon,
      final String? finishIcon,
      final String? lockVoice,
      final String? unfinishVoice,
      final String? finishVoice,
      final int? lessonGrade,
      final String? lessonGradeResourceUrl,
      final int? unFinishedNum,
      final String? reviewRoute,
      final bool? makeupUIExpired,
      final int? lessonSubjectType,
      final String? subjectNodeIcon,
      final List<RewardVo>? rewardVoList,
      final PopupInfo? popupInfo}) = _$_TimetableNodeList;

  factory _TimetableNodeList.fromJson(Map<String, dynamic> json) =
      _$_TimetableNodeList.fromJson;

  @override
  int? get showDateTime;
  @override //显示日期 当天0点毫秒时间戳
  String? get showDate;
  @override //显示日期 yyyy-MM-dd
  bool? get focus;
  @override //是否聚焦
  bool? get today;
  @override //是否今天
  bool? get gif;
  @override //是否是gif
  bool? get position;
  @override //用于默认定位
  String? get unfinishLottieIcon;
  @override //gif==true时，卡片图片取该字段，和是否聚焦无关
  int? get nodeStatus;
  @override //节点状态,可用值:0:未解锁,1:已解锁未完成,2:已解锁已完成,3:待补学
  int? get nodeType;
  @override //节点类型,可用值:1:课时
  String? get title;
  @override //标题
  String? get route;
  @override //跳转地址
  String? get toast;
  @override //语音文案
  int? get nodeId;
  @override
  int? get lessonId;
  @override //课时id
  int? get lessonOrder;
  @override //课时排序
  String? get segmentName;
  @override //主题名称
  int? get segmentId;
  @override //主题id
  String? get weekName;
  @override //单元名称
  int? get weekId;
  @override //单元id
  int? get collectStatus;
  @override // 0 :不展示收集物 1： 展示收集物
  String? get introductory;
  @override //单元引导语
  int? get courseChildType;
  @override //是否属于滚动排期 1滚动排期
  String? get studyDesc;
  @override //学习描述(去学习)
  int? get studyStatus;
  @override //学习状态(0不可学, 1可学)
  String? get studyTips;
  @override //不可学提示
  String? get studyTipsVoice;
  @override //不可学提示语音
  ///非课卡片才会处理以下字段(nodeType)
  String? get lockIcon;
  @override // 未解锁图标
  String? get unfinishIcon;
  @override //未完成icon
  String? get finishIcon;
  @override //已完成icon
  String? get lockVoice;
  @override // 未解锁语音
  String? get unfinishVoice;
  @override //未完成语音
  String? get finishVoice;
  @override //已完成语音
  int? get lessonGrade;
  @override
  String? get lessonGradeResourceUrl;
  @override
  int? get unFinishedNum;
  @override
  String? get reviewRoute;
  @override
  bool? get makeupUIExpired;
  @override //补读是否过期true表示过期默认false
  int? get lessonSubjectType;
  @override
  String? get subjectNodeIcon;
  @override
  List<RewardVo>? get rewardVoList;
  @override //阶段奖励数据
  PopupInfo? get popupInfo;
  @override
  @JsonKey(ignore: true)
  _$$_TimetableNodeListCopyWith<_$_TimetableNodeList> get copyWith =>
      throw _privateConstructorUsedError;
}

RenewalTimetableVo _$RenewalTimetableVoFromJson(Map<String, dynamic> json) {
  return _RenewalTimetableVo.fromJson(json);
}

/// @nodoc
mixin _$RenewalTimetableVo {
  String? get userCourseStatus => throw _privateConstructorUsedError;
  String? get buyCourseDesc => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  List<TimetableNodeList>? get renewalTimetableList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RenewalTimetableVoCopyWith<RenewalTimetableVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RenewalTimetableVoCopyWith<$Res> {
  factory $RenewalTimetableVoCopyWith(
          RenewalTimetableVo value, $Res Function(RenewalTimetableVo) then) =
      _$RenewalTimetableVoCopyWithImpl<$Res, RenewalTimetableVo>;
  @useResult
  $Res call(
      {String? userCourseStatus,
      String? buyCourseDesc,
      String? route,
      List<TimetableNodeList>? renewalTimetableList});
}

/// @nodoc
class _$RenewalTimetableVoCopyWithImpl<$Res, $Val extends RenewalTimetableVo>
    implements $RenewalTimetableVoCopyWith<$Res> {
  _$RenewalTimetableVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userCourseStatus = freezed,
    Object? buyCourseDesc = freezed,
    Object? route = freezed,
    Object? renewalTimetableList = freezed,
  }) {
    return _then(_value.copyWith(
      userCourseStatus: freezed == userCourseStatus
          ? _value.userCourseStatus
          : userCourseStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      buyCourseDesc: freezed == buyCourseDesc
          ? _value.buyCourseDesc
          : buyCourseDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      renewalTimetableList: freezed == renewalTimetableList
          ? _value.renewalTimetableList
          : renewalTimetableList // ignore: cast_nullable_to_non_nullable
              as List<TimetableNodeList>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RenewalTimetableVoCopyWith<$Res>
    implements $RenewalTimetableVoCopyWith<$Res> {
  factory _$$_RenewalTimetableVoCopyWith(_$_RenewalTimetableVo value,
          $Res Function(_$_RenewalTimetableVo) then) =
      __$$_RenewalTimetableVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? userCourseStatus,
      String? buyCourseDesc,
      String? route,
      List<TimetableNodeList>? renewalTimetableList});
}

/// @nodoc
class __$$_RenewalTimetableVoCopyWithImpl<$Res>
    extends _$RenewalTimetableVoCopyWithImpl<$Res, _$_RenewalTimetableVo>
    implements _$$_RenewalTimetableVoCopyWith<$Res> {
  __$$_RenewalTimetableVoCopyWithImpl(
      _$_RenewalTimetableVo _value, $Res Function(_$_RenewalTimetableVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userCourseStatus = freezed,
    Object? buyCourseDesc = freezed,
    Object? route = freezed,
    Object? renewalTimetableList = freezed,
  }) {
    return _then(_$_RenewalTimetableVo(
      userCourseStatus: freezed == userCourseStatus
          ? _value.userCourseStatus
          : userCourseStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      buyCourseDesc: freezed == buyCourseDesc
          ? _value.buyCourseDesc
          : buyCourseDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      renewalTimetableList: freezed == renewalTimetableList
          ? _value._renewalTimetableList
          : renewalTimetableList // ignore: cast_nullable_to_non_nullable
              as List<TimetableNodeList>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RenewalTimetableVo implements _RenewalTimetableVo {
  const _$_RenewalTimetableVo(
      {this.userCourseStatus,
      this.buyCourseDesc,
      this.route,
      final List<TimetableNodeList>? renewalTimetableList})
      : _renewalTimetableList = renewalTimetableList;

  factory _$_RenewalTimetableVo.fromJson(Map<String, dynamic> json) =>
      _$$_RenewalTimetableVoFromJson(json);

  @override
  final String? userCourseStatus;
  @override
  final String? buyCourseDesc;
  @override
  final String? route;
  final List<TimetableNodeList>? _renewalTimetableList;
  @override
  List<TimetableNodeList>? get renewalTimetableList {
    final value = _renewalTimetableList;
    if (value == null) return null;
    if (_renewalTimetableList is EqualUnmodifiableListView)
      return _renewalTimetableList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RenewalTimetableVo(userCourseStatus: $userCourseStatus, buyCourseDesc: $buyCourseDesc, route: $route, renewalTimetableList: $renewalTimetableList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RenewalTimetableVo &&
            (identical(other.userCourseStatus, userCourseStatus) ||
                other.userCourseStatus == userCourseStatus) &&
            (identical(other.buyCourseDesc, buyCourseDesc) ||
                other.buyCourseDesc == buyCourseDesc) &&
            (identical(other.route, route) || other.route == route) &&
            const DeepCollectionEquality()
                .equals(other._renewalTimetableList, _renewalTimetableList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userCourseStatus, buyCourseDesc,
      route, const DeepCollectionEquality().hash(_renewalTimetableList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RenewalTimetableVoCopyWith<_$_RenewalTimetableVo> get copyWith =>
      __$$_RenewalTimetableVoCopyWithImpl<_$_RenewalTimetableVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RenewalTimetableVoToJson(
      this,
    );
  }
}

abstract class _RenewalTimetableVo implements RenewalTimetableVo {
  const factory _RenewalTimetableVo(
          {final String? userCourseStatus,
          final String? buyCourseDesc,
          final String? route,
          final List<TimetableNodeList>? renewalTimetableList}) =
      _$_RenewalTimetableVo;

  factory _RenewalTimetableVo.fromJson(Map<String, dynamic> json) =
      _$_RenewalTimetableVo.fromJson;

  @override
  String? get userCourseStatus;
  @override
  String? get buyCourseDesc;
  @override
  String? get route;
  @override
  List<TimetableNodeList>? get renewalTimetableList;
  @override
  @JsonKey(ignore: true)
  _$$_RenewalTimetableVoCopyWith<_$_RenewalTimetableVo> get copyWith =>
      throw _privateConstructorUsedError;
}

PopupInfo _$PopupInfoFromJson(Map<String, dynamic> json) {
  return _PopupInfo.fromJson(json);
}

/// @nodoc
mixin _$PopupInfo {
  String? get popupTitle => throw _privateConstructorUsedError;
  List<String?>? get introduceImageList => throw _privateConstructorUsedError;
  String? get buttonRoute => throw _privateConstructorUsedError;
  String? get buttonText => throw _privateConstructorUsedError;
  String? get closedText => throw _privateConstructorUsedError;
  String? get introduce => throw _privateConstructorUsedError; //引导内容
  String? get actionTitle => throw _privateConstructorUsedError; //引导按钮名称
  String? get actionTip => throw _privateConstructorUsedError; //引导按钮提示
  int? get hasSetting => throw _privateConstructorUsedError; //是否设置
  List<Option>? get options => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PopupInfoCopyWith<PopupInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PopupInfoCopyWith<$Res> {
  factory $PopupInfoCopyWith(PopupInfo value, $Res Function(PopupInfo) then) =
      _$PopupInfoCopyWithImpl<$Res, PopupInfo>;
  @useResult
  $Res call(
      {String? popupTitle,
      List<String?>? introduceImageList,
      String? buttonRoute,
      String? buttonText,
      String? closedText,
      String? introduce,
      String? actionTitle,
      String? actionTip,
      int? hasSetting,
      List<Option>? options});
}

/// @nodoc
class _$PopupInfoCopyWithImpl<$Res, $Val extends PopupInfo>
    implements $PopupInfoCopyWith<$Res> {
  _$PopupInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popupTitle = freezed,
    Object? introduceImageList = freezed,
    Object? buttonRoute = freezed,
    Object? buttonText = freezed,
    Object? closedText = freezed,
    Object? introduce = freezed,
    Object? actionTitle = freezed,
    Object? actionTip = freezed,
    Object? hasSetting = freezed,
    Object? options = freezed,
  }) {
    return _then(_value.copyWith(
      popupTitle: freezed == popupTitle
          ? _value.popupTitle
          : popupTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      introduceImageList: freezed == introduceImageList
          ? _value.introduceImageList
          : introduceImageList // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      buttonRoute: freezed == buttonRoute
          ? _value.buttonRoute
          : buttonRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      closedText: freezed == closedText
          ? _value.closedText
          : closedText // ignore: cast_nullable_to_non_nullable
              as String?,
      introduce: freezed == introduce
          ? _value.introduce
          : introduce // ignore: cast_nullable_to_non_nullable
              as String?,
      actionTitle: freezed == actionTitle
          ? _value.actionTitle
          : actionTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      actionTip: freezed == actionTip
          ? _value.actionTip
          : actionTip // ignore: cast_nullable_to_non_nullable
              as String?,
      hasSetting: freezed == hasSetting
          ? _value.hasSetting
          : hasSetting // ignore: cast_nullable_to_non_nullable
              as int?,
      options: freezed == options
          ? _value.options
          : options // ignore: cast_nullable_to_non_nullable
              as List<Option>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PopupInfoCopyWith<$Res> implements $PopupInfoCopyWith<$Res> {
  factory _$$_PopupInfoCopyWith(
          _$_PopupInfo value, $Res Function(_$_PopupInfo) then) =
      __$$_PopupInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? popupTitle,
      List<String?>? introduceImageList,
      String? buttonRoute,
      String? buttonText,
      String? closedText,
      String? introduce,
      String? actionTitle,
      String? actionTip,
      int? hasSetting,
      List<Option>? options});
}

/// @nodoc
class __$$_PopupInfoCopyWithImpl<$Res>
    extends _$PopupInfoCopyWithImpl<$Res, _$_PopupInfo>
    implements _$$_PopupInfoCopyWith<$Res> {
  __$$_PopupInfoCopyWithImpl(
      _$_PopupInfo _value, $Res Function(_$_PopupInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popupTitle = freezed,
    Object? introduceImageList = freezed,
    Object? buttonRoute = freezed,
    Object? buttonText = freezed,
    Object? closedText = freezed,
    Object? introduce = freezed,
    Object? actionTitle = freezed,
    Object? actionTip = freezed,
    Object? hasSetting = freezed,
    Object? options = freezed,
  }) {
    return _then(_$_PopupInfo(
      popupTitle: freezed == popupTitle
          ? _value.popupTitle
          : popupTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      introduceImageList: freezed == introduceImageList
          ? _value._introduceImageList
          : introduceImageList // ignore: cast_nullable_to_non_nullable
              as List<String?>?,
      buttonRoute: freezed == buttonRoute
          ? _value.buttonRoute
          : buttonRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonText: freezed == buttonText
          ? _value.buttonText
          : buttonText // ignore: cast_nullable_to_non_nullable
              as String?,
      closedText: freezed == closedText
          ? _value.closedText
          : closedText // ignore: cast_nullable_to_non_nullable
              as String?,
      introduce: freezed == introduce
          ? _value.introduce
          : introduce // ignore: cast_nullable_to_non_nullable
              as String?,
      actionTitle: freezed == actionTitle
          ? _value.actionTitle
          : actionTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      actionTip: freezed == actionTip
          ? _value.actionTip
          : actionTip // ignore: cast_nullable_to_non_nullable
              as String?,
      hasSetting: freezed == hasSetting
          ? _value.hasSetting
          : hasSetting // ignore: cast_nullable_to_non_nullable
              as int?,
      options: freezed == options
          ? _value._options
          : options // ignore: cast_nullable_to_non_nullable
              as List<Option>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PopupInfo implements _PopupInfo {
  const _$_PopupInfo(
      {this.popupTitle,
      final List<String?>? introduceImageList,
      this.buttonRoute,
      this.buttonText,
      this.closedText,
      this.introduce,
      this.actionTitle,
      this.actionTip,
      this.hasSetting,
      final List<Option>? options})
      : _introduceImageList = introduceImageList,
        _options = options;

  factory _$_PopupInfo.fromJson(Map<String, dynamic> json) =>
      _$$_PopupInfoFromJson(json);

  @override
  final String? popupTitle;
  final List<String?>? _introduceImageList;
  @override
  List<String?>? get introduceImageList {
    final value = _introduceImageList;
    if (value == null) return null;
    if (_introduceImageList is EqualUnmodifiableListView)
      return _introduceImageList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? buttonRoute;
  @override
  final String? buttonText;
  @override
  final String? closedText;
  @override
  final String? introduce;
//引导内容
  @override
  final String? actionTitle;
//引导按钮名称
  @override
  final String? actionTip;
//引导按钮提示
  @override
  final int? hasSetting;
//是否设置
  final List<Option>? _options;
//是否设置
  @override
  List<Option>? get options {
    final value = _options;
    if (value == null) return null;
    if (_options is EqualUnmodifiableListView) return _options;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PopupInfo(popupTitle: $popupTitle, introduceImageList: $introduceImageList, buttonRoute: $buttonRoute, buttonText: $buttonText, closedText: $closedText, introduce: $introduce, actionTitle: $actionTitle, actionTip: $actionTip, hasSetting: $hasSetting, options: $options)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PopupInfo &&
            (identical(other.popupTitle, popupTitle) ||
                other.popupTitle == popupTitle) &&
            const DeepCollectionEquality()
                .equals(other._introduceImageList, _introduceImageList) &&
            (identical(other.buttonRoute, buttonRoute) ||
                other.buttonRoute == buttonRoute) &&
            (identical(other.buttonText, buttonText) ||
                other.buttonText == buttonText) &&
            (identical(other.closedText, closedText) ||
                other.closedText == closedText) &&
            (identical(other.introduce, introduce) ||
                other.introduce == introduce) &&
            (identical(other.actionTitle, actionTitle) ||
                other.actionTitle == actionTitle) &&
            (identical(other.actionTip, actionTip) ||
                other.actionTip == actionTip) &&
            (identical(other.hasSetting, hasSetting) ||
                other.hasSetting == hasSetting) &&
            const DeepCollectionEquality().equals(other._options, _options));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      popupTitle,
      const DeepCollectionEquality().hash(_introduceImageList),
      buttonRoute,
      buttonText,
      closedText,
      introduce,
      actionTitle,
      actionTip,
      hasSetting,
      const DeepCollectionEquality().hash(_options));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PopupInfoCopyWith<_$_PopupInfo> get copyWith =>
      __$$_PopupInfoCopyWithImpl<_$_PopupInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PopupInfoToJson(
      this,
    );
  }
}

abstract class _PopupInfo implements PopupInfo {
  const factory _PopupInfo(
      {final String? popupTitle,
      final List<String?>? introduceImageList,
      final String? buttonRoute,
      final String? buttonText,
      final String? closedText,
      final String? introduce,
      final String? actionTitle,
      final String? actionTip,
      final int? hasSetting,
      final List<Option>? options}) = _$_PopupInfo;

  factory _PopupInfo.fromJson(Map<String, dynamic> json) =
      _$_PopupInfo.fromJson;

  @override
  String? get popupTitle;
  @override
  List<String?>? get introduceImageList;
  @override
  String? get buttonRoute;
  @override
  String? get buttonText;
  @override
  String? get closedText;
  @override
  String? get introduce;
  @override //引导内容
  String? get actionTitle;
  @override //引导按钮名称
  String? get actionTip;
  @override //引导按钮提示
  int? get hasSetting;
  @override //是否设置
  List<Option>? get options;
  @override
  @JsonKey(ignore: true)
  _$$_PopupInfoCopyWith<_$_PopupInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

Option _$OptionFromJson(Map<String, dynamic> json) {
  return _Option.fromJson(json);
}

/// @nodoc
mixin _$Option {
  String? get navTitle => throw _privateConstructorUsedError; //星期标题
  int? get navKey => throw _privateConstructorUsedError; //女娲星期序号
  int? get hasChoice => throw _privateConstructorUsedError; //是否选择
  String? get contentImg => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OptionCopyWith<Option> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OptionCopyWith<$Res> {
  factory $OptionCopyWith(Option value, $Res Function(Option) then) =
      _$OptionCopyWithImpl<$Res, Option>;
  @useResult
  $Res call(
      {String? navTitle, int? navKey, int? hasChoice, String? contentImg});
}

/// @nodoc
class _$OptionCopyWithImpl<$Res, $Val extends Option>
    implements $OptionCopyWith<$Res> {
  _$OptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? navTitle = freezed,
    Object? navKey = freezed,
    Object? hasChoice = freezed,
    Object? contentImg = freezed,
  }) {
    return _then(_value.copyWith(
      navTitle: freezed == navTitle
          ? _value.navTitle
          : navTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      navKey: freezed == navKey
          ? _value.navKey
          : navKey // ignore: cast_nullable_to_non_nullable
              as int?,
      hasChoice: freezed == hasChoice
          ? _value.hasChoice
          : hasChoice // ignore: cast_nullable_to_non_nullable
              as int?,
      contentImg: freezed == contentImg
          ? _value.contentImg
          : contentImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_OptionCopyWith<$Res> implements $OptionCopyWith<$Res> {
  factory _$$_OptionCopyWith(_$_Option value, $Res Function(_$_Option) then) =
      __$$_OptionCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? navTitle, int? navKey, int? hasChoice, String? contentImg});
}

/// @nodoc
class __$$_OptionCopyWithImpl<$Res>
    extends _$OptionCopyWithImpl<$Res, _$_Option>
    implements _$$_OptionCopyWith<$Res> {
  __$$_OptionCopyWithImpl(_$_Option _value, $Res Function(_$_Option) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? navTitle = freezed,
    Object? navKey = freezed,
    Object? hasChoice = freezed,
    Object? contentImg = freezed,
  }) {
    return _then(_$_Option(
      navTitle: freezed == navTitle
          ? _value.navTitle
          : navTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      navKey: freezed == navKey
          ? _value.navKey
          : navKey // ignore: cast_nullable_to_non_nullable
              as int?,
      hasChoice: freezed == hasChoice
          ? _value.hasChoice
          : hasChoice // ignore: cast_nullable_to_non_nullable
              as int?,
      contentImg: freezed == contentImg
          ? _value.contentImg
          : contentImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Option implements _Option {
  const _$_Option(
      {this.navTitle, this.navKey, this.hasChoice, this.contentImg});

  factory _$_Option.fromJson(Map<String, dynamic> json) =>
      _$$_OptionFromJson(json);

  @override
  final String? navTitle;
//星期标题
  @override
  final int? navKey;
//女娲星期序号
  @override
  final int? hasChoice;
//是否选择
  @override
  final String? contentImg;

  @override
  String toString() {
    return 'Option(navTitle: $navTitle, navKey: $navKey, hasChoice: $hasChoice, contentImg: $contentImg)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Option &&
            (identical(other.navTitle, navTitle) ||
                other.navTitle == navTitle) &&
            (identical(other.navKey, navKey) || other.navKey == navKey) &&
            (identical(other.hasChoice, hasChoice) ||
                other.hasChoice == hasChoice) &&
            (identical(other.contentImg, contentImg) ||
                other.contentImg == contentImg));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, navTitle, navKey, hasChoice, contentImg);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_OptionCopyWith<_$_Option> get copyWith =>
      __$$_OptionCopyWithImpl<_$_Option>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_OptionToJson(
      this,
    );
  }
}

abstract class _Option implements Option {
  const factory _Option(
      {final String? navTitle,
      final int? navKey,
      final int? hasChoice,
      final String? contentImg}) = _$_Option;

  factory _Option.fromJson(Map<String, dynamic> json) = _$_Option.fromJson;

  @override
  String? get navTitle;
  @override //星期标题
  int? get navKey;
  @override //女娲星期序号
  int? get hasChoice;
  @override //是否选择
  String? get contentImg;
  @override
  @JsonKey(ignore: true)
  _$$_OptionCopyWith<_$_Option> get copyWith =>
      throw _privateConstructorUsedError;
}

RewardVo _$RewardVoFromJson(Map<String, dynamic> json) {
  return _RewardVo.fromJson(json);
}

/// @nodoc
mixin _$RewardVo {
  int? get rewardCount => throw _privateConstructorUsedError;
  int? get rewardType => throw _privateConstructorUsedError;
  int? get actionType => throw _privateConstructorUsedError;
  String? get actionTargetId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RewardVoCopyWith<RewardVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardVoCopyWith<$Res> {
  factory $RewardVoCopyWith(RewardVo value, $Res Function(RewardVo) then) =
      _$RewardVoCopyWithImpl<$Res, RewardVo>;
  @useResult
  $Res call(
      {int? rewardCount,
      int? rewardType,
      int? actionType,
      String? actionTargetId});
}

/// @nodoc
class _$RewardVoCopyWithImpl<$Res, $Val extends RewardVo>
    implements $RewardVoCopyWith<$Res> {
  _$RewardVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardCount = freezed,
    Object? rewardType = freezed,
    Object? actionType = freezed,
    Object? actionTargetId = freezed,
  }) {
    return _then(_value.copyWith(
      rewardCount: freezed == rewardCount
          ? _value.rewardCount
          : rewardCount // ignore: cast_nullable_to_non_nullable
              as int?,
      rewardType: freezed == rewardType
          ? _value.rewardType
          : rewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      actionType: freezed == actionType
          ? _value.actionType
          : actionType // ignore: cast_nullable_to_non_nullable
              as int?,
      actionTargetId: freezed == actionTargetId
          ? _value.actionTargetId
          : actionTargetId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RewardVoCopyWith<$Res> implements $RewardVoCopyWith<$Res> {
  factory _$$_RewardVoCopyWith(
          _$_RewardVo value, $Res Function(_$_RewardVo) then) =
      __$$_RewardVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? rewardCount,
      int? rewardType,
      int? actionType,
      String? actionTargetId});
}

/// @nodoc
class __$$_RewardVoCopyWithImpl<$Res>
    extends _$RewardVoCopyWithImpl<$Res, _$_RewardVo>
    implements _$$_RewardVoCopyWith<$Res> {
  __$$_RewardVoCopyWithImpl(
      _$_RewardVo _value, $Res Function(_$_RewardVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardCount = freezed,
    Object? rewardType = freezed,
    Object? actionType = freezed,
    Object? actionTargetId = freezed,
  }) {
    return _then(_$_RewardVo(
      rewardCount: freezed == rewardCount
          ? _value.rewardCount
          : rewardCount // ignore: cast_nullable_to_non_nullable
              as int?,
      rewardType: freezed == rewardType
          ? _value.rewardType
          : rewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      actionType: freezed == actionType
          ? _value.actionType
          : actionType // ignore: cast_nullable_to_non_nullable
              as int?,
      actionTargetId: freezed == actionTargetId
          ? _value.actionTargetId
          : actionTargetId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RewardVo implements _RewardVo {
  const _$_RewardVo(
      {this.rewardCount,
      this.rewardType,
      this.actionType,
      this.actionTargetId});

  factory _$_RewardVo.fromJson(Map<String, dynamic> json) =>
      _$$_RewardVoFromJson(json);

  @override
  final int? rewardCount;
  @override
  final int? rewardType;
  @override
  final int? actionType;
  @override
  final String? actionTargetId;

  @override
  String toString() {
    return 'RewardVo(rewardCount: $rewardCount, rewardType: $rewardType, actionType: $actionType, actionTargetId: $actionTargetId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RewardVo &&
            (identical(other.rewardCount, rewardCount) ||
                other.rewardCount == rewardCount) &&
            (identical(other.rewardType, rewardType) ||
                other.rewardType == rewardType) &&
            (identical(other.actionType, actionType) ||
                other.actionType == actionType) &&
            (identical(other.actionTargetId, actionTargetId) ||
                other.actionTargetId == actionTargetId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, rewardCount, rewardType, actionType, actionTargetId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RewardVoCopyWith<_$_RewardVo> get copyWith =>
      __$$_RewardVoCopyWithImpl<_$_RewardVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RewardVoToJson(
      this,
    );
  }
}

abstract class _RewardVo implements RewardVo {
  const factory _RewardVo(
      {final int? rewardCount,
      final int? rewardType,
      final int? actionType,
      final String? actionTargetId}) = _$_RewardVo;

  factory _RewardVo.fromJson(Map<String, dynamic> json) = _$_RewardVo.fromJson;

  @override
  int? get rewardCount;
  @override
  int? get rewardType;
  @override
  int? get actionType;
  @override
  String? get actionTargetId;
  @override
  @JsonKey(ignore: true)
  _$$_RewardVoCopyWith<_$_RewardVo> get copyWith =>
      throw _privateConstructorUsedError;
}
