// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_study_sea_map_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_TeamStudySeaMapModel _$$_TeamStudySeaMapModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudySeaMapModel(
      title: json['title'] as String?,
      barColor: json['barColor'] as String?,
      bgWidth: (json['bgWidth'] as num?)?.toDouble(),
      bgHeight: (json['bgHeight'] as num?)?.toDouble(),
      bgImages: (json['bgImages'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudySeaMapPointRes.fromJson(e as Map<String, dynamic>))
          .toList(),
      items: (json['items'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudySeaMapPointRes.fromJson(e as Map<String, dynamic>))
          .toList(),
      points: (json['points'] as List<dynamic>?)
          ?.map((e) => TeamStudySeaMapPoint.fromJson(e as Map<String, dynamic>))
          .toList(),
      pointImg: (json['pointImg'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudySeaMapPointRes.fromJson(e as Map<String, dynamic>))
          .toList(),
      pointSpine: (json['pointSpine'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudySeaMapPointRes.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeamStudySeaMapModelToJson(
        _$_TeamStudySeaMapModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'barColor': instance.barColor,
      'bgWidth': instance.bgWidth,
      'bgHeight': instance.bgHeight,
      'bgImages': instance.bgImages,
      'items': instance.items,
      'points': instance.points,
      'pointImg': instance.pointImg,
      'pointSpine': instance.pointSpine,
    };

_$_TeamStudySeaMapPoint _$$_TeamStudySeaMapPointFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudySeaMapPoint(
      id: json['id'] as int?,
      rect: json['rect'] == null
          ? null
          : TeamStudySeaMapRect.fromJson(json['rect'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_TeamStudySeaMapPointToJson(
        _$_TeamStudySeaMapPoint instance) =>
    <String, dynamic>{
      'id': instance.id,
      'rect': instance.rect,
    };

_$_TeamStudySeaMapPointRes _$$_TeamStudySeaMapPointResFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudySeaMapPointRes(
      type: json['type'] as String?,
      image: json['image'] as String?,
      skeletonFile: json['skeletonFile'] as String?,
      atlasFile: json['atlasFile'] as String?,
      animationName: json['animationName'] as String?,
      rect: json['rect'] == null
          ? null
          : TeamStudySeaMapRect.fromJson(json['rect'] as Map<String, dynamic>),
      resType:
          $enumDecodeNullable(_$TeamStudySeaMapResTypeEnumMap, json['resType']),
    );

Map<String, dynamic> _$$_TeamStudySeaMapPointResToJson(
        _$_TeamStudySeaMapPointRes instance) =>
    <String, dynamic>{
      'type': instance.type,
      'image': instance.image,
      'skeletonFile': instance.skeletonFile,
      'atlasFile': instance.atlasFile,
      'animationName': instance.animationName,
      'rect': instance.rect,
      'resType': _$TeamStudySeaMapResTypeEnumMap[instance.resType],
    };

const _$TeamStudySeaMapResTypeEnumMap = {
  TeamStudySeaMapResType.image: 'image',
  TeamStudySeaMapResType.spine: 'spine',
};

_$_TeamStudySeaMapRect _$$_TeamStudySeaMapRectFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudySeaMapRect(
      x: (json['x'] as num?)?.toDouble(),
      y: (json['y'] as num?)?.toDouble(),
      w: (json['w'] as num?)?.toDouble(),
      h: (json['h'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$_TeamStudySeaMapRectToJson(
        _$_TeamStudySeaMapRect instance) =>
    <String, dynamic>{
      'x': instance.x,
      'y': instance.y,
      'w': instance.w,
      'h': instance.h,
    };

_$_TeamStudySeaMapPointState _$$_TeamStudySeaMapPointStateFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudySeaMapPointState(
      teamId: json['teamId'] as int?,
      nodeId: json['nodeId'] as int?,
      number: json['number'] as int?,
      isFinish: json['isFinish'] as int?,
      isReceiveReward: json['isReceiveReward'] as int?,
      receiveTime: json['receiveTime'] as int?,
      nodeRewardType: $enumDecodeNullable(
          _$TeamStudySeaMapRewardTypeEnumMap, json['nodeRewardType']),
      rewardList: (json['rewardList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudySeaMapPointReward.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeamStudySeaMapPointStateToJson(
        _$_TeamStudySeaMapPointState instance) =>
    <String, dynamic>{
      'teamId': instance.teamId,
      'nodeId': instance.nodeId,
      'number': instance.number,
      'isFinish': instance.isFinish,
      'isReceiveReward': instance.isReceiveReward,
      'receiveTime': instance.receiveTime,
      'nodeRewardType':
          _$TeamStudySeaMapRewardTypeEnumMap[instance.nodeRewardType],
      'rewardList': instance.rewardList,
    };

const _$TeamStudySeaMapRewardTypeEnumMap = {
  TeamStudySeaMapRewardType.none: 0,
  TeamStudySeaMapRewardType.reward: 1,
  TeamStudySeaMapRewardType.photo: 2,
  TeamStudySeaMapRewardType.rewardHigh: 3,
};

_$_TeamStudySeaMapPointReward _$$_TeamStudySeaMapPointRewardFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudySeaMapPointReward(
      id: json['id'] as int?,
      type: json['type'] as int?,
      name: json['name'] as String?,
      desc: json['desc'] as String?,
      img: json['img'] as String?,
    );

Map<String, dynamic> _$$_TeamStudySeaMapPointRewardToJson(
        _$_TeamStudySeaMapPointReward instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'name': instance.name,
      'desc': instance.desc,
      'img': instance.img,
    };
