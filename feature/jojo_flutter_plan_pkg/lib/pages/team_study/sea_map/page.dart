import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../../../../generated/l10n.dart';
import 'controller.dart';
import 'state.dart';
import 'view.dart';

class TeamStudySeaMapPage extends BasePage {
  final int? subjectType;

  const TeamStudySeaMapPage({Key? key, this.subjectType}) : super(key: key);

  @override
  State<StatefulWidget> createState() => TeamStudySeaMapPageState();
}

class TeamStudySeaMapPageState extends BaseState<TeamStudySeaMapPage> {
  late TeamStudySeaMapController _controller;

  @override
  void initState() {
    super.initState();
    _controller =
        TeamStudySeaMapController.withDefault(subjectType: widget.subjectType);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _controller,
      child: Builder(
        builder: (innerContext) {
          return BlocBuilder<TeamStudySeaMapController, TeamStudySeaMapState>(
            builder: (context, state) {
              return JoJoPageLoadingV25(
                scene: PageScene.common,
                hideProgress: true,
                status: state.pageStatus,
                child: TeamStudySeaMapView(
                  state: state,
                ),
                retry: () {
                  _controller.startLoad();
                },
              );
            },
          );
        },
      ),
    );
  }
}
