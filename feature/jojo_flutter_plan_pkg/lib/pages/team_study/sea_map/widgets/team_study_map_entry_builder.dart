import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

import '../../../../common/config/config.dart';

class TeamStudyMapEntry extends StatelessWidget {
  final int current;
  final int total;
  final bool showReward;
  final int? rewardType;
  final GestureTapCallback? onTap;

  const TeamStudyMapEntry({
    super.key,
    required this.current,
    required this.total,
    required this.showReward,
    this.rewardType,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.rdp),
        border: Border.all(
          color: HexColor("#E0DFDF"),
          width: 1.rdp,
        ),
      ),
      child: Row(
        children: [
          SizedBox(width: 14.rdp),
          ImageAssetWeb(
            assetName: AssetsImg.TEAM_STUDY_MAP_ICON,
            width: 48.rdp,
            height: 48.rdp,
            package: Config.package,
          ),
          SizedBox(width: 4.rdp),
          Text(
            '$current/$total',
            style: TextStyle(
              color: context.appColors.jColorGray6,
              fontSize: 20.rdp,
              fontWeight: FontWeight.w500,
              fontFamily: 'PingFang SC',
              package: Config.package,
            ),
          ),
          Expanded(child: Container()),
          showReward ? getRewardWidget(context) : getNormalWidget(context),
        ],
      ),
    );
  }

  Widget getRewardWidget(BuildContext context) {
    var type = rewardType ?? 0;
    String icon = AssetsImg.TEAM_STUDY_REWARD;
    if (type == 2) {
      icon = AssetsImg.TEAM_STUDY_PHOTO;
    } else if (type == 3) {
      icon = AssetsImg.TEAM_STUDY_HIGH_REWARD;
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.white,
        child: Row(
          children: [
            ImageAssetWeb(
              assetName: icon,
              width: 36.rdp,
              height: 36.rdp,
              package: Config.package,
            ),
            Text(
              S.of(context).getReward,
              style: TextStyle(
                color: context.appColors.jColorYellow5,
                fontSize: 15.rdp,
                fontWeight: FontWeight.w400,
                fontFamily: 'PingFang SC',
                package: Config.package,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 12.rdp),
                Container(
                  color: Colors.white,
                  width: 16.rdp,
                  alignment: Alignment.centerRight,
                  child: Container(
                    width: 8.rdp,
                    height: 8.rdp,
                    decoration: BoxDecoration(
                      color: HexColor('#FF9045'),
                      borderRadius: BorderRadius.circular(8.rdp),
                    ),
                  ),
                ),
                SizedBox(height: 8.rdp),
                ImageAssetWeb(
                  assetName: AssetsImg.TEAM_STUDY_ARROW_YELLOW,
                  width: 14.rdp,
                  height: 14.rdp,
                  package: Config.package,
                ),
              ],
            ),
            SizedBox(width: 12.rdp),
          ],
        ),
      ),
    );
  }

  Widget getNormalWidget(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.white,
        child: Row(
          children: [
            Text(
              S.of(context).goToMap,
              style: TextStyle(
                color: context.appColors.jColorGray5,
                fontSize: 14.rdp,
                fontWeight: FontWeight.w400,
                fontFamily: 'PingFang SC',
                package: Config.package,
              ),
            ),
            ImageAssetWeb(
              assetName: AssetsImg.TEAM_STUDY_ARROW_GRAY,
              width: 14.rdp,
              height: 14.rdp,
              package: Config.package,
            ),
            SizedBox(width: 14.rdp, height: 48.rdp),
          ],
        ),
      ),
    );
  }
}
