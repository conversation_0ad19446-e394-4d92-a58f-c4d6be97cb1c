import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_download.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/utils/unified_exception_util.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/utils/file_util.dart';
import 'package:path/path.dart' as p;

import '../../../service/team_study_activity_api.dart';
import '../../team_study_activity_page/models/team_study_activity_model.dart';
import '../service/team_study_map_api.dart';
import 'model/team_study_sea_map_model.dart';
import 'state.dart';

class TeamStudySeaMapController extends Cubit<TeamStudySeaMapState> {
  static const String tag = "TeamStudySeaMap";
  final TeamStudyActivityApi api;
  final int? subjectType;

  String mapResFolderPath = '';
  String mapConfigPath = '';

  late TeamStudySeaMapModel? mapConfig;
  late TeamStudyActivityModel? mapDetails;

  final JoJoResourceManager _resourceManager = JoJoResourceManager();

  TeamStudySeaMapController({required this.api, this.subjectType})
      : super(TeamStudySeaMapState(PageStatus.loading));

  TeamStudySeaMapController.withDefault({this.subjectType})
      : api = teamStudyApi,
        super(TeamStudySeaMapState(PageStatus.loading)) {
    startLoad();
  }

  startLoad() async {
    emit(state.copyWith(PageStatus.loading));

    var model = await requestTeamStudyMapDetails();
    if (model != null) {
      mapDetails = model;
      downloadMapRes(mapDetails?.mapResource ?? "");
    }
  }

  downloadMapRes(String resourceUrl) async {
    await downloadConfig(
      resourceUrl,
      successListener: (res) {
        downloadComplete();
      },
      failListener: (error) {
        handleError(error, true);
      },
    );
  }

  downloadComplete() async {
    mapConfigPath = finMapResConfigJson(mapResFolderPath) ?? "";
    mapConfig = await getConfigModel(mapConfigPath);
    showMap();
  }

  showMap() {
    if (mapConfig != null) {
      List<TeamStudySeaMapPointState> mapNodeList =
          mapDetails?.mapNodeList ?? [];

      var bgHeight = mapConfig?.bgHeight ?? 1000;
      var pointsCount = mapConfig?.points?.length ?? 0;

      // 节点图层分组，每组对应的高度和节点数
      var allPointHeights = state.getAllPointLayerGroupHeights();
      var pointHeights = state.getLayerGroupHeights(bgHeight, allPointHeights);
      var allPointCounts = state.getAllPointLayerGroupPointCounts();
      var pointCounts =
          state.getLayerGroupPointCounts(pointsCount, allPointCounts);

      // 奖励图层分组，每组对应的高度和节点数
      var allRewardHeights = state.getAllRewardLayerGroupHeights();
      var rewardHeights =
          state.getLayerGroupHeights(bgHeight, allRewardHeights);
      var allRewardPointCounts = state.getAllRewardLayerGroupPointCounts();
      var rewardPointCounts =
          state.getLayerGroupPointCounts(pointsCount, allRewardPointCounts);

      emit(state.copyWith(
        PageStatus.success,
        mapConfig: mapConfig,
        mapNodeList: mapNodeList,
        pointLayerGroupHeights: pointHeights,
        pointLayerGroupPointCounts: pointCounts,
        rewardLayerGroupHeights: rewardHeights,
        rewardLayerGroupPointCounts: rewardPointCounts,
      ));
    } else {
      emit(state.copyWith(PageStatus.error));
    }
  }

  downloadConfig(
    String resUrl, {
    Function(Map<String, String>)? successListener,
    Function(NativeDownloadError)? failListener,
  }) async {
    await _resourceManager.downloadUrl([resUrl], isNeedCancel: false,
        successListener: (urlMap) async {
      String localPath = urlMap[resUrl] ?? "";
      if (localPath.isNotEmpty) {
        String dirPath = await unzip(localPath);
        await removeUselessFilesAndDirs(Directory(dirPath));
        mapResFolderPath = dirPath;
        if (Directory(dirPath).existsSync()) {
          l.i(tag, "下载地图资源成功 url: $resUrl localPath: $localPath");
        } else {
          l.e(tag, "下载地图资源失败 url: $resUrl localPath: $localPath");
        }
      }
      successListener?.call(urlMap);
    }, failListener: (error) {
      l.e(tag, "下载地图资源失败 error: $error");
      failListener?.call(NativeDownloadError.convert(error));
    });
  }

  /// 查找 config.json 文件
  String? finMapResConfigJson(String directory) {
    try {
      File? jsonFile = findFilesByName(directory, "config.json");
      if (jsonFile != null) {
        return jsonFile.path;
      }
    } catch (e) {
      l.w(tag, "查找 config.json 时异常：$e");
    }
    return null;
  }

  /// 读取 config.json 文件并解析为 TeamStudySeaMapModel
  Future<TeamStudySeaMapModel?> getConfigModel(String? filePath) async {
    if (filePath != null && File(filePath).existsSync()) {
      try {
        String jsonData = await File(filePath).readAsString();
        var data = json.decode(jsonData);
        var model = TeamStudySeaMapModel.fromJson(data['map']);
        return model;
      } catch (e, stackTrace) {
        l.e(tag, "读取 json 失败 $stackTrace");
      }
    }
    return null;
  }

  openReward(
    TeamStudySeaMapPoint point,
    TeamStudySeaMapPointState pointState,
  ) {
    var rewardState = state.getRewardState(pointState);
    if (rewardState == TeamStudySeaMapRewardState.openable) {
      var mapNodeList = state.mapNodeList ?? [];
      for (var i = 0; i < mapNodeList.length; i++) {
        var node = mapNodeList[i];
        if (node.nodeId == pointState.nodeId) {
          node.isReceiveReward = 1;
          print(
              'weich map update ${node.number} isReceiveReward ${node.isReceiveReward}');
          // 打开宝箱
          // requestOpenReward(node.teamId ?? 0, node.nodeId ?? 0);
        }
      }

      emit(state.copyWith(
        PageStatus.success,
        mapNodeList: mapNodeList,
      ));
    }
  }

  Future<TeamStudyActivityModel?> requestTeamStudyMapDetails() async {
    try {
      var model = await api.getTeamStudyActivityInfo(subjectType, 'map');
      return model;
    } catch (e) {
      handleError(e, true);
    }
    return Future.value(null);
  }

  requestOpenReward(int teamId, int nodeId) async {
    try {
      var model = await api.getMapRewards(teamId, 2, null, nodeId);
      return model;
    } catch (e) {
      handleError(e, false);
    }
    return Future.value(null);
  }

  handleError(dynamic e, bool refresh) {
    if (e is Exception) {
      UnifiedExceptionData data = UnifiedExceptionUtil.exceptionHandler(e);
      final exception = NativeDownloadError.convert(data);
      if (refresh) {
        emit(state.copyWith(
          PageStatus.error,
          exception: exception,
        ));
      }
    } else {
      handleError(Exception(e), false);
    }
  }

  @override
  Future<void> close() {
    _resourceManager.cancelDownload();
    return super.close();
  }

  String getResFilePath(String path) {
    return '$mapResFolderPath/$path';
  }
}
