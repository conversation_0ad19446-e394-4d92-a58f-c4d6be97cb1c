import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/common/dio/use.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:retrofit/http.dart';

import '../model/team_study_detail_model.dart';

part 'team_study_map_api.g.dart';

@RestApi()
abstract class TeamStudySeaMapApi {
  factory TeamStudySeaMapApi(Dio dio, {String baseUrl}) = _TeamStudySeaMapApi;

  /// 组队/小队详情
  @GET("/api/pagani/study-teams/{uid}")
  Future<TeamStudyDetailModel> getTeamStudyDetail(
    @Path('uid') int? uid, // uid固定传0，表示当前用户
    @Query('subjectType') int? subjectType, // subjectType科目类型，非必传，默认阅读
    @Query('scene') String? scene, // scene场景页面定义(map地图页；team小队详情)
  );

  /// 能量收集/奖励领取
  @POST("/api/pagani/study-teams/{teamId}/rewards")
  Future<void> getMapRewards(
    @Path('teamId') int teamId, // 小队id
    @Path('type') int type, // 类型(1能量2奖励)
    @Query('memberIds') int memberIds, // 成员id(type=1)
    @Query('nodeId') int nodeId, // 节点id(type=2, 引导奖励传0)
  );
}

final proTeamStudySeaMapApi =
    TeamStudySeaMapApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);
