// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'team_study_detail_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TeamStudyDetailModel _$TeamStudyDetailModelFromJson(Map<String, dynamic> json) {
  return _TeamStudyDetailModel.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyDetailModel {
  int? get teamId => throw _privateConstructorUsedError;
  int? get status =>
      throw _privateConstructorUsedError; // 活动状态(0未解锁,1组队中,2进行中,3已结束)
  int? get startTime => throw _privateConstructorUsedError;
  int? get endTime => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  String? get resource => throw _privateConstructorUsedError; // 页面资源
  String? get mapResource => throw _privateConstructorUsedError; // 地图页面资源
  int? get current => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;
  int? get isUnReceiveReward =>
      throw _privateConstructorUsedError; // 是否有未领取奖励(1是0否)
  int? get nodeRewardType =>
      throw _privateConstructorUsedError; // 类型(0无奖励,1宝箱,2照片,3终极宝箱)
  List<TeamStudyActivityRewardModel>? get joinActivityRewardList =>
      throw _privateConstructorUsedError; // 参加活动奖励列表
  List<TeamStudyMemberModel>? get teamMemberList =>
      throw _privateConstructorUsedError;
  List<TeamStudySeaMapPointState>? get mapNodeList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyDetailModelCopyWith<TeamStudyDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyDetailModelCopyWith<$Res> {
  factory $TeamStudyDetailModelCopyWith(TeamStudyDetailModel value,
          $Res Function(TeamStudyDetailModel) then) =
      _$TeamStudyDetailModelCopyWithImpl<$Res, TeamStudyDetailModel>;
  @useResult
  $Res call(
      {int? teamId,
      int? status,
      int? startTime,
      int? endTime,
      int? subjectType,
      String? subjectName,
      String? resource,
      String? mapResource,
      int? current,
      int? total,
      int? isUnReceiveReward,
      int? nodeRewardType,
      List<TeamStudyActivityRewardModel>? joinActivityRewardList,
      List<TeamStudyMemberModel>? teamMemberList,
      List<TeamStudySeaMapPointState>? mapNodeList});
}

/// @nodoc
class _$TeamStudyDetailModelCopyWithImpl<$Res,
        $Val extends TeamStudyDetailModel>
    implements $TeamStudyDetailModelCopyWith<$Res> {
  _$TeamStudyDetailModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? status = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? resource = freezed,
    Object? mapResource = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? isUnReceiveReward = freezed,
    Object? nodeRewardType = freezed,
    Object? joinActivityRewardList = freezed,
    Object? teamMemberList = freezed,
    Object? mapNodeList = freezed,
  }) {
    return _then(_value.copyWith(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      mapResource: freezed == mapResource
          ? _value.mapResource
          : mapResource // ignore: cast_nullable_to_non_nullable
              as String?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      isUnReceiveReward: freezed == isUnReceiveReward
          ? _value.isUnReceiveReward
          : isUnReceiveReward // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeRewardType: freezed == nodeRewardType
          ? _value.nodeRewardType
          : nodeRewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      joinActivityRewardList: freezed == joinActivityRewardList
          ? _value.joinActivityRewardList
          : joinActivityRewardList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyActivityRewardModel>?,
      teamMemberList: freezed == teamMemberList
          ? _value.teamMemberList
          : teamMemberList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyMemberModel>?,
      mapNodeList: freezed == mapNodeList
          ? _value.mapNodeList
          : mapNodeList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointState>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyDetailModelCopyWith<$Res>
    implements $TeamStudyDetailModelCopyWith<$Res> {
  factory _$$_TeamStudyDetailModelCopyWith(_$_TeamStudyDetailModel value,
          $Res Function(_$_TeamStudyDetailModel) then) =
      __$$_TeamStudyDetailModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? teamId,
      int? status,
      int? startTime,
      int? endTime,
      int? subjectType,
      String? subjectName,
      String? resource,
      String? mapResource,
      int? current,
      int? total,
      int? isUnReceiveReward,
      int? nodeRewardType,
      List<TeamStudyActivityRewardModel>? joinActivityRewardList,
      List<TeamStudyMemberModel>? teamMemberList,
      List<TeamStudySeaMapPointState>? mapNodeList});
}

/// @nodoc
class __$$_TeamStudyDetailModelCopyWithImpl<$Res>
    extends _$TeamStudyDetailModelCopyWithImpl<$Res, _$_TeamStudyDetailModel>
    implements _$$_TeamStudyDetailModelCopyWith<$Res> {
  __$$_TeamStudyDetailModelCopyWithImpl(_$_TeamStudyDetailModel _value,
      $Res Function(_$_TeamStudyDetailModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? status = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? resource = freezed,
    Object? mapResource = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? isUnReceiveReward = freezed,
    Object? nodeRewardType = freezed,
    Object? joinActivityRewardList = freezed,
    Object? teamMemberList = freezed,
    Object? mapNodeList = freezed,
  }) {
    return _then(_$_TeamStudyDetailModel(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      mapResource: freezed == mapResource
          ? _value.mapResource
          : mapResource // ignore: cast_nullable_to_non_nullable
              as String?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      isUnReceiveReward: freezed == isUnReceiveReward
          ? _value.isUnReceiveReward
          : isUnReceiveReward // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeRewardType: freezed == nodeRewardType
          ? _value.nodeRewardType
          : nodeRewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      joinActivityRewardList: freezed == joinActivityRewardList
          ? _value._joinActivityRewardList
          : joinActivityRewardList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyActivityRewardModel>?,
      teamMemberList: freezed == teamMemberList
          ? _value._teamMemberList
          : teamMemberList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyMemberModel>?,
      mapNodeList: freezed == mapNodeList
          ? _value._mapNodeList
          : mapNodeList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointState>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyDetailModel implements _TeamStudyDetailModel {
  _$_TeamStudyDetailModel(
      {this.teamId,
      this.status,
      this.startTime,
      this.endTime,
      this.subjectType,
      this.subjectName,
      this.resource,
      this.mapResource,
      this.current,
      this.total,
      this.isUnReceiveReward,
      this.nodeRewardType,
      final List<TeamStudyActivityRewardModel>? joinActivityRewardList,
      final List<TeamStudyMemberModel>? teamMemberList,
      final List<TeamStudySeaMapPointState>? mapNodeList})
      : _joinActivityRewardList = joinActivityRewardList,
        _teamMemberList = teamMemberList,
        _mapNodeList = mapNodeList;

  factory _$_TeamStudyDetailModel.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyDetailModelFromJson(json);

  @override
  final int? teamId;
  @override
  final int? status;
// 活动状态(0未解锁,1组队中,2进行中,3已结束)
  @override
  final int? startTime;
  @override
  final int? endTime;
  @override
  final int? subjectType;
  @override
  final String? subjectName;
  @override
  final String? resource;
// 页面资源
  @override
  final String? mapResource;
// 地图页面资源
  @override
  final int? current;
  @override
  final int? total;
  @override
  final int? isUnReceiveReward;
// 是否有未领取奖励(1是0否)
  @override
  final int? nodeRewardType;
// 类型(0无奖励,1宝箱,2照片,3终极宝箱)
  final List<TeamStudyActivityRewardModel>? _joinActivityRewardList;
// 类型(0无奖励,1宝箱,2照片,3终极宝箱)
  @override
  List<TeamStudyActivityRewardModel>? get joinActivityRewardList {
    final value = _joinActivityRewardList;
    if (value == null) return null;
    if (_joinActivityRewardList is EqualUnmodifiableListView)
      return _joinActivityRewardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 参加活动奖励列表
  final List<TeamStudyMemberModel>? _teamMemberList;
// 参加活动奖励列表
  @override
  List<TeamStudyMemberModel>? get teamMemberList {
    final value = _teamMemberList;
    if (value == null) return null;
    if (_teamMemberList is EqualUnmodifiableListView) return _teamMemberList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TeamStudySeaMapPointState>? _mapNodeList;
  @override
  List<TeamStudySeaMapPointState>? get mapNodeList {
    final value = _mapNodeList;
    if (value == null) return null;
    if (_mapNodeList is EqualUnmodifiableListView) return _mapNodeList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TeamStudyDetailModel(teamId: $teamId, status: $status, startTime: $startTime, endTime: $endTime, subjectType: $subjectType, subjectName: $subjectName, resource: $resource, mapResource: $mapResource, current: $current, total: $total, isUnReceiveReward: $isUnReceiveReward, nodeRewardType: $nodeRewardType, joinActivityRewardList: $joinActivityRewardList, teamMemberList: $teamMemberList, mapNodeList: $mapNodeList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyDetailModel &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            (identical(other.resource, resource) ||
                other.resource == resource) &&
            (identical(other.mapResource, mapResource) ||
                other.mapResource == mapResource) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.isUnReceiveReward, isUnReceiveReward) ||
                other.isUnReceiveReward == isUnReceiveReward) &&
            (identical(other.nodeRewardType, nodeRewardType) ||
                other.nodeRewardType == nodeRewardType) &&
            const DeepCollectionEquality().equals(
                other._joinActivityRewardList, _joinActivityRewardList) &&
            const DeepCollectionEquality()
                .equals(other._teamMemberList, _teamMemberList) &&
            const DeepCollectionEquality()
                .equals(other._mapNodeList, _mapNodeList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      teamId,
      status,
      startTime,
      endTime,
      subjectType,
      subjectName,
      resource,
      mapResource,
      current,
      total,
      isUnReceiveReward,
      nodeRewardType,
      const DeepCollectionEquality().hash(_joinActivityRewardList),
      const DeepCollectionEquality().hash(_teamMemberList),
      const DeepCollectionEquality().hash(_mapNodeList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyDetailModelCopyWith<_$_TeamStudyDetailModel> get copyWith =>
      __$$_TeamStudyDetailModelCopyWithImpl<_$_TeamStudyDetailModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyDetailModelToJson(
      this,
    );
  }
}

abstract class _TeamStudyDetailModel implements TeamStudyDetailModel {
  factory _TeamStudyDetailModel(
          {final int? teamId,
          final int? status,
          final int? startTime,
          final int? endTime,
          final int? subjectType,
          final String? subjectName,
          final String? resource,
          final String? mapResource,
          final int? current,
          final int? total,
          final int? isUnReceiveReward,
          final int? nodeRewardType,
          final List<TeamStudyActivityRewardModel>? joinActivityRewardList,
          final List<TeamStudyMemberModel>? teamMemberList,
          final List<TeamStudySeaMapPointState>? mapNodeList}) =
      _$_TeamStudyDetailModel;

  factory _TeamStudyDetailModel.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyDetailModel.fromJson;

  @override
  int? get teamId;
  @override
  int? get status;
  @override // 活动状态(0未解锁,1组队中,2进行中,3已结束)
  int? get startTime;
  @override
  int? get endTime;
  @override
  int? get subjectType;
  @override
  String? get subjectName;
  @override
  String? get resource;
  @override // 页面资源
  String? get mapResource;
  @override // 地图页面资源
  int? get current;
  @override
  int? get total;
  @override
  int? get isUnReceiveReward;
  @override // 是否有未领取奖励(1是0否)
  int? get nodeRewardType;
  @override // 类型(0无奖励,1宝箱,2照片,3终极宝箱)
  List<TeamStudyActivityRewardModel>? get joinActivityRewardList;
  @override // 参加活动奖励列表
  List<TeamStudyMemberModel>? get teamMemberList;
  @override
  List<TeamStudySeaMapPointState>? get mapNodeList;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyDetailModelCopyWith<_$_TeamStudyDetailModel> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamStudyActivityRewardModel _$TeamStudyActivityRewardModelFromJson(
    Map<String, dynamic> json) {
  return _TeamStudyActivityRewardModel.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyActivityRewardModel {
  int? get id => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  int? get isReceive => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyActivityRewardModelCopyWith<TeamStudyActivityRewardModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyActivityRewardModelCopyWith<$Res> {
  factory $TeamStudyActivityRewardModelCopyWith(
          TeamStudyActivityRewardModel value,
          $Res Function(TeamStudyActivityRewardModel) then) =
      _$TeamStudyActivityRewardModelCopyWithImpl<$Res,
          TeamStudyActivityRewardModel>;
  @useResult
  $Res call(
      {int? id,
      int? type,
      String? name,
      String? desc,
      String? img,
      int? isReceive});
}

/// @nodoc
class _$TeamStudyActivityRewardModelCopyWithImpl<$Res,
        $Val extends TeamStudyActivityRewardModel>
    implements $TeamStudyActivityRewardModelCopyWith<$Res> {
  _$TeamStudyActivityRewardModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? img = freezed,
    Object? isReceive = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      isReceive: freezed == isReceive
          ? _value.isReceive
          : isReceive // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyActivityRewardModelCopyWith<$Res>
    implements $TeamStudyActivityRewardModelCopyWith<$Res> {
  factory _$$_TeamStudyActivityRewardModelCopyWith(
          _$_TeamStudyActivityRewardModel value,
          $Res Function(_$_TeamStudyActivityRewardModel) then) =
      __$$_TeamStudyActivityRewardModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? type,
      String? name,
      String? desc,
      String? img,
      int? isReceive});
}

/// @nodoc
class __$$_TeamStudyActivityRewardModelCopyWithImpl<$Res>
    extends _$TeamStudyActivityRewardModelCopyWithImpl<$Res,
        _$_TeamStudyActivityRewardModel>
    implements _$$_TeamStudyActivityRewardModelCopyWith<$Res> {
  __$$_TeamStudyActivityRewardModelCopyWithImpl(
      _$_TeamStudyActivityRewardModel _value,
      $Res Function(_$_TeamStudyActivityRewardModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? img = freezed,
    Object? isReceive = freezed,
  }) {
    return _then(_$_TeamStudyActivityRewardModel(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      isReceive: freezed == isReceive
          ? _value.isReceive
          : isReceive // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyActivityRewardModel implements _TeamStudyActivityRewardModel {
  _$_TeamStudyActivityRewardModel(
      {this.id, this.type, this.name, this.desc, this.img, this.isReceive});

  factory _$_TeamStudyActivityRewardModel.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyActivityRewardModelFromJson(json);

  @override
  final int? id;
  @override
  final int? type;
  @override
  final String? name;
  @override
  final String? desc;
  @override
  final String? img;
  @override
  final int? isReceive;

  @override
  String toString() {
    return 'TeamStudyActivityRewardModel(id: $id, type: $type, name: $name, desc: $desc, img: $img, isReceive: $isReceive)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyActivityRewardModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.isReceive, isReceive) ||
                other.isReceive == isReceive));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, type, name, desc, img, isReceive);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyActivityRewardModelCopyWith<_$_TeamStudyActivityRewardModel>
      get copyWith => __$$_TeamStudyActivityRewardModelCopyWithImpl<
          _$_TeamStudyActivityRewardModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyActivityRewardModelToJson(
      this,
    );
  }
}

abstract class _TeamStudyActivityRewardModel
    implements TeamStudyActivityRewardModel {
  factory _TeamStudyActivityRewardModel(
      {final int? id,
      final int? type,
      final String? name,
      final String? desc,
      final String? img,
      final int? isReceive}) = _$_TeamStudyActivityRewardModel;

  factory _TeamStudyActivityRewardModel.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyActivityRewardModel.fromJson;

  @override
  int? get id;
  @override
  int? get type;
  @override
  String? get name;
  @override
  String? get desc;
  @override
  String? get img;
  @override
  int? get isReceive;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyActivityRewardModelCopyWith<_$_TeamStudyActivityRewardModel>
      get copyWith => throw _privateConstructorUsedError;
}

TeamStudyMemberModel _$TeamStudyMemberModelFromJson(Map<String, dynamic> json) {
  return _TeamStudyMemberModel.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyMemberModel {
  int? get memberId => throw _privateConstructorUsedError;
  String? get photo => throw _privateConstructorUsedError;
  String? get nickname => throw _privateConstructorUsedError;
  int? get dayCount => throw _privateConstructorUsedError; // 最近连续天数
  int? get isSelf => throw _privateConstructorUsedError; //是否本人(1是0否)
  List<TeamStudyMemberDressModel>? get dressList =>
      throw _privateConstructorUsedError;
  int? get pendingCollectionEnergy => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyMemberModelCopyWith<TeamStudyMemberModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyMemberModelCopyWith<$Res> {
  factory $TeamStudyMemberModelCopyWith(TeamStudyMemberModel value,
          $Res Function(TeamStudyMemberModel) then) =
      _$TeamStudyMemberModelCopyWithImpl<$Res, TeamStudyMemberModel>;
  @useResult
  $Res call(
      {int? memberId,
      String? photo,
      String? nickname,
      int? dayCount,
      int? isSelf,
      List<TeamStudyMemberDressModel>? dressList,
      int? pendingCollectionEnergy});
}

/// @nodoc
class _$TeamStudyMemberModelCopyWithImpl<$Res,
        $Val extends TeamStudyMemberModel>
    implements $TeamStudyMemberModelCopyWith<$Res> {
  _$TeamStudyMemberModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberId = freezed,
    Object? photo = freezed,
    Object? nickname = freezed,
    Object? dayCount = freezed,
    Object? isSelf = freezed,
    Object? dressList = freezed,
    Object? pendingCollectionEnergy = freezed,
  }) {
    return _then(_value.copyWith(
      memberId: freezed == memberId
          ? _value.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as int?,
      photo: freezed == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isSelf: freezed == isSelf
          ? _value.isSelf
          : isSelf // ignore: cast_nullable_to_non_nullable
              as int?,
      dressList: freezed == dressList
          ? _value.dressList
          : dressList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyMemberDressModel>?,
      pendingCollectionEnergy: freezed == pendingCollectionEnergy
          ? _value.pendingCollectionEnergy
          : pendingCollectionEnergy // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyMemberModelCopyWith<$Res>
    implements $TeamStudyMemberModelCopyWith<$Res> {
  factory _$$_TeamStudyMemberModelCopyWith(_$_TeamStudyMemberModel value,
          $Res Function(_$_TeamStudyMemberModel) then) =
      __$$_TeamStudyMemberModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? memberId,
      String? photo,
      String? nickname,
      int? dayCount,
      int? isSelf,
      List<TeamStudyMemberDressModel>? dressList,
      int? pendingCollectionEnergy});
}

/// @nodoc
class __$$_TeamStudyMemberModelCopyWithImpl<$Res>
    extends _$TeamStudyMemberModelCopyWithImpl<$Res, _$_TeamStudyMemberModel>
    implements _$$_TeamStudyMemberModelCopyWith<$Res> {
  __$$_TeamStudyMemberModelCopyWithImpl(_$_TeamStudyMemberModel _value,
      $Res Function(_$_TeamStudyMemberModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberId = freezed,
    Object? photo = freezed,
    Object? nickname = freezed,
    Object? dayCount = freezed,
    Object? isSelf = freezed,
    Object? dressList = freezed,
    Object? pendingCollectionEnergy = freezed,
  }) {
    return _then(_$_TeamStudyMemberModel(
      memberId: freezed == memberId
          ? _value.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as int?,
      photo: freezed == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isSelf: freezed == isSelf
          ? _value.isSelf
          : isSelf // ignore: cast_nullable_to_non_nullable
              as int?,
      dressList: freezed == dressList
          ? _value._dressList
          : dressList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyMemberDressModel>?,
      pendingCollectionEnergy: freezed == pendingCollectionEnergy
          ? _value.pendingCollectionEnergy
          : pendingCollectionEnergy // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyMemberModel implements _TeamStudyMemberModel {
  _$_TeamStudyMemberModel(
      {this.memberId,
      this.photo,
      this.nickname,
      this.dayCount,
      this.isSelf,
      final List<TeamStudyMemberDressModel>? dressList,
      this.pendingCollectionEnergy})
      : _dressList = dressList;

  factory _$_TeamStudyMemberModel.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyMemberModelFromJson(json);

  @override
  final int? memberId;
  @override
  final String? photo;
  @override
  final String? nickname;
  @override
  final int? dayCount;
// 最近连续天数
  @override
  final int? isSelf;
//是否本人(1是0否)
  final List<TeamStudyMemberDressModel>? _dressList;
//是否本人(1是0否)
  @override
  List<TeamStudyMemberDressModel>? get dressList {
    final value = _dressList;
    if (value == null) return null;
    if (_dressList is EqualUnmodifiableListView) return _dressList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? pendingCollectionEnergy;

  @override
  String toString() {
    return 'TeamStudyMemberModel(memberId: $memberId, photo: $photo, nickname: $nickname, dayCount: $dayCount, isSelf: $isSelf, dressList: $dressList, pendingCollectionEnergy: $pendingCollectionEnergy)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyMemberModel &&
            (identical(other.memberId, memberId) ||
                other.memberId == memberId) &&
            (identical(other.photo, photo) || other.photo == photo) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.dayCount, dayCount) ||
                other.dayCount == dayCount) &&
            (identical(other.isSelf, isSelf) || other.isSelf == isSelf) &&
            const DeepCollectionEquality()
                .equals(other._dressList, _dressList) &&
            (identical(
                    other.pendingCollectionEnergy, pendingCollectionEnergy) ||
                other.pendingCollectionEnergy == pendingCollectionEnergy));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      memberId,
      photo,
      nickname,
      dayCount,
      isSelf,
      const DeepCollectionEquality().hash(_dressList),
      pendingCollectionEnergy);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyMemberModelCopyWith<_$_TeamStudyMemberModel> get copyWith =>
      __$$_TeamStudyMemberModelCopyWithImpl<_$_TeamStudyMemberModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyMemberModelToJson(
      this,
    );
  }
}

abstract class _TeamStudyMemberModel implements TeamStudyMemberModel {
  factory _TeamStudyMemberModel(
      {final int? memberId,
      final String? photo,
      final String? nickname,
      final int? dayCount,
      final int? isSelf,
      final List<TeamStudyMemberDressModel>? dressList,
      final int? pendingCollectionEnergy}) = _$_TeamStudyMemberModel;

  factory _TeamStudyMemberModel.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyMemberModel.fromJson;

  @override
  int? get memberId;
  @override
  String? get photo;
  @override
  String? get nickname;
  @override
  int? get dayCount;
  @override // 最近连续天数
  int? get isSelf;
  @override //是否本人(1是0否)
  List<TeamStudyMemberDressModel>? get dressList;
  @override
  int? get pendingCollectionEnergy;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyMemberModelCopyWith<_$_TeamStudyMemberModel> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamStudyMemberDressModel _$TeamStudyMemberDressModelFromJson(
    Map<String, dynamic> json) {
  return _TeamStudyMemberDressModel.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyMemberDressModel {
  String? get resource => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyMemberDressModelCopyWith<TeamStudyMemberDressModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyMemberDressModelCopyWith<$Res> {
  factory $TeamStudyMemberDressModelCopyWith(TeamStudyMemberDressModel value,
          $Res Function(TeamStudyMemberDressModel) then) =
      _$TeamStudyMemberDressModelCopyWithImpl<$Res, TeamStudyMemberDressModel>;
  @useResult
  $Res call({String? resource});
}

/// @nodoc
class _$TeamStudyMemberDressModelCopyWithImpl<$Res,
        $Val extends TeamStudyMemberDressModel>
    implements $TeamStudyMemberDressModelCopyWith<$Res> {
  _$TeamStudyMemberDressModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? resource = freezed,
  }) {
    return _then(_value.copyWith(
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyMemberDressModelCopyWith<$Res>
    implements $TeamStudyMemberDressModelCopyWith<$Res> {
  factory _$$_TeamStudyMemberDressModelCopyWith(
          _$_TeamStudyMemberDressModel value,
          $Res Function(_$_TeamStudyMemberDressModel) then) =
      __$$_TeamStudyMemberDressModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? resource});
}

/// @nodoc
class __$$_TeamStudyMemberDressModelCopyWithImpl<$Res>
    extends _$TeamStudyMemberDressModelCopyWithImpl<$Res,
        _$_TeamStudyMemberDressModel>
    implements _$$_TeamStudyMemberDressModelCopyWith<$Res> {
  __$$_TeamStudyMemberDressModelCopyWithImpl(
      _$_TeamStudyMemberDressModel _value,
      $Res Function(_$_TeamStudyMemberDressModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? resource = freezed,
  }) {
    return _then(_$_TeamStudyMemberDressModel(
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyMemberDressModel implements _TeamStudyMemberDressModel {
  _$_TeamStudyMemberDressModel({this.resource});

  factory _$_TeamStudyMemberDressModel.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyMemberDressModelFromJson(json);

  @override
  final String? resource;

  @override
  String toString() {
    return 'TeamStudyMemberDressModel(resource: $resource)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyMemberDressModel &&
            (identical(other.resource, resource) ||
                other.resource == resource));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, resource);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyMemberDressModelCopyWith<_$_TeamStudyMemberDressModel>
      get copyWith => __$$_TeamStudyMemberDressModelCopyWithImpl<
          _$_TeamStudyMemberDressModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyMemberDressModelToJson(
      this,
    );
  }
}

abstract class _TeamStudyMemberDressModel implements TeamStudyMemberDressModel {
  factory _TeamStudyMemberDressModel({final String? resource}) =
      _$_TeamStudyMemberDressModel;

  factory _TeamStudyMemberDressModel.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyMemberDressModel.fromJson;

  @override
  String? get resource;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyMemberDressModelCopyWith<_$_TeamStudyMemberDressModel>
      get copyWith => throw _privateConstructorUsedError;
}
