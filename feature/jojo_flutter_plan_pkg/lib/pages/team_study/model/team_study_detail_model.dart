import 'package:freezed_annotation/freezed_annotation.dart';

import '../sea_map/model/team_study_sea_map_model.dart';

part 'team_study_detail_model.freezed.dart';
part 'team_study_detail_model.g.dart';

@freezed
class TeamStudyDetailModel with _$TeamStudyDetailModel {
  factory TeamStudyDetailModel({
    int? teamId,
    int? status, // 活动状态(0未解锁,1组队中,2进行中,3已结束)
    int? startTime,
    int? endTime,
    int? subjectType,
    String? subjectName,
    String? resource, // 页面资源
    String? mapResource, // 地图页面资源
    int? current,
    int? total,
    int? isUnReceiveReward, // 是否有未领取奖励(1是0否)
    int? nodeRewardType, // 类型(0无奖励,1宝箱,2照片,3终极宝箱)
    List<TeamStudyActivityRewardModel>? joinActivityRewardList, // 参加活动奖励列表
    List<TeamStudyMemberModel>? teamMemberList,
    List<TeamStudySeaMapPointState>? mapNodeList,
  }) = _TeamStudyDetailModel;

  factory TeamStudyDetailModel.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyDetailModelFromJson(json);
}

@freezed
class TeamStudyActivityRewardModel with _$TeamStudyActivityRewardModel {
  factory TeamStudyActivityRewardModel({
    int? id,
    int? type,
    String? name,
    String? desc,
    String? img,
    int? isReceive,
  }) = _TeamStudyActivityRewardModel;

  factory TeamStudyActivityRewardModel.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyActivityRewardModelFromJson(json);
}

@freezed
class TeamStudyMemberModel with _$TeamStudyMemberModel {
  factory TeamStudyMemberModel({
    int? memberId,
    String? photo,
    String? nickname,
    int? dayCount, // 最近连续天数
    int? isSelf, //是否本人(1是0否)
    List<TeamStudyMemberDressModel>? dressList,
    int? pendingCollectionEnergy, // 待领取能量
  }) = _TeamStudyMemberModel;

  factory TeamStudyMemberModel.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyMemberModelFromJson(json);
}

@freezed
class TeamStudyMemberDressModel with _$TeamStudyMemberDressModel {
  factory TeamStudyMemberDressModel({
    String? resource,
  }) = _TeamStudyMemberDressModel;

  factory TeamStudyMemberDressModel.fromJson(Map<String, dynamic> json) =>
      _$TeamStudyMemberDressModelFromJson(json);
}
