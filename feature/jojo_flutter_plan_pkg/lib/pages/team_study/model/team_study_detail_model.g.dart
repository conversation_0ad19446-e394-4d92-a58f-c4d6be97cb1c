// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_study_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_TeamStudyDetailModel _$$_TeamStudyDetailModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudyDetailModel(
      teamId: json['teamId'] as int?,
      status: json['status'] as int?,
      startTime: json['startTime'] as int?,
      endTime: json['endTime'] as int?,
      subjectType: json['subjectType'] as int?,
      subjectName: json['subjectName'] as String?,
      resource: json['resource'] as String?,
      mapResource: json['mapResource'] as String?,
      current: json['current'] as int?,
      total: json['total'] as int?,
      isUnReceiveReward: json['isUnReceiveReward'] as int?,
      nodeRewardType: json['nodeRewardType'] as int?,
      joinActivityRewardList: (json['joinActivityRewardList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudyActivityRewardModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      teamMemberList: (json['teamMemberList'] as List<dynamic>?)
          ?.map((e) => TeamStudyMemberModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      mapNodeList: (json['mapNodeList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudySeaMapPointState.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeamStudyDetailModelToJson(
        _$_TeamStudyDetailModel instance) =>
    <String, dynamic>{
      'teamId': instance.teamId,
      'status': instance.status,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'subjectType': instance.subjectType,
      'subjectName': instance.subjectName,
      'resource': instance.resource,
      'mapResource': instance.mapResource,
      'current': instance.current,
      'total': instance.total,
      'isUnReceiveReward': instance.isUnReceiveReward,
      'nodeRewardType': instance.nodeRewardType,
      'joinActivityRewardList': instance.joinActivityRewardList,
      'teamMemberList': instance.teamMemberList,
      'mapNodeList': instance.mapNodeList,
    };

_$_TeamStudyActivityRewardModel _$$_TeamStudyActivityRewardModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudyActivityRewardModel(
      id: json['id'] as int?,
      type: json['type'] as int?,
      name: json['name'] as String?,
      desc: json['desc'] as String?,
      img: json['img'] as String?,
      isReceive: json['isReceive'] as int?,
    );

Map<String, dynamic> _$$_TeamStudyActivityRewardModelToJson(
        _$_TeamStudyActivityRewardModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'name': instance.name,
      'desc': instance.desc,
      'img': instance.img,
      'isReceive': instance.isReceive,
    };

_$_TeamStudyMemberModel _$$_TeamStudyMemberModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudyMemberModel(
      memberId: json['memberId'] as int?,
      photo: json['photo'] as String?,
      nickname: json['nickname'] as String?,
      dayCount: json['dayCount'] as int?,
      isSelf: json['isSelf'] as int?,
      dressList: (json['dressList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudyMemberDressModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      pendingCollectionEnergy: json['pendingCollectionEnergy'] as int?,
    );

Map<String, dynamic> _$$_TeamStudyMemberModelToJson(
        _$_TeamStudyMemberModel instance) =>
    <String, dynamic>{
      'memberId': instance.memberId,
      'photo': instance.photo,
      'nickname': instance.nickname,
      'dayCount': instance.dayCount,
      'isSelf': instance.isSelf,
      'dressList': instance.dressList,
      'pendingCollectionEnergy': instance.pendingCollectionEnergy,
    };

_$_TeamStudyMemberDressModel _$$_TeamStudyMemberDressModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudyMemberDressModel(
      resource: json['resource'] as String?,
    );

Map<String, dynamic> _$$_TeamStudyMemberDressModelToJson(
        _$_TeamStudyMemberDressModel instance) =>
    <String, dynamic>{
      'resource': instance.resource,
    };
