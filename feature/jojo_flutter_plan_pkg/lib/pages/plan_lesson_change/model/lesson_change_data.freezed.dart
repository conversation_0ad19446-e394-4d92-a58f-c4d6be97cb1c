// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lesson_change_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

LessonChangeData _$LessonChangeDataFromJson(Map<String, dynamic> json) {
  return _LessonChangeData.fromJson(json);
}

/// @nodoc
mixin _$LessonChangeData {
  String? get orderNo => throw _privateConstructorUsedError;
  int? get ownSkuId => throw _privateConstructorUsedError;
  String? get ownSkuName => throw _privateConstructorUsedError;
  String? get ownGoodsCode => throw _privateConstructorUsedError;
  int? get ownCourseId => throw _privateConstructorUsedError;
  bool? get gift => throw _privateConstructorUsedError;
  int? get belongSkuId => throw _privateConstructorUsedError;
  List<SkuList?>? get skuList => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonChangeDataCopyWith<LessonChangeData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonChangeDataCopyWith<$Res> {
  factory $LessonChangeDataCopyWith(
          LessonChangeData value, $Res Function(LessonChangeData) then) =
      _$LessonChangeDataCopyWithImpl<$Res, LessonChangeData>;
  @useResult
  $Res call(
      {String? orderNo,
      int? ownSkuId,
      String? ownSkuName,
      String? ownGoodsCode,
      int? ownCourseId,
      bool? gift,
      int? belongSkuId,
      List<SkuList?>? skuList,
      String? title});
}

/// @nodoc
class _$LessonChangeDataCopyWithImpl<$Res, $Val extends LessonChangeData>
    implements $LessonChangeDataCopyWith<$Res> {
  _$LessonChangeDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderNo = freezed,
    Object? ownSkuId = freezed,
    Object? ownSkuName = freezed,
    Object? ownGoodsCode = freezed,
    Object? ownCourseId = freezed,
    Object? gift = freezed,
    Object? belongSkuId = freezed,
    Object? skuList = freezed,
    Object? title = freezed,
  }) {
    return _then(_value.copyWith(
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      ownSkuId: freezed == ownSkuId
          ? _value.ownSkuId
          : ownSkuId // ignore: cast_nullable_to_non_nullable
              as int?,
      ownSkuName: freezed == ownSkuName
          ? _value.ownSkuName
          : ownSkuName // ignore: cast_nullable_to_non_nullable
              as String?,
      ownGoodsCode: freezed == ownGoodsCode
          ? _value.ownGoodsCode
          : ownGoodsCode // ignore: cast_nullable_to_non_nullable
              as String?,
      ownCourseId: freezed == ownCourseId
          ? _value.ownCourseId
          : ownCourseId // ignore: cast_nullable_to_non_nullable
              as int?,
      gift: freezed == gift
          ? _value.gift
          : gift // ignore: cast_nullable_to_non_nullable
              as bool?,
      belongSkuId: freezed == belongSkuId
          ? _value.belongSkuId
          : belongSkuId // ignore: cast_nullable_to_non_nullable
              as int?,
      skuList: freezed == skuList
          ? _value.skuList
          : skuList // ignore: cast_nullable_to_non_nullable
              as List<SkuList?>?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonChangeDataCopyWith<$Res>
    implements $LessonChangeDataCopyWith<$Res> {
  factory _$$_LessonChangeDataCopyWith(
          _$_LessonChangeData value, $Res Function(_$_LessonChangeData) then) =
      __$$_LessonChangeDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? orderNo,
      int? ownSkuId,
      String? ownSkuName,
      String? ownGoodsCode,
      int? ownCourseId,
      bool? gift,
      int? belongSkuId,
      List<SkuList?>? skuList,
      String? title});
}

/// @nodoc
class __$$_LessonChangeDataCopyWithImpl<$Res>
    extends _$LessonChangeDataCopyWithImpl<$Res, _$_LessonChangeData>
    implements _$$_LessonChangeDataCopyWith<$Res> {
  __$$_LessonChangeDataCopyWithImpl(
      _$_LessonChangeData _value, $Res Function(_$_LessonChangeData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderNo = freezed,
    Object? ownSkuId = freezed,
    Object? ownSkuName = freezed,
    Object? ownGoodsCode = freezed,
    Object? ownCourseId = freezed,
    Object? gift = freezed,
    Object? belongSkuId = freezed,
    Object? skuList = freezed,
    Object? title = freezed,
  }) {
    return _then(_$_LessonChangeData(
      orderNo: freezed == orderNo
          ? _value.orderNo
          : orderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      ownSkuId: freezed == ownSkuId
          ? _value.ownSkuId
          : ownSkuId // ignore: cast_nullable_to_non_nullable
              as int?,
      ownSkuName: freezed == ownSkuName
          ? _value.ownSkuName
          : ownSkuName // ignore: cast_nullable_to_non_nullable
              as String?,
      ownGoodsCode: freezed == ownGoodsCode
          ? _value.ownGoodsCode
          : ownGoodsCode // ignore: cast_nullable_to_non_nullable
              as String?,
      ownCourseId: freezed == ownCourseId
          ? _value.ownCourseId
          : ownCourseId // ignore: cast_nullable_to_non_nullable
              as int?,
      gift: freezed == gift
          ? _value.gift
          : gift // ignore: cast_nullable_to_non_nullable
              as bool?,
      belongSkuId: freezed == belongSkuId
          ? _value.belongSkuId
          : belongSkuId // ignore: cast_nullable_to_non_nullable
              as int?,
      skuList: freezed == skuList
          ? _value._skuList
          : skuList // ignore: cast_nullable_to_non_nullable
              as List<SkuList?>?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonChangeData implements _LessonChangeData {
  const _$_LessonChangeData(
      {this.orderNo,
      this.ownSkuId,
      this.ownSkuName,
      this.ownGoodsCode,
      this.ownCourseId,
      this.gift,
      this.belongSkuId,
      final List<SkuList?>? skuList,
      this.title})
      : _skuList = skuList;

  factory _$_LessonChangeData.fromJson(Map<String, dynamic> json) =>
      _$$_LessonChangeDataFromJson(json);

  @override
  final String? orderNo;
  @override
  final int? ownSkuId;
  @override
  final String? ownSkuName;
  @override
  final String? ownGoodsCode;
  @override
  final int? ownCourseId;
  @override
  final bool? gift;
  @override
  final int? belongSkuId;
  final List<SkuList?>? _skuList;
  @override
  List<SkuList?>? get skuList {
    final value = _skuList;
    if (value == null) return null;
    if (_skuList is EqualUnmodifiableListView) return _skuList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? title;

  @override
  String toString() {
    return 'LessonChangeData(orderNo: $orderNo, ownSkuId: $ownSkuId, ownSkuName: $ownSkuName, ownGoodsCode: $ownGoodsCode, ownCourseId: $ownCourseId, gift: $gift, belongSkuId: $belongSkuId, skuList: $skuList, title: $title)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonChangeData &&
            (identical(other.orderNo, orderNo) || other.orderNo == orderNo) &&
            (identical(other.ownSkuId, ownSkuId) ||
                other.ownSkuId == ownSkuId) &&
            (identical(other.ownSkuName, ownSkuName) ||
                other.ownSkuName == ownSkuName) &&
            (identical(other.ownGoodsCode, ownGoodsCode) ||
                other.ownGoodsCode == ownGoodsCode) &&
            (identical(other.ownCourseId, ownCourseId) ||
                other.ownCourseId == ownCourseId) &&
            (identical(other.gift, gift) || other.gift == gift) &&
            (identical(other.belongSkuId, belongSkuId) ||
                other.belongSkuId == belongSkuId) &&
            const DeepCollectionEquality().equals(other._skuList, _skuList) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      orderNo,
      ownSkuId,
      ownSkuName,
      ownGoodsCode,
      ownCourseId,
      gift,
      belongSkuId,
      const DeepCollectionEquality().hash(_skuList),
      title);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonChangeDataCopyWith<_$_LessonChangeData> get copyWith =>
      __$$_LessonChangeDataCopyWithImpl<_$_LessonChangeData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonChangeDataToJson(
      this,
    );
  }
}

abstract class _LessonChangeData implements LessonChangeData {
  const factory _LessonChangeData(
      {final String? orderNo,
      final int? ownSkuId,
      final String? ownSkuName,
      final String? ownGoodsCode,
      final int? ownCourseId,
      final bool? gift,
      final int? belongSkuId,
      final List<SkuList?>? skuList,
      final String? title}) = _$_LessonChangeData;

  factory _LessonChangeData.fromJson(Map<String, dynamic> json) =
      _$_LessonChangeData.fromJson;

  @override
  String? get orderNo;
  @override
  int? get ownSkuId;
  @override
  String? get ownSkuName;
  @override
  String? get ownGoodsCode;
  @override
  int? get ownCourseId;
  @override
  bool? get gift;
  @override
  int? get belongSkuId;
  @override
  List<SkuList?>? get skuList;
  @override
  String? get title;
  @override
  @JsonKey(ignore: true)
  _$$_LessonChangeDataCopyWith<_$_LessonChangeData> get copyWith =>
      throw _privateConstructorUsedError;
}

SkuList _$SkuListFromJson(Map<String, dynamic> json) {
  return _SkuList.fromJson(json);
}

/// @nodoc
mixin _$SkuList {
  int? get skuId => throw _privateConstructorUsedError;
  String? get skuName => throw _privateConstructorUsedError;
  String? get goodsCode => throw _privateConstructorUsedError;
  int? get courseId => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  bool? get own => throw _privateConstructorUsedError;
  bool? get change => throw _privateConstructorUsedError;
  bool? get selected => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SkuListCopyWith<SkuList> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SkuListCopyWith<$Res> {
  factory $SkuListCopyWith(SkuList value, $Res Function(SkuList) then) =
      _$SkuListCopyWithImpl<$Res, SkuList>;
  @useResult
  $Res call(
      {int? skuId,
      String? skuName,
      String? goodsCode,
      int? courseId,
      String? courseKey,
      bool? own,
      bool? change,
      bool? selected});
}

/// @nodoc
class _$SkuListCopyWithImpl<$Res, $Val extends SkuList>
    implements $SkuListCopyWith<$Res> {
  _$SkuListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? goodsCode = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? own = freezed,
    Object? change = freezed,
    Object? selected = freezed,
  }) {
    return _then(_value.copyWith(
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as int?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsCode: freezed == goodsCode
          ? _value.goodsCode
          : goodsCode // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      own: freezed == own
          ? _value.own
          : own // ignore: cast_nullable_to_non_nullable
              as bool?,
      change: freezed == change
          ? _value.change
          : change // ignore: cast_nullable_to_non_nullable
              as bool?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SkuListCopyWith<$Res> implements $SkuListCopyWith<$Res> {
  factory _$$_SkuListCopyWith(
          _$_SkuList value, $Res Function(_$_SkuList) then) =
      __$$_SkuListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? skuId,
      String? skuName,
      String? goodsCode,
      int? courseId,
      String? courseKey,
      bool? own,
      bool? change,
      bool? selected});
}

/// @nodoc
class __$$_SkuListCopyWithImpl<$Res>
    extends _$SkuListCopyWithImpl<$Res, _$_SkuList>
    implements _$$_SkuListCopyWith<$Res> {
  __$$_SkuListCopyWithImpl(_$_SkuList _value, $Res Function(_$_SkuList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? skuId = freezed,
    Object? skuName = freezed,
    Object? goodsCode = freezed,
    Object? courseId = freezed,
    Object? courseKey = freezed,
    Object? own = freezed,
    Object? change = freezed,
    Object? selected = freezed,
  }) {
    return _then(_$_SkuList(
      skuId: freezed == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as int?,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsCode: freezed == goodsCode
          ? _value.goodsCode
          : goodsCode // ignore: cast_nullable_to_non_nullable
              as String?,
      courseId: freezed == courseId
          ? _value.courseId
          : courseId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      own: freezed == own
          ? _value.own
          : own // ignore: cast_nullable_to_non_nullable
              as bool?,
      change: freezed == change
          ? _value.change
          : change // ignore: cast_nullable_to_non_nullable
              as bool?,
      selected: freezed == selected
          ? _value.selected
          : selected // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SkuList implements _SkuList {
  const _$_SkuList(
      {this.skuId,
      this.skuName,
      this.goodsCode,
      this.courseId,
      this.courseKey,
      this.own,
      this.change,
      this.selected});

  factory _$_SkuList.fromJson(Map<String, dynamic> json) =>
      _$$_SkuListFromJson(json);

  @override
  final int? skuId;
  @override
  final String? skuName;
  @override
  final String? goodsCode;
  @override
  final int? courseId;
  @override
  final String? courseKey;
  @override
  final bool? own;
  @override
  final bool? change;
  @override
  final bool? selected;

  @override
  String toString() {
    return 'SkuList(skuId: $skuId, skuName: $skuName, goodsCode: $goodsCode, courseId: $courseId, courseKey: $courseKey, own: $own, change: $change, selected: $selected)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SkuList &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.skuName, skuName) || other.skuName == skuName) &&
            (identical(other.goodsCode, goodsCode) ||
                other.goodsCode == goodsCode) &&
            (identical(other.courseId, courseId) ||
                other.courseId == courseId) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.own, own) || other.own == own) &&
            (identical(other.change, change) || other.change == change) &&
            (identical(other.selected, selected) ||
                other.selected == selected));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, skuId, skuName, goodsCode,
      courseId, courseKey, own, change, selected);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SkuListCopyWith<_$_SkuList> get copyWith =>
      __$$_SkuListCopyWithImpl<_$_SkuList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SkuListToJson(
      this,
    );
  }
}

abstract class _SkuList implements SkuList {
  const factory _SkuList(
      {final int? skuId,
      final String? skuName,
      final String? goodsCode,
      final int? courseId,
      final String? courseKey,
      final bool? own,
      final bool? change,
      final bool? selected}) = _$_SkuList;

  factory _SkuList.fromJson(Map<String, dynamic> json) = _$_SkuList.fromJson;

  @override
  int? get skuId;
  @override
  String? get skuName;
  @override
  String? get goodsCode;
  @override
  int? get courseId;
  @override
  String? get courseKey;
  @override
  bool? get own;
  @override
  bool? get change;
  @override
  bool? get selected;
  @override
  @JsonKey(ignore: true)
  _$$_SkuListCopyWith<_$_SkuList> get copyWith =>
      throw _privateConstructorUsedError;
}
