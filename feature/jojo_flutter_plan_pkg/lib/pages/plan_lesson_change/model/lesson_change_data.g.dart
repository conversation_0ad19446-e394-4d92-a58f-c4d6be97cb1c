// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_change_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_LessonChangeData _$$_LessonChangeDataFromJson(Map<String, dynamic> json) =>
    _$_LessonChangeData(
      orderNo: json['orderNo'] as String?,
      ownSkuId: json['ownSkuId'] as int?,
      ownSkuName: json['ownSkuName'] as String?,
      ownGoodsCode: json['ownGoodsCode'] as String?,
      ownCourseId: json['ownCourseId'] as int?,
      gift: json['gift'] as bool?,
      belongSkuId: json['belongSkuId'] as int?,
      skuList: (json['skuList'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : SkuList.fromJson(e as Map<String, dynamic>))
          .toList(),
      title: json['title'] as String?,
    );

Map<String, dynamic> _$$_LessonChangeDataToJson(_$_LessonChangeData instance) =>
    <String, dynamic>{
      'orderNo': instance.orderNo,
      'ownSkuId': instance.ownSkuId,
      'ownSkuName': instance.ownSkuName,
      'ownGoodsCode': instance.ownGoodsCode,
      'ownCourseId': instance.ownCourseId,
      'gift': instance.gift,
      'belongSkuId': instance.belongSkuId,
      'skuList': instance.skuList,
      'title': instance.title,
    };

_$_SkuList _$$_SkuListFromJson(Map<String, dynamic> json) => _$_SkuList(
      skuId: json['skuId'] as int?,
      skuName: json['skuName'] as String?,
      goodsCode: json['goodsCode'] as String?,
      courseId: json['courseId'] as int?,
      courseKey: json['courseKey'] as String?,
      own: json['own'] as bool?,
      change: json['change'] as bool?,
      selected: json['selected'] as bool?,
    );

Map<String, dynamic> _$$_SkuListToJson(_$_SkuList instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'goodsCode': instance.goodsCode,
      'courseId': instance.courseId,
      'courseKey': instance.courseKey,
      'own': instance.own,
      'change': instance.change,
      'selected': instance.selected,
    };
