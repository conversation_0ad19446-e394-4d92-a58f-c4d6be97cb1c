// To parse this JSON data, do
//
//     final lessonChangeData = lessonChangeDataFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'lesson_change_data.freezed.dart';
part 'lesson_change_data.g.dart';

LessonChangeData lessonChangeDataFromJson(String str) => LessonChangeData.fromJson(json.decode(str));

String lessonChangeDataToJson(LessonChangeData data) => json.encode(data.toJson());

@freezed
class LessonChangeData with _$LessonChangeData {
  const factory LessonChangeData({
    String? orderNo,
    int? ownSkuId,
    String? ownSkuName,
    String? ownGoodsCode,
    int? ownCourseId,
    bool? gift,
    int? belongSkuId,
    List<SkuList?>? skuList,
    String? title,
  }) = _LessonChangeData;

  factory LessonChangeData.fromJson(Map<String, dynamic> json) => _$LessonChangeDataFromJson(json);
}

@freezed
class SkuList with _$SkuList {
  const factory SkuList({
    int? skuId,
    String? skuName,
    String? goodsCode,
    int? courseId,
    String? courseKey,
    bool? own,
    bool? change,
    bool? selected
  }) = _SkuList;

  factory SkuList.fromJson(Map<String, dynamic> json) => _$SkuListFromJson(json);
}
