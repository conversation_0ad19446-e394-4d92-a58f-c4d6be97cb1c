import 'package:bloc/bloc.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_state.dart';

class TreasureChestCubit extends Cubit<TreasureChestState> {
  TreasureChestCubit() : super(const TreasureChestState());

  void switchSpine(BoxSpineNames spineName) {
    emit(state.copyWith(spineName: spineName));
  }

  void openTreasureChest() {
    emit(state.copyWith(isOpen: true));
  }

  List<String> convertNumToList(int input) {
    final realInput = input > 999 ? 999 : input;
    String inputStr = realInput.toString();

    if (!RegExp(r'^\d+$').hasMatch(inputStr)) {
      throw const FormatException('输入必须是非负整数');
    }
    final tempList = inputStr.split('').map((char) => char).toList();
    tempList.insert(0, "x");
    return tempList;
  }
}
