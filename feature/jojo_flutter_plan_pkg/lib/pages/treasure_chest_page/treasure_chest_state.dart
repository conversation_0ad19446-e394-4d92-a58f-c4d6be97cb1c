import 'package:equatable/equatable.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';

class TreasureChestState extends Equatable {
  final BoxSpineNames spineName;
  final bool isOpen;

  const TreasureChestState(
      {this.spineName = BoxSpineNames.boxEnter, this.isOpen = false});

  TreasureChestState copyWith({
    BoxSpineNames? spineName,
    bool? isOpen,
  }) {
    return TreasureChestState(
      spineName: spineName ?? this.spineName,
      isOpen: isOpen ?? this.isOpen,
    );
  }

  @override
  List<Object?> get props => [spineName, isOpen];
}

///
/// 展示奖励
/// [name] 道具名
/// [num] 道具数量
/// [icon] 道具图标
/// [desc] 道具描述
///
class TreasureChestData {
  final String name;
  final int num;
  final String icon;
  final String desc;

  TreasureChestData(
      {required this.name,
      required this.num,
      required this.icon,
      required this.desc});
}
