import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/audio_provider.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/local_spine_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/task_settle/widget/animations/animation_audio_mixin.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_cubit.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_state.dart';
import 'package:jojo_flutter_plan_pkg/static/audio.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';
import 'package:jojo_flutter_plan_pkg/utils/widget_util.dart';

abstract class _TreasureAudioState<T extends StatefulWidget> extends State<T>
    with
        SingleTickerProviderStateMixin,
        AudioProvider,
        AnimationAudioPlayMixin {
  final int _boxOpenIndex = 2;

  _playLocalAudioByIndex(String? audioPath, int index) {
    if (audioPath.isNotNullOrEmpty()) {
      playAudio(audioPlayer: audioPlayerByIndex(index), path: audioPath!);
    }
  }

  playBoxOpenAudio() {
    _playLocalAudioByIndex(AssetsAudio.FINISH_COURSE_BOX_OPEN, _boxOpenIndex);
  }

  playBoxGetAudio() {
    _playLocalAudioByIndex(AssetsAudio.FINISH_COURSE_BOX_GET, _boxOpenIndex);
  }
}

class TreasureChestPage extends StatefulWidget {
  final TreasureChestData data;

  const TreasureChestPage({Key? key, required this.data }) : super(key: key);


  @override
  State<TreasureChestPage> createState() => _TreasureChestWidgetPageState();

  ///
  /// 展示奖励
  ///
  static void show({required TreasureChestData data, VoidCallback? onClose}) {
    SmartDialog.show(
        tag: "treasure_chest_page",
        useAnimation: false,
        alignment: Alignment.center,
        bindPage: true,
        debounce: true,
        backDismiss: false,
        clickMaskDismiss: false,
        keepSingle: true,
        onDismiss: onClose,
        builder: (context) {
          return TreasureChestPage(data: data);
        });
  }
}

class _TreasureChestWidgetPageState extends State<TreasureChestPage> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => TreasureChestCubit(),
      child: Builder(
          builder: (context) => Scaffold(
                  body: Stack(
                children: [
                  BlocSelector<TreasureChestCubit, TreasureChestState,
                      BoxSpineNames>(selector: (state) {
                    return state.spineName;
                  }, builder: (context, spineName) {
                    return _TreasureChestWidget(spineName);
                  }),
                  BlocSelector<TreasureChestCubit, TreasureChestState, bool>(
                      selector: (state) {
                    return state.isOpen;
                  }, builder: (context, isOpen) {
                    return Visibility(
                        visible: isOpen,
                        child: _RewardWidget(
                          icon: widget.data.icon,
                          name: widget.data.name,
                          num: widget.data.num,
                          desc: widget.data.desc,
                        ));
                  })
                ],
              ))),
    );
  }
}

/// 奖品
class _RewardWidget extends StatefulWidget {
  final String icon; // 图片路径
  final String name; // 显示的文字
  final String desc;
  final int num;

  const _RewardWidget(
      {Key? key,
      required this.icon,
      required this.name,
      required this.num,
      required this.desc})
      : super(key: key);

  @override
  _RewardWidgetState createState() => _RewardWidgetState();
}

class _RewardWidgetState extends State<_RewardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _imageOpacityAnimation;
  late Animation<double> _imageScaleAnimation;
  late Animation<double> _textOpacityAnimation;

  @override
  void initState() {
    super.initState();
    // 初始化动画控制器，设置最大时长为300ms（图片动画）
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 图片透明度动画：0 -> 1，300ms，easeOutBack 曲线
    _imageOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutBack,
      ),
    );

    // 图片缩放动画：0.5 -> 1，300ms，easeOutBack 曲线
    _imageScaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutBack,
      ),
    );

    // 文字透明度动画：0 -> 1，200ms，线性曲线
    _textOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 200.0 / 300.0, curve: Curves.linear),
      ),
    );

    // 启动动画
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  List<Widget> _getImgList(BuildContext context) {
    return context
            .readOrNull<TreasureChestCubit>()
            ?.convertNumToList(widget.num)
            .map((e) {
          if (e == "x") {
            return ImageAssetWeb(
              assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_REWARD_X,
              package: Config.package,
              width: 22.rdp,
              height: 30.rdp,
            );
          }

          return Transform.translate(
            offset: Offset(-4.rdp, 0),
            child: ImageAssetWeb(
              assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_REWARD_X
                  .replaceFirst('x', e),
              package: Config.package,
              width: 22.rdp,
              height: 30.rdp,
            ),
          );
        }).toList() ??
        [];
  }

  Widget _getIconWidget(BuildContext context) => FadeTransition(
        opacity: _imageOpacityAnimation,
        child: ScaleTransition(
            scale: _imageScaleAnimation,
            child: Container(
              width: 151.rdp,
              height: 151.rdp,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(34.rdp),
                  border: Border.all(
                      color: context.appColors.jColorGray3, width: 1.rdp)),
              child: Stack(
                children: [
                  Center(
                    child: ImageNetworkCached(
                      imageUrl: widget.icon,
                      width: 150.rdp,
                      height: 150.rdp,
                      fit: BoxFit.contain,
                      alignment: Alignment.center,
                    ),
                  ),
                  Container(
                    alignment: Alignment.bottomRight,
                    padding: EdgeInsets.only(bottom: 8.49.rdp, right: 11.21.rdp),
                    child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: _getImgList(context)),
                  ),
                ],
              ),
            )),
      );

  Widget _getCenterContent(BuildContext context) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            FadeTransition(
              opacity: _textOpacityAnimation,
              child: SizedBox(
                width: 228.rdp,
                child: Text(
                  widget.name,
                  style: context.textstyles.largestTextEmphasis.pf
                      .copyWith(color: context.appColors.jColorGray5),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            SizedBox(height: 60.rdp),
            _getIconWidget(context),
            SizedBox(height: 60.rdp),
            FadeTransition(
              opacity: _textOpacityAnimation,
              child: SizedBox(
                width: 228.rdp,
                child: Text(
                  widget.desc,
                  style: context.textstyles.bodyTextLargeEmphasis.pf
                      .copyWith(color: context.appColors.jColorGray5),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      );

  Widget _getButton(BuildContext context) => Align(
        alignment: Alignment.bottomCenter,
        child: Padding(
          padding: EdgeInsets.only(bottom: 100.rdp),
          child: FadeTransition(
            opacity: _textOpacityAnimation,
            child: Text(
              S.of(context).clickAndClose,
              style: context.textstyles.remark.pf.copyWith(
                  fontWeight: FontWeight.w600,
                  color: context.appColors.jColorYellow5),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );

  @override
  Widget build(BuildContext context) {
    return ClickWidget(
      onTap: () => SmartDialog.dismiss(tag: "treasure_chest_page"),
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Stack(
          children: [_getCenterContent(context), _getButton(context)],
        ),
      ),
    );
  }
}

/// 宝箱
class _TreasureChestWidget extends StatefulWidget {
  final boxOpenBgKey = GlobalKey();

  final BoxSpineNames spineName;

  _TreasureChestWidget(this.spineName);

  @override
  State<StatefulWidget> createState() {
    return _TreasureChestPageState();
  }
}

class _TreasureChestPageState
    extends _TreasureAudioState<_TreasureChestWidget> {
  @override
  void initState() {
    super.initState();
    playBoxGetAudio();
  }

  void _openTreasureChest(BuildContext context) {
    playBoxOpenAudio();
    context
        .readOrNull<TreasureChestCubit>()
        ?.switchSpine(BoxSpineNames.boxOpen);
    context.readOrNull<TreasureChestCubit>()?.openTreasureChest();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox.fromSize(
        size: Size(MediaQuery.of(context).size.width,
            MediaQuery.of(context).size.height),
        child: Stack(
          children: [
            ClickWidget(
              onTap: () {
                _openTreasureChest(context);
              },
              child: Center(
                child: LocalSpineView(
                    key: widget.boxOpenBgKey,
                    initSpineName: widget.spineName.name,
                    atlasPath:
                        AssetsSpine.SPINE_FINISH_COURSE_MILESTONE_BOX_ATLAS,
                    skelPath:
                        AssetsSpine.SPINE_FINISH_COURSE_MILESTONE_BOX_SKEL,
                    spineShouldLoopCallback: (curName) =>
                        curName == BoxSpineNames.boxLoop.name,
                    spineNeedListenerCallback: (curName) =>
                        curName != BoxSpineNames.boxLoop.name,
                    animationFinishCallback: (curName) {
                      if (curName == BoxSpineNames.boxOpen.name) {
                        // 启动奖品动效
                        return true;
                      }
                      if (curName == BoxSpineNames.boxEnter.name) {
                        Future.delayed(const Duration(seconds: 3), () {
                          if(mounted){
                            _openTreasureChest(context);
                          }
                        });
                      }
                      return curName == BoxSpineNames.boxLoop.name;
                    },
                    customNextSpineNameHandler: _customNextSpineName),
              ),
            ),
            BlocSelector<TreasureChestCubit, TreasureChestState, bool>(
                selector: (state) {
              return state.isOpen;
            }, builder: (context, isOpen) {
              return Visibility(
                visible: !isOpen,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 100.rdp),
                    child: ClickWidget(
                      onTap: () => _openTreasureChest(context),
                      child: Text(
                        S.of(context).clickAndOpen,
                        style: context.textstyles.remark.pf.copyWith(
                            fontWeight: FontWeight.w600,
                            color: context.appColors.jColorYellow5),
                        textAlign: TextAlign.center,
                        softWrap: true,
                      ),
                    ),
                  ),
                ),
              );
            })
          ],
        ));
  }

  String? _customNextSpineName(String curName) {
    if (curName == BoxSpineNames.boxEnter.name) {
      return BoxSpineNames.boxLoop.name;
    }

    return null;
  }
}
