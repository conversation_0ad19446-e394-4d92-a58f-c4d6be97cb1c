import 'package:bloc/bloc.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/others/enum.dart';
import 'package:jojo_flutter_plan_pkg/service/partner_apply_api.dart';
import '../../service/partner_message_api.dart';
import 'model/partner_message_data.dart';
import 'state.dart';

class PartnerMessageController extends Cubit<PartnerMessageState> {
  PartnerMessageController({
    PartnerMessageApi? messageApi,
    PartnerApplyApi? applyApi,
  })  : _messageApi = messageApi ?? partnerMessageApi,
        _applyApi = applyApi ?? partnerApplyApi,
        super(PartnerMessageState(messages: null));

  final PartnerMessageApi _messageApi;
  final PartnerApplyApi _applyApi;


  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  void refreshMessages() async {
    // 刷新数据把偏移设置成0，重置刷新组件
    state.resetState();
    refreshController.resetNoData();
    fetchMessages(true);
  }

  void loadMoreMessages() async {
    fetchMessages(false);
  }

  void fetchMessages(bool refresh) async {
    PartnerMessageState newState = state.copyWith();
    try {
      PartnerMessageModel messageData = await _messageApi.getPartnerMessages(state.offset.toString(), state.size);
      newState.addMessages(messageData);
    } catch (e) {
      l.e('partner message error', 'error:$e');
    } finally {
      // 重置刷新的状态
      if (refresh) {
        refreshController.refreshCompleted();
      } else {
        refreshController.loadComplete();
      }
      if (newState.noMoreMessage == true) {
        // 没有更多数据
        refreshController.loadNoData();
      }

      if (newState.haveMessages()) {
        newState.status = PageStatus.success;
      } else {
        newState.status = PageStatus.empty;
      }
      emit(newState);
    }
  }

  // 同意成为学伴
  void agreeClick(PartnerMessage message) async {
    dynamic result;
    try {
      result = await handleApply(message, 'approve');
    } catch (e) {
      l.i('partner message', 'agreeClick error:$e');
    } finally {
      if (result != null) {
        l.i('partner message', 'agreeClick success');
        PartnerMessage newMessage = message.copyWith(
          friendsAcceptRequest: 2, // 更新字段值
        );
        final newState = state.copyWith();
        newState.replaceMessage(newMessage, message);
        emit(newState);
      }
    }
  }

  // 拒绝成为学伴
  void refuseClick(PartnerMessage message) async {
    dynamic result;
    try {
      result = await handleApply(message, 'reject');
    } catch (e) {
      l.i('partner message', 'refuseClick error:$e');
    } finally {
      if (result != null) {
        l.i('partner message', 'refuseClick success');
        PartnerMessage newMessage = message.copyWith(
          friendsAcceptRequest: 1, // 更新字段值
        );
        final newState = state.copyWith();
        newState.replaceMessage(newMessage, message);
        emit(newState);
      }
    }
  }

  // 访问学伴主页
  void visitPartnerHome(PartnerMessage message) {
    l.i('partner message', 'visitPartnerHome:${message.partnerId}');
    final partnerId = message.partnerId ?? 0;
    if (partnerId <= 0) {
      return;
    }
    if (message.jumpUrl == null) {
      l.i('partner message', 'jumpUrl null');
      return;
    }
    RunEnv.jumpLink(message.jumpUrl!);
  }

  Future<dynamic> handleApply(PartnerMessage message, String applyType) async {
    l.i('partner message', '_handleApply:$applyType');
    if (!PartnerMessageType.isPartnerRequestOrGroup(message.type)) {
      l.i('partner message', 'type wrong:${message.msgId}');
      return;
    }
    // 埋点
    final elementName =
        applyType == 'approve' ? '个人主页_消息界面_通过' : '个人主页_消息界面_拒绝';
    RunEnv.sensorsTrack('\$AppClick', {'\$element_name': elementName});

    if (message.type == PartnerMessageType.partnerGroup) {
      if (message.inviteRecordId == null) {
        l.i('partner message', 'inviteRecordId wrong:${message.msgId}');
      }

      final parm = {
        'action': applyType,
        'send_invite_user_id': message.inviteUserId
      };
      return _applyApi.handleGroupInvited(message.inviteRecordId!, parm);
    } else {
      if (message.partnerId == null) {
        l.i('partner message', 'partnerId wrong:${message.msgId}');
        return;
      }

      final parm = {'action': applyType, 'messageId': message.msgId};
      return _applyApi.handleApply(message.partnerId!, parm);
    }
  }
}
