// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'partner_message_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TeamMember _$TeamMemberFromJson(Map<String, dynamic> json) {
  return _TeamMember.fromJson(json);
}

/// @nodoc
mixin _$TeamMember {
  int? get memberId => throw _privateConstructorUsedError; // 分布式id
  String? get photo => throw _privateConstructorUsedError; // 头像
  String? get nickname => throw _privateConstructorUsedError; // 昵称
  int? get dayCount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamMemberCopyWith<TeamMember> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamMemberCopyWith<$Res> {
  factory $TeamMemberCopyWith(
          TeamMember value, $Res Function(TeamMember) then) =
      _$TeamMemberCopyWithImpl<$Res, TeamMember>;
  @useResult
  $Res call({int? memberId, String? photo, String? nickname, int? dayCount});
}

/// @nodoc
class _$TeamMemberCopyWithImpl<$Res, $Val extends TeamMember>
    implements $TeamMemberCopyWith<$Res> {
  _$TeamMemberCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberId = freezed,
    Object? photo = freezed,
    Object? nickname = freezed,
    Object? dayCount = freezed,
  }) {
    return _then(_value.copyWith(
      memberId: freezed == memberId
          ? _value.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as int?,
      photo: freezed == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamMemberCopyWith<$Res>
    implements $TeamMemberCopyWith<$Res> {
  factory _$$_TeamMemberCopyWith(
          _$_TeamMember value, $Res Function(_$_TeamMember) then) =
      __$$_TeamMemberCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? memberId, String? photo, String? nickname, int? dayCount});
}

/// @nodoc
class __$$_TeamMemberCopyWithImpl<$Res>
    extends _$TeamMemberCopyWithImpl<$Res, _$_TeamMember>
    implements _$$_TeamMemberCopyWith<$Res> {
  __$$_TeamMemberCopyWithImpl(
      _$_TeamMember _value, $Res Function(_$_TeamMember) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberId = freezed,
    Object? photo = freezed,
    Object? nickname = freezed,
    Object? dayCount = freezed,
  }) {
    return _then(_$_TeamMember(
      memberId: freezed == memberId
          ? _value.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as int?,
      photo: freezed == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamMember implements _TeamMember {
  _$_TeamMember({this.memberId, this.photo, this.nickname, this.dayCount});

  factory _$_TeamMember.fromJson(Map<String, dynamic> json) =>
      _$$_TeamMemberFromJson(json);

  @override
  final int? memberId;
// 分布式id
  @override
  final String? photo;
// 头像
  @override
  final String? nickname;
// 昵称
  @override
  final int? dayCount;

  @override
  String toString() {
    return 'TeamMember(memberId: $memberId, photo: $photo, nickname: $nickname, dayCount: $dayCount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamMember &&
            (identical(other.memberId, memberId) ||
                other.memberId == memberId) &&
            (identical(other.photo, photo) || other.photo == photo) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.dayCount, dayCount) ||
                other.dayCount == dayCount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, memberId, photo, nickname, dayCount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamMemberCopyWith<_$_TeamMember> get copyWith =>
      __$$_TeamMemberCopyWithImpl<_$_TeamMember>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamMemberToJson(
      this,
    );
  }
}

abstract class _TeamMember implements TeamMember {
  factory _TeamMember(
      {final int? memberId,
      final String? photo,
      final String? nickname,
      final int? dayCount}) = _$_TeamMember;

  factory _TeamMember.fromJson(Map<String, dynamic> json) =
      _$_TeamMember.fromJson;

  @override
  int? get memberId;
  @override // 分布式id
  String? get photo;
  @override // 头像
  String? get nickname;
  @override // 昵称
  int? get dayCount;
  @override
  @JsonKey(ignore: true)
  _$$_TeamMemberCopyWith<_$_TeamMember> get copyWith =>
      throw _privateConstructorUsedError;
}

PartnerMessageModel _$PartnerMessageModelFromJson(Map<String, dynamic> json) {
  return _PartnerMessageModel.fromJson(json);
}

/// @nodoc
mixin _$PartnerMessageModel {
  int? get size => throw _privateConstructorUsedError;
  List<PartnerMessage>? get messages => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PartnerMessageModelCopyWith<PartnerMessageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PartnerMessageModelCopyWith<$Res> {
  factory $PartnerMessageModelCopyWith(
          PartnerMessageModel value, $Res Function(PartnerMessageModel) then) =
      _$PartnerMessageModelCopyWithImpl<$Res, PartnerMessageModel>;
  @useResult
  $Res call({int? size, List<PartnerMessage>? messages});
}

/// @nodoc
class _$PartnerMessageModelCopyWithImpl<$Res, $Val extends PartnerMessageModel>
    implements $PartnerMessageModelCopyWith<$Res> {
  _$PartnerMessageModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? size = freezed,
    Object? messages = freezed,
  }) {
    return _then(_value.copyWith(
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      messages: freezed == messages
          ? _value.messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<PartnerMessage>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PartnerMessageModelCopyWith<$Res>
    implements $PartnerMessageModelCopyWith<$Res> {
  factory _$$_PartnerMessageModelCopyWith(_$_PartnerMessageModel value,
          $Res Function(_$_PartnerMessageModel) then) =
      __$$_PartnerMessageModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? size, List<PartnerMessage>? messages});
}

/// @nodoc
class __$$_PartnerMessageModelCopyWithImpl<$Res>
    extends _$PartnerMessageModelCopyWithImpl<$Res, _$_PartnerMessageModel>
    implements _$$_PartnerMessageModelCopyWith<$Res> {
  __$$_PartnerMessageModelCopyWithImpl(_$_PartnerMessageModel _value,
      $Res Function(_$_PartnerMessageModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? size = freezed,
    Object? messages = freezed,
  }) {
    return _then(_$_PartnerMessageModel(
      size: freezed == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int?,
      messages: freezed == messages
          ? _value._messages
          : messages // ignore: cast_nullable_to_non_nullable
              as List<PartnerMessage>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PartnerMessageModel implements _PartnerMessageModel {
  _$_PartnerMessageModel({this.size, final List<PartnerMessage>? messages})
      : _messages = messages;

  factory _$_PartnerMessageModel.fromJson(Map<String, dynamic> json) =>
      _$$_PartnerMessageModelFromJson(json);

  @override
  final int? size;
  final List<PartnerMessage>? _messages;
  @override
  List<PartnerMessage>? get messages {
    final value = _messages;
    if (value == null) return null;
    if (_messages is EqualUnmodifiableListView) return _messages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PartnerMessageModel(size: $size, messages: $messages)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PartnerMessageModel &&
            (identical(other.size, size) || other.size == size) &&
            const DeepCollectionEquality().equals(other._messages, _messages));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, size, const DeepCollectionEquality().hash(_messages));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PartnerMessageModelCopyWith<_$_PartnerMessageModel> get copyWith =>
      __$$_PartnerMessageModelCopyWithImpl<_$_PartnerMessageModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PartnerMessageModelToJson(
      this,
    );
  }
}

abstract class _PartnerMessageModel implements PartnerMessageModel {
  factory _PartnerMessageModel(
      {final int? size,
      final List<PartnerMessage>? messages}) = _$_PartnerMessageModel;

  factory _PartnerMessageModel.fromJson(Map<String, dynamic> json) =
      _$_PartnerMessageModel.fromJson;

  @override
  int? get size;
  @override
  List<PartnerMessage>? get messages;
  @override
  @JsonKey(ignore: true)
  _$$_PartnerMessageModelCopyWith<_$_PartnerMessageModel> get copyWith =>
      throw _privateConstructorUsedError;
}

PartnerMessage _$PartnerMessageFromJson(Map<String, dynamic> json) {
  return _PartnerMessage.fromJson(json);
}

/// @nodoc
mixin _$PartnerMessage {
  int? get msgId => throw _privateConstructorUsedError; // 消息id
  String? get content => throw _privateConstructorUsedError; // 消息内容
  int? get type =>
      throw _privateConstructorUsedError; // 1-送花花 2-戳一戳 3-学伴申请 4-组队邀请 // 消息类型
  int? get partnerId => throw _privateConstructorUsedError; // 学伴id
  String? get nickName => throw _privateConstructorUsedError; // 学伴昵称
  String? get avatarUrl => throw _privateConstructorUsedError; // 学伴头像
  int? get receiveTime => throw _privateConstructorUsedError; // 消息接收时间
  String? get timeDesc => throw _privateConstructorUsedError; // 时间描述
  int? get friendsAcceptRequest =>
      throw _privateConstructorUsedError; //0 已通过(申请方) 1 已拒绝(被申请方) 2 已通过(被申请方) 3 未处理(被申请方)
  String? get jumpUrl =>
      throw _privateConstructorUsedError; // 新增组队邀请相关字段 (type = 4)
  int? get teamStatus =>
      throw _privateConstructorUsedError; // 小队状态(0未解锁,1组队中,2进行中,3已结束)
  int? get inviteRecordId =>
      throw _privateConstructorUsedError; // 邀请记录id(同意/拒绝参数)
  int? get inviteUserId =>
      throw _privateConstructorUsedError; // 邀请用户id(同意/拒绝参数)
  List<TeamMember>? get teamMemberList => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PartnerMessageCopyWith<PartnerMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PartnerMessageCopyWith<$Res> {
  factory $PartnerMessageCopyWith(
          PartnerMessage value, $Res Function(PartnerMessage) then) =
      _$PartnerMessageCopyWithImpl<$Res, PartnerMessage>;
  @useResult
  $Res call(
      {int? msgId,
      String? content,
      int? type,
      int? partnerId,
      String? nickName,
      String? avatarUrl,
      int? receiveTime,
      String? timeDesc,
      int? friendsAcceptRequest,
      String? jumpUrl,
      int? teamStatus,
      int? inviteRecordId,
      int? inviteUserId,
      List<TeamMember>? teamMemberList});
}

/// @nodoc
class _$PartnerMessageCopyWithImpl<$Res, $Val extends PartnerMessage>
    implements $PartnerMessageCopyWith<$Res> {
  _$PartnerMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? msgId = freezed,
    Object? content = freezed,
    Object? type = freezed,
    Object? partnerId = freezed,
    Object? nickName = freezed,
    Object? avatarUrl = freezed,
    Object? receiveTime = freezed,
    Object? timeDesc = freezed,
    Object? friendsAcceptRequest = freezed,
    Object? jumpUrl = freezed,
    Object? teamStatus = freezed,
    Object? inviteRecordId = freezed,
    Object? inviteUserId = freezed,
    Object? teamMemberList = freezed,
  }) {
    return _then(_value.copyWith(
      msgId: freezed == msgId
          ? _value.msgId
          : msgId // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      receiveTime: freezed == receiveTime
          ? _value.receiveTime
          : receiveTime // ignore: cast_nullable_to_non_nullable
              as int?,
      timeDesc: freezed == timeDesc
          ? _value.timeDesc
          : timeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      friendsAcceptRequest: freezed == friendsAcceptRequest
          ? _value.friendsAcceptRequest
          : friendsAcceptRequest // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpUrl: freezed == jumpUrl
          ? _value.jumpUrl
          : jumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStatus: freezed == teamStatus
          ? _value.teamStatus
          : teamStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      inviteRecordId: freezed == inviteRecordId
          ? _value.inviteRecordId
          : inviteRecordId // ignore: cast_nullable_to_non_nullable
              as int?,
      inviteUserId: freezed == inviteUserId
          ? _value.inviteUserId
          : inviteUserId // ignore: cast_nullable_to_non_nullable
              as int?,
      teamMemberList: freezed == teamMemberList
          ? _value.teamMemberList
          : teamMemberList // ignore: cast_nullable_to_non_nullable
              as List<TeamMember>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PartnerMessageCopyWith<$Res>
    implements $PartnerMessageCopyWith<$Res> {
  factory _$$_PartnerMessageCopyWith(
          _$_PartnerMessage value, $Res Function(_$_PartnerMessage) then) =
      __$$_PartnerMessageCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? msgId,
      String? content,
      int? type,
      int? partnerId,
      String? nickName,
      String? avatarUrl,
      int? receiveTime,
      String? timeDesc,
      int? friendsAcceptRequest,
      String? jumpUrl,
      int? teamStatus,
      int? inviteRecordId,
      int? inviteUserId,
      List<TeamMember>? teamMemberList});
}

/// @nodoc
class __$$_PartnerMessageCopyWithImpl<$Res>
    extends _$PartnerMessageCopyWithImpl<$Res, _$_PartnerMessage>
    implements _$$_PartnerMessageCopyWith<$Res> {
  __$$_PartnerMessageCopyWithImpl(
      _$_PartnerMessage _value, $Res Function(_$_PartnerMessage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? msgId = freezed,
    Object? content = freezed,
    Object? type = freezed,
    Object? partnerId = freezed,
    Object? nickName = freezed,
    Object? avatarUrl = freezed,
    Object? receiveTime = freezed,
    Object? timeDesc = freezed,
    Object? friendsAcceptRequest = freezed,
    Object? jumpUrl = freezed,
    Object? teamStatus = freezed,
    Object? inviteRecordId = freezed,
    Object? inviteUserId = freezed,
    Object? teamMemberList = freezed,
  }) {
    return _then(_$_PartnerMessage(
      msgId: freezed == msgId
          ? _value.msgId
          : msgId // ignore: cast_nullable_to_non_nullable
              as int?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      partnerId: freezed == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int?,
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      receiveTime: freezed == receiveTime
          ? _value.receiveTime
          : receiveTime // ignore: cast_nullable_to_non_nullable
              as int?,
      timeDesc: freezed == timeDesc
          ? _value.timeDesc
          : timeDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      friendsAcceptRequest: freezed == friendsAcceptRequest
          ? _value.friendsAcceptRequest
          : friendsAcceptRequest // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpUrl: freezed == jumpUrl
          ? _value.jumpUrl
          : jumpUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStatus: freezed == teamStatus
          ? _value.teamStatus
          : teamStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      inviteRecordId: freezed == inviteRecordId
          ? _value.inviteRecordId
          : inviteRecordId // ignore: cast_nullable_to_non_nullable
              as int?,
      inviteUserId: freezed == inviteUserId
          ? _value.inviteUserId
          : inviteUserId // ignore: cast_nullable_to_non_nullable
              as int?,
      teamMemberList: freezed == teamMemberList
          ? _value._teamMemberList
          : teamMemberList // ignore: cast_nullable_to_non_nullable
              as List<TeamMember>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PartnerMessage implements _PartnerMessage {
  _$_PartnerMessage(
      {this.msgId,
      this.content,
      this.type,
      this.partnerId,
      this.nickName,
      this.avatarUrl,
      this.receiveTime,
      this.timeDesc,
      this.friendsAcceptRequest,
      this.jumpUrl,
      this.teamStatus,
      this.inviteRecordId,
      this.inviteUserId,
      final List<TeamMember>? teamMemberList})
      : _teamMemberList = teamMemberList;

  factory _$_PartnerMessage.fromJson(Map<String, dynamic> json) =>
      _$$_PartnerMessageFromJson(json);

  @override
  final int? msgId;
// 消息id
  @override
  final String? content;
// 消息内容
  @override
  final int? type;
// 1-送花花 2-戳一戳 3-学伴申请 4-组队邀请 // 消息类型
  @override
  final int? partnerId;
// 学伴id
  @override
  final String? nickName;
// 学伴昵称
  @override
  final String? avatarUrl;
// 学伴头像
  @override
  final int? receiveTime;
// 消息接收时间
  @override
  final String? timeDesc;
// 时间描述
  @override
  final int? friendsAcceptRequest;
//0 已通过(申请方) 1 已拒绝(被申请方) 2 已通过(被申请方) 3 未处理(被申请方)
  @override
  final String? jumpUrl;
// 新增组队邀请相关字段 (type = 4)
  @override
  final int? teamStatus;
// 小队状态(0未解锁,1组队中,2进行中,3已结束)
  @override
  final int? inviteRecordId;
// 邀请记录id(同意/拒绝参数)
  @override
  final int? inviteUserId;
// 邀请用户id(同意/拒绝参数)
  final List<TeamMember>? _teamMemberList;
// 邀请用户id(同意/拒绝参数)
  @override
  List<TeamMember>? get teamMemberList {
    final value = _teamMemberList;
    if (value == null) return null;
    if (_teamMemberList is EqualUnmodifiableListView) return _teamMemberList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PartnerMessage(msgId: $msgId, content: $content, type: $type, partnerId: $partnerId, nickName: $nickName, avatarUrl: $avatarUrl, receiveTime: $receiveTime, timeDesc: $timeDesc, friendsAcceptRequest: $friendsAcceptRequest, jumpUrl: $jumpUrl, teamStatus: $teamStatus, inviteRecordId: $inviteRecordId, inviteUserId: $inviteUserId, teamMemberList: $teamMemberList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PartnerMessage &&
            (identical(other.msgId, msgId) || other.msgId == msgId) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.partnerId, partnerId) ||
                other.partnerId == partnerId) &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.receiveTime, receiveTime) ||
                other.receiveTime == receiveTime) &&
            (identical(other.timeDesc, timeDesc) ||
                other.timeDesc == timeDesc) &&
            (identical(other.friendsAcceptRequest, friendsAcceptRequest) ||
                other.friendsAcceptRequest == friendsAcceptRequest) &&
            (identical(other.jumpUrl, jumpUrl) || other.jumpUrl == jumpUrl) &&
            (identical(other.teamStatus, teamStatus) ||
                other.teamStatus == teamStatus) &&
            (identical(other.inviteRecordId, inviteRecordId) ||
                other.inviteRecordId == inviteRecordId) &&
            (identical(other.inviteUserId, inviteUserId) ||
                other.inviteUserId == inviteUserId) &&
            const DeepCollectionEquality()
                .equals(other._teamMemberList, _teamMemberList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      msgId,
      content,
      type,
      partnerId,
      nickName,
      avatarUrl,
      receiveTime,
      timeDesc,
      friendsAcceptRequest,
      jumpUrl,
      teamStatus,
      inviteRecordId,
      inviteUserId,
      const DeepCollectionEquality().hash(_teamMemberList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PartnerMessageCopyWith<_$_PartnerMessage> get copyWith =>
      __$$_PartnerMessageCopyWithImpl<_$_PartnerMessage>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PartnerMessageToJson(
      this,
    );
  }
}

abstract class _PartnerMessage implements PartnerMessage {
  factory _PartnerMessage(
      {final int? msgId,
      final String? content,
      final int? type,
      final int? partnerId,
      final String? nickName,
      final String? avatarUrl,
      final int? receiveTime,
      final String? timeDesc,
      final int? friendsAcceptRequest,
      final String? jumpUrl,
      final int? teamStatus,
      final int? inviteRecordId,
      final int? inviteUserId,
      final List<TeamMember>? teamMemberList}) = _$_PartnerMessage;

  factory _PartnerMessage.fromJson(Map<String, dynamic> json) =
      _$_PartnerMessage.fromJson;

  @override
  int? get msgId;
  @override // 消息id
  String? get content;
  @override // 消息内容
  int? get type;
  @override // 1-送花花 2-戳一戳 3-学伴申请 4-组队邀请 // 消息类型
  int? get partnerId;
  @override // 学伴id
  String? get nickName;
  @override // 学伴昵称
  String? get avatarUrl;
  @override // 学伴头像
  int? get receiveTime;
  @override // 消息接收时间
  String? get timeDesc;
  @override // 时间描述
  int? get friendsAcceptRequest;
  @override //0 已通过(申请方) 1 已拒绝(被申请方) 2 已通过(被申请方) 3 未处理(被申请方)
  String? get jumpUrl;
  @override // 新增组队邀请相关字段 (type = 4)
  int? get teamStatus;
  @override // 小队状态(0未解锁,1组队中,2进行中,3已结束)
  int? get inviteRecordId;
  @override // 邀请记录id(同意/拒绝参数)
  int? get inviteUserId;
  @override // 邀请用户id(同意/拒绝参数)
  List<TeamMember>? get teamMemberList;
  @override
  @JsonKey(ignore: true)
  _$$_PartnerMessageCopyWith<_$_PartnerMessage> get copyWith =>
      throw _privateConstructorUsedError;
}
