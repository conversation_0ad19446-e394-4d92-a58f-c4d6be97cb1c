import 'package:freezed_annotation/freezed_annotation.dart';
part 'partner_message_data.freezed.dart';
part 'partner_message_data.g.dart';

@freezed
class TeamMember with _$TeamMember {
  factory TeamMember({
    int? memberId, // 分布式id
    String? photo, // 头像
    String? nickname, // 昵称
    int? dayCount, // 最近连续天数
  }) = _TeamMember;
  factory TeamMember.fromJson(Map<String, dynamic> json) =>
      _$TeamMemberFromJson(json);
}

@freezed
class PartnerMessageModel with _$PartnerMessageModel {
  factory PartnerMessageModel({
    int? size,
    List<PartnerMessage>? messages,
  }) = _PartnerMessageModel;
  factory PartnerMessageModel.fromJson(Map<String, dynamic> json) =>
      _$PartnerMessageModelFromJson(json);
}

extension PartnerMessageModelExt on PartnerMessageModel {
  bool isNoMessages() {
    return messages == null || messages!.isEmpty;
  }
}

@freezed
class PartnerMessage with _$PartnerMessage {
  factory PartnerMessage({
    int? msgId, // 消息id
    String? content, // 消息内容
    int? type, // 1-送花花 2-戳一戳 3-学伴申请 4-组队邀请 // 消息类型
    int? partnerId, // 学伴id
    String? nickName, // 学伴昵称
    String? avatarUrl, // 学伴头像
    int? receiveTime, // 消息接收时间
    String? timeDesc, // 时间描述
    int? friendsAcceptRequest, //0 已通过(申请方) 1 已拒绝(被申请方) 2 已通过(被申请方) 3 未处理(被申请方)
    String? jumpUrl,
    // 新增组队邀请相关字段 (type = 4)
    int? teamStatus, // 小队状态(0未解锁,1组队中,2进行中,3已结束)
    int? inviteRecordId, // 邀请记录id(同意/拒绝参数)
    int? inviteUserId, // 邀请用户id(同意/拒绝参数)
    List<TeamMember>? teamMemberList, // 团队成员列表
  }) = _PartnerMessage;
  factory PartnerMessage.fromJson(Map<String, dynamic> json) =>
      _$PartnerMessageFromJson(json);
}

extension PartnerMessageExt on PartnerMessage {
  bool haveAcceptResult() {
    return friendsAcceptRequest != null && (friendsAcceptRequest == 1 || friendsAcceptRequest == 2);
  }
}
