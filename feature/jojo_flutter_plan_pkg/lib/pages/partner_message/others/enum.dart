


class PartnerMessageType {
  static const int sendFlower = 1;
  static const int poke = 2;
  static const int partnerRequest = 3;
  static const int partnerGroup = 4;
}

/// 好友申请处理状态枚举
class FriendsAcceptRequest {
  /// 已通过(申请方)
  static const int approved = 0;

  /// 已拒绝(被申请方)
  static const int rejected = 1;

  /// 已通过(被申请方)
  static const int accepted = 2;

  /// 未处理(被申请方)
  static const int pending = 3;

  /// 判断是否有处理结果
  static bool hasResult(int? status) {
    return status == rejected || status == accepted;
  }

  /// 判断是否已通过
  static bool isApproved(int? status) {
    return status == approved || status == accepted;
  }

  /// 判断是否已拒绝
  static bool isRejected(int? status) {
    return status == rejected;
  }

  /// 判断是否待处理
  static bool isPending(int? status) {
    return status == pending;
  }
}

/// 团队状态枚举
class TeamStatus {
  /// 未解锁
  static const int unlocked = 0;

  /// 组队中
  static const int recruiting = 1;

  /// 进行中
  static const int inProgress = 2;

  /// 已结束
  static const int finished = 3;

  /// 判断是否正在组队
  static bool isRecruiting(int? status) {
    return status == recruiting;
  }

  /// 判断是否进行中
  static bool isInProgress(int? status) {
    return status == inProgress;
  }

  /// 判断是否已结束
  static bool isFinished(int? status) {
    return status == finished;
  }

  /// 判断是否未解锁
  static bool isUnlocked(int? status) {
    return status == unlocked;
  }
}

