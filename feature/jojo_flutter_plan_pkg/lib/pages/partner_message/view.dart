import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/pull_refresh.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/mixins/tablet_scale_minx.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/widget/alternative_image_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/model/partner_message_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/others/enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/others/partner_message_const.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/landscape/home_incentive_info_land_widget.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:path/path.dart';

import '../../static/svg.dart';

class PartnerMessageView extends HookWidget with TabletScaleMixin {
  final int loadingScene;
  final PartnerMessageState state;

  const PartnerMessageView(
      {super.key, required this.state, required this.loadingScene});

  @override
  Widget build(BuildContext context) {
    final controller = context.read<PartnerMessageController>();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: JoJoAppBar(
        title: S.of(context).partnerMessage,
      ),
      body: PullOrRefresh(
        onLoading: () => controller.loadMoreMessages(),
        onRefresh: () => controller.refreshMessages(),
        refreshController: controller.refreshController,
        child: CustomScrollView(
          slivers: [
            // 消息列表主体
            SliverList(
              delegate: SliverChildBuilderDelegate(
                  (context, index) =>
                      _buildMessageItem(controller, state.messages![index], context),
                  childCount: state.messageCount()),
            ),
          ],
        ),
      ),
    );
  }

  // 单条消息项
  Widget _buildMessageItem(
      PartnerMessageController controller, PartnerMessage message, BuildContext context) {
    bool showGroup = message.type == PartnerMessageType.partnerGroup &&
         FriendsAcceptRequest.isPending(message.friendsAcceptRequest);
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.rdp),
      padding: EdgeInsets.symmetric(vertical: 14.rdp),
      decoration: const BoxDecoration(
        // 下划线
        border: Border(bottom: BorderSide(color: Color(0xF5F4F4FF))),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCellHeader(controller,message),
          SizedBox(width: context.dimensions.smallSpacing.rdp),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // SizedBox(width: 2.rdp),
              _buildCellTitle(message), // 用户名和时间
              SizedBox(height: 4.rdp), // 间隔
              _buildMessageContent(message), // 消息内容
              if (showGroup) _buildGroup(message),
              // 操作按钮区 (仅学伴申请类型)
              if (PartnerMessageType.isPartnerRequestOrGroup(message.type) && message.friendsAcceptRequest != 0)
                SizedBox(height: 12.rdp),
                _buildActionButtons(controller, message),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCellHeader(PartnerMessageController controller,PartnerMessage message) {
    return GestureDetector(
            onTap: () => controller.visitPartnerHome(message),
            child: AlternativeImageWidget(
              imageUrl: message.avatarUrl ?? '',
              displayWidth: PartnerMessageConst.avatarSize.rdp,
              displayHeight: PartnerMessageConst.avatarSize.rdp,
              displayConfig: ImageDisplayConfig.head,
              hideDefaultHolder: true,
              boxDecoration: BoxDecoration(
                borderRadius:BorderRadius.circular(PartnerMessageConst.avatarSize.rdp),
                border: Border.all(
                  color: HexColor('#F5F4F4'), // 灰色圆环
                  width: 1.rdp, // 圆环宽度（按需调整）
                ),
              ),
            ),
    );
  }

  Widget _buildCellTitle(PartnerMessage message) {
    return SizedBox(
      height: 21.rdp,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            message.nickName ?? '',
            style: TextStyle(
              fontSize: 14.rdp,
              fontWeight: FontWeight.w400,
              color: HexColor('#666666'),
            ),
          ),
          SizedBox(width: 4.rdp), // 间隔
          Text(
            message.timeDesc ?? '',
            style: TextStyle(
              fontSize: 14.rdp,
              fontWeight: FontWeight.w400,
              color: HexColor('#666666'),
            ),
          ),
        ],
      ),
    );
  }

  // 消息内容构建
  Widget _buildMessageContent(PartnerMessage message) {
    if (message.type == PartnerMessageType.sendFlower) {
      return Row(
        children: [
          ImageAssetWeb(
            assetName:AssetsImg.PARTNER_MESSAGE_PARTNER_SEND_FLOWER,
            package: RunEnv.package,
            width: 18.rdp,
            height: 18.rdp
          ),
          SizedBox(width: 6.rdp),
          _messageContent(message),
        ],
      );
    } else if (message.type == PartnerMessageType.poke) {
      return Row(
        children: [
          ImageAssetWeb(
            assetName:AssetsImg.PARTNER_MESSAGE_PARTNER_MESSAGE_CUO,
            package: RunEnv.package,
            width: 18.rdp,
            height: 18.rdp
          ),
          SizedBox(width: 4.rdp),
          _messageContent(message),
        ],
      );
    } else if (message.type == PartnerMessageType.partnerRequest ||
        message.type == PartnerMessageType.partnerGroup) {
      return _messageContent(message);
    } else {
      return Row(
        children: [
          Icon(Icons.energy_savings_leaf, size: 18, color: Colors.yellow[700]),
          const SizedBox(width: 4),
          const Text('消息类型未找到'),
        ],
      );
    }
  }

  Widget _messageContent(PartnerMessage message){
    return Text(
        message.content ?? '',
        style: TextStyle(
          fontSize: 16.rdp,
          fontWeight: FontWeight.w500,
          color: HexColor('#404040'),
        ),
      );
  }

  Widget _buildGroup(PartnerMessage message) {
    return Container(
      margin: EdgeInsets.only(top: 4.rdp),
      height: 82.rdp,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: _buildTeamMemberWidgets(message),
      ),
    );
  }

  /// 构建团队成员头像列表（无名称显示）
  List<Widget> _buildTeamMemberWidgets(PartnerMessage message) {
    List<Widget> widgets = [];
    final teamMembers = message.teamMemberList ?? [];

    // 最多显示3个位置
    for (int i = 0; i < 3; i++) {
      if (i < teamMembers.length) {
        // 显示团队成员头像
        final member = teamMembers[i];
        widgets.add(_buildMemberAvatar(member));
      } else {
        // 显示空位置（添加按钮样式）
        widgets.add(_buildEmptySlot());
      }

      // 添加间距（除了最后一个）
      if (i < 2) {
        widgets.add(SizedBox(width: 12.rdp));
      }
    }

    return widgets;
  }

  /// 构建单个成员头像
  Widget _buildMemberAvatar(TeamMember member) {
    return SizedBox(
      width: 82.rdp,
      height: 82.rdp,
      child: Center(
        child: Container(
          height: 58.rdp,
          width: 58.rdp,
          decoration: BoxDecoration(
            border: Border.all(
              color: HexColor('#E0DFDF'),
              width: 1.rdp,
            ),
            color: HexColor('#F5F4F4'),
            shape: BoxShape.circle,
          ),
          child: AlternativeImageWidget(
            imageUrl: member.photo ?? '',
            displayWidth: 58.rdp,
            displayHeight: 58.rdp,
            displayConfig: ImageDisplayConfig.head,
            hideDefaultHolder: true,
          ),
        ),
      ),
    );
  }

  /// 构建空位置（添加按钮样式）
  Widget _buildEmptySlot() {
    return SizedBox(
      width: 82.rdp,
      height: 82.rdp,
      child: Center(
        child: SvgAssetWeb(
          assetName: AssetsSvg.MY_PARTNERS_TEAM_STUDY_MESSAGE_ADD,
          height: 58.rdp,
          width: 58.rdp,
          package: RunEnv.package,
        ),
      ),
    );
  }

  // 操作按钮区
  Widget _buildActionButtons(
      PartnerMessageController controller, PartnerMessage message) {
    if (message.haveAcceptResult()) { // 已拒绝、已同意
      // 已处理状态显示 "已成为学伴"
      var icon = '';
      if(message.friendsAcceptRequest == 1){
        icon = AssetsImg.PARTNER_MESSAGE_PARTNER_MESSAGE_MAKE_REFUSE;
      }else if(message.friendsAcceptRequest == 2){
        icon = AssetsImg.PARTNER_MESSAGE_PARTNER_MESSAGE_MAKE_DONE;
      }
      return Row(
        children: [
          ImageAssetWeb(
            assetName:icon,
            package: RunEnv.package,
            width: 94.rdp,
            height: 32.rdp
          ),
        ],
      );
    }else if(message.friendsAcceptRequest == 0){ // 已通过
      return const SizedBox.shrink();
    }

    // 未处理的学伴申请显示操作按钮
    return Row(
      children: [
        GestureDetector(
          onTap: () => controller.refuseClick(message),
          child: ImageAssetWeb(
            assetName:AssetsImg.PARTNER_MESSAGE_PARTNER_MESSAGE_REFUSE,
            package: RunEnv.package,
            width: 80.rdp,
            height: 32.rdp
          ),
        ),
        const SizedBox(width: 12),
        GestureDetector(
          onTap: () => controller.agreeClick(message),
          child: ImageAssetWeb(
            assetName:AssetsImg.PARTNER_MESSAGE_PARTNER_MESSAGE_AGREE,
            package: RunEnv.package,
            width: 80.rdp,
            height: 32.rdp
          ),
        ),
      ],
    );
  }
}
