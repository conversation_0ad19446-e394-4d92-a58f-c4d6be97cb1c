
import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/utils/num_to_image.dart';


class ContinueLearnImgTool {

  /// 获取数字总宽度 number:数字
  /// numImgs:数字图片集合
  /// exImgs:数字图片的扩展图片 “天”
  /// height:图片高度
  /// return:数字总宽度
  static double getNumberWith(int number,Map<String,String> numImgs,List<String>? extraImgPaths, double height){
      
    final numList = number.toString().split('');
    List<String> numImgPaths = [];
    for(var i = 0; i < numList.length; i++){
      final imgPath = numImgs[numList[i]];
      if(imgPath != null){
        numImgPaths.add(imgPath);
      }
    }
    if(extraImgPaths != null && extraImgPaths.isNotEmpty){
      numImgPaths.addAll(extraImgPaths);
    }
    return _getImgsWidth(numImgPaths, height);
  }

  static Future<Size> _getImageSize(String assetPath) async {
    final completer = Completer<Size>();
    final image = Image.asset(assetPath);
    final ImageStream stream = image.image.resolve(ImageConfiguration.empty);

    late ImageStreamListener listener;
    listener = ImageStreamListener((ImageInfo info, bool _) {
      final Size size = Size(
        info.image.width.toDouble(),
        info.image.height.toDouble(),
      );
      info.image.dispose(); // 释放图片解码数据
      stream.removeListener(listener); // 关键：移除监听
      completer.complete(size);
    });

    stream.addListener(listener);
    return completer.future;
  }

  static Size _getImgSize(String path){
    return continueImageMapSize[path] ?? Size.zero;
  }

  static double _getImgWidthWithHeight(String path, double height){
    final size = _getImgSize(path);
    if(size.isEmpty){
      return 0;
    }
    return size.width / size.height * height;
  }

  static double _getImgsWidth(List<String> paths, double height){
    return paths.fold(0, (previousValue, element) {
      return previousValue + _getImgWidthWithHeight(element, height);
    });
  }

}