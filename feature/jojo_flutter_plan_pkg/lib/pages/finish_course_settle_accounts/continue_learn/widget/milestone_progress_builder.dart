import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';

import '../../../../generated/l10n.dart';
import '../../../../static/img.dart';
import '../buried_ext.dart';
import '../continue_learn_state.dart';
import '../device_scales.dart';
import 'builder.dart';
import 'finish_course_enum.dart';
import 'finish_course_types.dart';
import 'milestone_end_progress_view.dart';
import 'milestone_progress_view.dart';

class ProgressMilestoneBuilder {
  static double fit(BuildContext context, double size) {
    return size * screenScale(context);
  }

  static double screenScale(BuildContext context) {
    return layout.isTablet(context) ? sPadScale : 1.0;
  }

  /// 里程碑新样式
  static List<Widget> getProgressMilestoneEndWidget({
    required BuildContext context,
    required ContinueLearnState state,
    required bool showEndPageButtons,
    required bool showEndPageTip,
    required AnimationController endPageSlideController,
    required AnimationController endPageButtonController,
    required TapCallback? tapCallback,
  }) {
    return [
      Positioned.fill(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            VisibilityObserve(
              onShow: () {
                buriedContinueLearnAppViewScreen(state,
                    customScreenName: "课程结束_里程碑进度预告页面");
              },
              child: _buildProgressMilestoneEndContent(
                context: context,
                state: state,
                showEndPageTip: showEndPageTip,
                endPageSlideController: endPageSlideController,
                tapCallback: tapCallback,
              ),
            ),
            _buildButtonWidget(
              showEndPageButtons: showEndPageButtons,
              isContentPosition: true,
              isBottomPosition: false,
              context: context,
              state: state,
              tapCallback: tapCallback,
              endPageButtonController: endPageButtonController,
            ),
            layout.isTablet(context)
                ? SizedBox(height: fit(context, 60.rdp))
                : Container(),
          ],
        ),
      ),
      _buildButtonWidget(
        showEndPageButtons: showEndPageButtons,
        isContentPosition: false,
        isBottomPosition: true,
        context: context,
        state: state,
        tapCallback: tapCallback,
        endPageButtonController: endPageButtonController,
      )
    ];
  }

  static Widget _buildProgressMilestoneEndContent({
    required BuildContext context,
    required ContinueLearnState state,
    required bool showEndPageTip,
    required AnimationController endPageSlideController,
    required TapCallback? tapCallback,
  }) {
    return buildSlideWidget(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
      animationController: endPageSlideController,
      child: Container(
        color: Colors.transparent,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (state.dayCount > 0) ...[
              SizedBox(height: fit(context, 14.rdp)),
              buildNumberImagesNoAnim(
                state.dayCount,
                fit(context, state.finalNumberHeight().rdp),
              ),
              SizedBox(height: fit(context, 8.rdp))
            ],
            _getTitleWidget(context: context, state: state),
            _getTipsWidget(context: context, state: state),
            SizedBox(height: fit(context, 8.rdp)),
            _buildMilestoneProgressView(
              context: context,
              state: state,
              showEndPageTip: showEndPageTip,
              endPageButtonController: endPageSlideController,
              tapCallback: tapCallback,
            ),
            if (state.dayCount <= 0) SizedBox(height: 26.rdp),
          ],
        ),
      ),
    );
  }

  static Widget _getTitleWidget({
    required BuildContext context,
    required ContinueLearnState state,
  }) {
    return Container(
      height: fit(context, 36.rdp),
      alignment: Alignment.center,
      child: Text(
        S.of(context).continueMilestone,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: fit(context, 24.rdp),
          color: context.appColors.colorVariant6(state.subjectColor),
          fontFamily: 'PingFang SC',
        ),
      ),
    );
  }

  static Widget _getTipsWidget({
    required BuildContext context,
    required ContinueLearnState state,
  }) {
    return Container(
      height: fit(context, 21.rdp),
      alignment: Alignment.center,
      child: Text(
        state.milestoneProgressSceneTip ?? '',
        style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: fit(context, 14.rdp),
          color: context.appColors
              .colorVariant6(state.subjectColor)
              .withOpacity(0.7),
          fontFamily: 'PingFang SC',
        ),
      ),
    );
  }

  /// 构建按钮
  /// 手机按钮在内容下方，平板在页面底部，适配方式不同
  static Widget _buildButtonWidget({
    required bool showEndPageButtons,
    required bool isContentPosition,
    required bool isBottomPosition,
    required BuildContext context,
    required ContinueLearnState state,
    required AnimationController endPageButtonController,
    required TapCallback? tapCallback,
  }) {
    bool fitButtonWithContent = false; // 从内容（进度/日历）位置往下适配
    double fitButtonWithContentOffset = 14.5.rdp; // 距离内容间距

    bool fitButtonWithBottom = false; // 从页面底部网上适配
    double fitButtonWithBottomOffset = 80.rdp; // 距离页面底部间距

    bool isTabletDevice = layout.isTablet(context);
    bool isPhoneDevice = !layout.isTablet(context);

    if (showEndPageButtons) {
      if (isPhoneDevice) {
        // 当前设备是手机
        if (state.dayCount > 0 && isContentPosition) {
          // 当前连续学日期大于0,按钮位置在内容下方
          fitButtonWithContent = true;
          fitButtonWithBottom = false;
        }
        if (state.dayCount <= 0 && isBottomPosition) {
          // 当前连续学日期等于0,按钮位置在底部
          fitButtonWithContent = false;
          fitButtonWithBottom = true;
          fitButtonWithBottomOffset = 20.rdp;
        }
      }
      // 当前设备是平板,按钮位置在底部
      if (isTabletDevice && isBottomPosition) {
        fitButtonWithContent = false;
        fitButtonWithBottom = true;
      }

      if (fitButtonWithContent) {
        return Padding(
          padding: EdgeInsets.only(top: fitButtonWithContentOffset),
          child: _buildMilestoneProgressEndWidgetButton(
            context: context,
            state: state,
            tapCallback: tapCallback,
            endPageButtonController: endPageButtonController,
          ),
        );
      } else if (fitButtonWithBottom) {
        return Positioned(
          width: MediaQuery.of(context).size.width,
          bottom: fitButtonWithBottomOffset,
          child: _buildMilestoneProgressEndWidgetButton(
            context: context,
            state: state,
            tapCallback: tapCallback,
            endPageButtonController: endPageButtonController,
          ),
        );
      } else {
        return Container();
      }
    } else {
      if (isPhoneDevice && isContentPosition && state.dayCount > 0) {
        // 增加间距保持内容位置，不会在按钮出现时乱动
        return SizedBox(height: fit(context, 58.5.rdp));
      } else {
        return Container();
      }
    }
  }

  /// 底部按钮 继续保持和分享我的高光时刻
  static Widget _buildMilestoneProgressEndWidgetButton({
    required BuildContext context,
    required ContinueLearnState state,
    required AnimationController endPageButtonController,
    required TapCallback? tapCallback,
  }) {
    var showShare = state.getShowMilestoneShareBtn();
    final btnText = S.of(context).continueMaintain;
    return buildSlideWidget(
      begin: const Offset(0, 1),
      end: Offset.zero,
      animationController: endPageButtonController,
      child: buildAppearWidget(
        animationController: endPageButtonController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                /// 继续保持
                GestureDetector(
                  onTap: () => tapCallback?.call(TapType.keepContinue, btnText),
                  child: VisibilityObserve(
                    onShow: () => buriedContinueButtonViewScreen(
                        state: state, buttonName: btnText),
                    child: showShare
                        ? buildButton(
                            context,
                            btnText,
                            width: fit(context, 132.rdp),
                            backgroundColor: Colors.white,
                            borderColor: context.appColors.jColorYellow4,
                            fontFamily: 'PingFang SC',
                            scale: screenScale(context),
                          )
                        : buildButton(
                            context,
                            btnText,
                            width: fit(context, 280.rdp),
                            backgroundColor: context.appColors.jColorYellow4,
                            textColor: context.appColors.jColorYellow6,
                            fontFamily: 'PingFang SC',
                            scale: screenScale(context),
                          ),
                  ),
                ),
                if (showShare)
                  ..._buildMilestoneProgressShareBtn(
                    context: context,
                    state: state,
                    tapCallback: tapCallback,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 分享我的高光时刻
  static List<Widget> _buildMilestoneProgressShareBtn({
    required BuildContext context,
    required ContinueLearnState state,
    required TapCallback? tapCallback,
  }) {
    final btnText = S.of(context).shareMyHighlightMoment;
    return [
      SizedBox(width: fit(context, 20.rdp)),
      GestureDetector(
        onTap: () => tapCallback?.call(TapType.winShare, btnText),
        child: VisibilityObserve(
          onShow: () =>
              buriedContinueButtonViewScreen(state: state, buttonName: btnText),
          child: buildShareButton(
            context,
            btnText,
            AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_CAMERA,
            fontFamily: 'PingFang SC',
            scale: screenScale(context),
          ),
        ),
      ),
    ];
  }

  static Widget _buildMilestoneProgressView({
    required BuildContext context,
    required ContinueLearnState state,
    required bool showEndPageTip,
    required AnimationController endPageButtonController,
    required TapCallback? tapCallback,
  }) {
    var style = state.getMilestoneEndPageStyle();
    if (style == MilestoneEndPageStyle.calendar) {
      return Padding(
        padding: EdgeInsets.only(right: 5.rdp),
        child: MilestoneProgressView(
          initSpineName: state.getMilestoneProgressSpineName(
            count: state.getMilestoneCalendarCurrent(),
          ),
          spineType: state.spineType,
          tipPath: showEndPageTip ? state.endBubbleImagePath : null,
          scale: screenScale(context),
        ),
      );
    } else if (style == MilestoneEndPageStyle.progress) {
      return MilestoneEndProgressView(
        total: state.getMilestoneProgressTargetDays(),
        current: state.getMilestoneProgressCurrentDays(),
        subjectColor: state.subjectColor,
      );
    } else {
      return Container();
    }
  }
}
