import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';

import '../../../../common/config/config.dart';
import '../../../../static/img.dart';
import '../../../course_session_list/mixins/tablet_scale_minx.dart';

class MilestoneEndProgressView extends StatefulWidget {
  final int total; // 总进度
  final int current; // 当前进度
  final Color subjectColor;

  const MilestoneEndProgressView({
    super.key,
    required this.total,
    required this.current,
    required this.subjectColor,
  });

  @override
  State<MilestoneEndProgressView> createState() =>
      _MilestoneEndProgressViewState();
}

class _MilestoneEndProgressViewState extends State<MilestoneEndProgressView>
    with TabletScaleMixin, SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _rotationAnimation;

  double _progressCurrentWidth = 0;
  final double _progressHeight = 16.rdp;
  final double _progressTotalWidth = 278.rdp;
  final double _progressRadius = 70.rdp;

  double fit(double size) {
    return applyTabletScale(context, size);
  }

  _setProgress(int index) {
    int total = max(1, widget.total);
    index = min(index, total);
    _progressCurrentWidth =
        min(_progressTotalWidth * (index / total), _progressTotalWidth);
  }

  @override
  void initState() {
    super.initState();

    _setProgress(widget.current - 1);

    // 创建动画控制器，持续时间设置为0.5秒
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 720),
    );

    _rotationAnimation = TweenSequence<double>(
      [
        // 第一阶段：从0度向左旋转到6度
        _getTweenSequenceItem(0, 6, 1),
        // 第二阶段：从6度向右旋转到-6度
        _getTweenSequenceItem(6, -6, 2),
        _getTweenSequenceItem(-6, 6, 2),
        _getTweenSequenceItem(6, -6, 2),
        _getTweenSequenceItem(-6, 6, 2),
        _getTweenSequenceItem(6, -6, 2),
        // 第三阶段：从-6度回到0度
        _getTweenSequenceItem(-6, 0, 1),
      ],
    ).animate(_controller);

    Future.delayed(const Duration(milliseconds: 700), () {
      if (mounted) {
        setState(() {
          _setProgress(widget.current);
        });
      }

      _startPeriodicAnimation();
    });
  }

  TweenSequenceItem<double> _getTweenSequenceItem(
    double begin,
    double end,
    double weight,
  ) {
    return TweenSequenceItem(
      tween: Tween(
        begin: begin * pi / 180,
        end: end * pi / 180,
      ).chain(CurveTween(curve: Curves.easeInOut)),
      weight: weight,
    );
  }

  void _startPeriodicAnimation() {
    _controller.forward();
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        _controller.forward().then((_) {
          _controller.reset();
          _startPeriodicAnimation();
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: fit(386.rdp),
      height: fit(80.rdp),
      padding: EdgeInsets.only(
        left: fit(20.rdp),
        right: fit(20.rdp),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(fit(24.rdp)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: fit(24.rdp),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '${widget.current}/${widget.total}',
                    style: TextStyle(
                      fontSize: fit(16.rdp),
                      color: context.appColors.jColorGray5.withOpacity(0.7),
                      fontWeight: FontWeight.w400,
                      fontFamily: 'PingFang SC',
                    ),
                  ),
                ),
                SizedBox(height: fit(4.rdp)),
                _getProgressBar(),
              ],
            ),
          ),
          SizedBox(width: fit(8.rdp)),
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value,
                alignment:  const Alignment(0.0, 0.5),
                child: child,
              );
            },
            child: ImageAssetWeb(
              assetName: AssetsImg.FINISH_COURSE_SETTLE_ACCOUNTS_SMALL_BOX,
              package: Config.package,
              fit: BoxFit.contain,
              width: fit(60.rdp),
              height: fit(60.rdp),
            ),
          )
        ],
      ),
    );
  }

  Widget _getProgressBar() {
    return Container(
      height: fit(_progressHeight),
      width: fit(_progressTotalWidth),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F4F4),
        borderRadius: BorderRadius.all(Radius.circular(_progressRadius)),
      ),
      child: Stack(
        children: [
          Positioned(
            left: 0,
            top: 0,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
              width: fit(_progressCurrentWidth),
              height: fit(_progressHeight),
              decoration: BoxDecoration(
                color: context.appColors.colorVariant3(widget.subjectColor),
                border: Border.all(
                  color: context.appColors.colorVariant4(widget.subjectColor),
                  width: fit(1.rdp),
                ),
                borderRadius: BorderRadius.circular(_progressRadius),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
