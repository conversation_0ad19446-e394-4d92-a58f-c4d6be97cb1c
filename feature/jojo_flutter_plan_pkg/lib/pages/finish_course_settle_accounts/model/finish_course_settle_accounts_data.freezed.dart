// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'finish_course_settle_accounts_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

FinishCourseSettleAccountsData _$FinishCourseSettleAccountsDataFromJson(
    Map<String, dynamic> json) {
  return _FinishCourseSettleAccountsData.fromJson(json);
}

/// @nodoc
mixin _$FinishCourseSettleAccountsData {
  List<Popup>? get popups => throw _privateConstructorUsedError;
  bool? get disablePopups => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FinishCourseSettleAccountsDataCopyWith<FinishCourseSettleAccountsData>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FinishCourseSettleAccountsDataCopyWith<$Res> {
  factory $FinishCourseSettleAccountsDataCopyWith(
          FinishCourseSettleAccountsData value,
          $Res Function(FinishCourseSettleAccountsData) then) =
      _$FinishCourseSettleAccountsDataCopyWithImpl<$Res,
          FinishCourseSettleAccountsData>;
  @useResult
  $Res call({List<Popup>? popups, bool? disablePopups});
}

/// @nodoc
class _$FinishCourseSettleAccountsDataCopyWithImpl<$Res,
        $Val extends FinishCourseSettleAccountsData>
    implements $FinishCourseSettleAccountsDataCopyWith<$Res> {
  _$FinishCourseSettleAccountsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popups = freezed,
    Object? disablePopups = freezed,
  }) {
    return _then(_value.copyWith(
      popups: freezed == popups
          ? _value.popups
          : popups // ignore: cast_nullable_to_non_nullable
              as List<Popup>?,
      disablePopups: freezed == disablePopups
          ? _value.disablePopups
          : disablePopups // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_FinishCourseSettleAccountsDataCopyWith<$Res>
    implements $FinishCourseSettleAccountsDataCopyWith<$Res> {
  factory _$$_FinishCourseSettleAccountsDataCopyWith(
          _$_FinishCourseSettleAccountsData value,
          $Res Function(_$_FinishCourseSettleAccountsData) then) =
      __$$_FinishCourseSettleAccountsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Popup>? popups, bool? disablePopups});
}

/// @nodoc
class __$$_FinishCourseSettleAccountsDataCopyWithImpl<$Res>
    extends _$FinishCourseSettleAccountsDataCopyWithImpl<$Res,
        _$_FinishCourseSettleAccountsData>
    implements _$$_FinishCourseSettleAccountsDataCopyWith<$Res> {
  __$$_FinishCourseSettleAccountsDataCopyWithImpl(
      _$_FinishCourseSettleAccountsData _value,
      $Res Function(_$_FinishCourseSettleAccountsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? popups = freezed,
    Object? disablePopups = freezed,
  }) {
    return _then(_$_FinishCourseSettleAccountsData(
      popups: freezed == popups
          ? _value._popups
          : popups // ignore: cast_nullable_to_non_nullable
              as List<Popup>?,
      disablePopups: freezed == disablePopups
          ? _value.disablePopups
          : disablePopups // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_FinishCourseSettleAccountsData
    implements _FinishCourseSettleAccountsData {
  const _$_FinishCourseSettleAccountsData(
      {final List<Popup>? popups, this.disablePopups})
      : _popups = popups;

  factory _$_FinishCourseSettleAccountsData.fromJson(
          Map<String, dynamic> json) =>
      _$$_FinishCourseSettleAccountsDataFromJson(json);

  final List<Popup>? _popups;
  @override
  List<Popup>? get popups {
    final value = _popups;
    if (value == null) return null;
    if (_popups is EqualUnmodifiableListView) return _popups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? disablePopups;

  @override
  String toString() {
    return 'FinishCourseSettleAccountsData(popups: $popups, disablePopups: $disablePopups)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_FinishCourseSettleAccountsData &&
            const DeepCollectionEquality().equals(other._popups, _popups) &&
            (identical(other.disablePopups, disablePopups) ||
                other.disablePopups == disablePopups));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_popups), disablePopups);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_FinishCourseSettleAccountsDataCopyWith<_$_FinishCourseSettleAccountsData>
      get copyWith => __$$_FinishCourseSettleAccountsDataCopyWithImpl<
          _$_FinishCourseSettleAccountsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_FinishCourseSettleAccountsDataToJson(
      this,
    );
  }
}

abstract class _FinishCourseSettleAccountsData
    implements FinishCourseSettleAccountsData {
  const factory _FinishCourseSettleAccountsData(
      {final List<Popup>? popups,
      final bool? disablePopups}) = _$_FinishCourseSettleAccountsData;

  factory _FinishCourseSettleAccountsData.fromJson(Map<String, dynamic> json) =
      _$_FinishCourseSettleAccountsData.fromJson;

  @override
  List<Popup>? get popups;
  @override
  bool? get disablePopups;
  @override
  @JsonKey(ignore: true)
  _$$_FinishCourseSettleAccountsDataCopyWith<_$_FinishCourseSettleAccountsData>
      get copyWith => throw _privateConstructorUsedError;
}

Popup _$PopupFromJson(Map<String, dynamic> json) {
  return _Popup.fromJson(json);
}

/// @nodoc
mixin _$Popup {
  String? get type =>
      throw _privateConstructorUsedError; //  TASK_LIST、CONTINUOUS_LEARNING
  String? get name => throw _privateConstructorUsedError;
  int? get order => throw _privateConstructorUsedError;
  String? get route => throw _privateConstructorUsedError;
  Res? get res => throw _privateConstructorUsedError;
  Extend? get extend => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PopupCopyWith<Popup> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PopupCopyWith<$Res> {
  factory $PopupCopyWith(Popup value, $Res Function(Popup) then) =
      _$PopupCopyWithImpl<$Res, Popup>;
  @useResult
  $Res call(
      {String? type,
      String? name,
      int? order,
      String? route,
      Res? res,
      Extend? extend});

  $ResCopyWith<$Res>? get res;
  $ExtendCopyWith<$Res>? get extend;
}

/// @nodoc
class _$PopupCopyWithImpl<$Res, $Val extends Popup>
    implements $PopupCopyWith<$Res> {
  _$PopupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? order = freezed,
    Object? route = freezed,
    Object? res = freezed,
    Object? extend = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      res: freezed == res
          ? _value.res
          : res // ignore: cast_nullable_to_non_nullable
              as Res?,
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as Extend?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ResCopyWith<$Res>? get res {
    if (_value.res == null) {
      return null;
    }

    return $ResCopyWith<$Res>(_value.res!, (value) {
      return _then(_value.copyWith(res: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ExtendCopyWith<$Res>? get extend {
    if (_value.extend == null) {
      return null;
    }

    return $ExtendCopyWith<$Res>(_value.extend!, (value) {
      return _then(_value.copyWith(extend: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_PopupCopyWith<$Res> implements $PopupCopyWith<$Res> {
  factory _$$_PopupCopyWith(_$_Popup value, $Res Function(_$_Popup) then) =
      __$$_PopupCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? type,
      String? name,
      int? order,
      String? route,
      Res? res,
      Extend? extend});

  @override
  $ResCopyWith<$Res>? get res;
  @override
  $ExtendCopyWith<$Res>? get extend;
}

/// @nodoc
class __$$_PopupCopyWithImpl<$Res> extends _$PopupCopyWithImpl<$Res, _$_Popup>
    implements _$$_PopupCopyWith<$Res> {
  __$$_PopupCopyWithImpl(_$_Popup _value, $Res Function(_$_Popup) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? order = freezed,
    Object? route = freezed,
    Object? res = freezed,
    Object? extend = freezed,
  }) {
    return _then(_$_Popup(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      route: freezed == route
          ? _value.route
          : route // ignore: cast_nullable_to_non_nullable
              as String?,
      res: freezed == res
          ? _value.res
          : res // ignore: cast_nullable_to_non_nullable
              as Res?,
      extend: freezed == extend
          ? _value.extend
          : extend // ignore: cast_nullable_to_non_nullable
              as Extend?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Popup implements _Popup {
  const _$_Popup(
      {this.type, this.name, this.order, this.route, this.res, this.extend});

  factory _$_Popup.fromJson(Map<String, dynamic> json) =>
      _$$_PopupFromJson(json);

  @override
  final String? type;
//  TASK_LIST、CONTINUOUS_LEARNING
  @override
  final String? name;
  @override
  final int? order;
  @override
  final String? route;
  @override
  final Res? res;
  @override
  final Extend? extend;

  @override
  String toString() {
    return 'Popup(type: $type, name: $name, order: $order, route: $route, res: $res, extend: $extend)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Popup &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.route, route) || other.route == route) &&
            (identical(other.res, res) || other.res == res) &&
            (identical(other.extend, extend) || other.extend == extend));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, name, order, route, res, extend);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PopupCopyWith<_$_Popup> get copyWith =>
      __$$_PopupCopyWithImpl<_$_Popup>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PopupToJson(
      this,
    );
  }
}

abstract class _Popup implements Popup {
  const factory _Popup(
      {final String? type,
      final String? name,
      final int? order,
      final String? route,
      final Res? res,
      final Extend? extend}) = _$_Popup;

  factory _Popup.fromJson(Map<String, dynamic> json) = _$_Popup.fromJson;

  @override
  String? get type;
  @override //  TASK_LIST、CONTINUOUS_LEARNING
  String? get name;
  @override
  int? get order;
  @override
  String? get route;
  @override
  Res? get res;
  @override
  Extend? get extend;
  @override
  @JsonKey(ignore: true)
  _$$_PopupCopyWith<_$_Popup> get copyWith =>
      throw _privateConstructorUsedError;
}

Extend _$ExtendFromJson(Map<String, dynamic> json) {
  return _Extend.fromJson(json);
}

/// @nodoc
mixin _$Extend {
  AttendanceInfo? get attendanceInfo => throw _privateConstructorUsedError;
  BurialResources? get burialResources => throw _privateConstructorUsedError;
  List<LessonTaskList>? get lessonTaskList =>
      throw _privateConstructorUsedError;
  List<Asset>? get assets => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtendCopyWith<Extend> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtendCopyWith<$Res> {
  factory $ExtendCopyWith(Extend value, $Res Function(Extend) then) =
      _$ExtendCopyWithImpl<$Res, Extend>;
  @useResult
  $Res call(
      {AttendanceInfo? attendanceInfo,
      BurialResources? burialResources,
      List<LessonTaskList>? lessonTaskList,
      List<Asset>? assets});

  $AttendanceInfoCopyWith<$Res>? get attendanceInfo;
  $BurialResourcesCopyWith<$Res>? get burialResources;
}

/// @nodoc
class _$ExtendCopyWithImpl<$Res, $Val extends Extend>
    implements $ExtendCopyWith<$Res> {
  _$ExtendCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attendanceInfo = freezed,
    Object? burialResources = freezed,
    Object? lessonTaskList = freezed,
    Object? assets = freezed,
  }) {
    return _then(_value.copyWith(
      attendanceInfo: freezed == attendanceInfo
          ? _value.attendanceInfo
          : attendanceInfo // ignore: cast_nullable_to_non_nullable
              as AttendanceInfo?,
      burialResources: freezed == burialResources
          ? _value.burialResources
          : burialResources // ignore: cast_nullable_to_non_nullable
              as BurialResources?,
      lessonTaskList: freezed == lessonTaskList
          ? _value.lessonTaskList
          : lessonTaskList // ignore: cast_nullable_to_non_nullable
              as List<LessonTaskList>?,
      assets: freezed == assets
          ? _value.assets
          : assets // ignore: cast_nullable_to_non_nullable
              as List<Asset>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AttendanceInfoCopyWith<$Res>? get attendanceInfo {
    if (_value.attendanceInfo == null) {
      return null;
    }

    return $AttendanceInfoCopyWith<$Res>(_value.attendanceInfo!, (value) {
      return _then(_value.copyWith(attendanceInfo: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $BurialResourcesCopyWith<$Res>? get burialResources {
    if (_value.burialResources == null) {
      return null;
    }

    return $BurialResourcesCopyWith<$Res>(_value.burialResources!, (value) {
      return _then(_value.copyWith(burialResources: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ExtendCopyWith<$Res> implements $ExtendCopyWith<$Res> {
  factory _$$_ExtendCopyWith(_$_Extend value, $Res Function(_$_Extend) then) =
      __$$_ExtendCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AttendanceInfo? attendanceInfo,
      BurialResources? burialResources,
      List<LessonTaskList>? lessonTaskList,
      List<Asset>? assets});

  @override
  $AttendanceInfoCopyWith<$Res>? get attendanceInfo;
  @override
  $BurialResourcesCopyWith<$Res>? get burialResources;
}

/// @nodoc
class __$$_ExtendCopyWithImpl<$Res>
    extends _$ExtendCopyWithImpl<$Res, _$_Extend>
    implements _$$_ExtendCopyWith<$Res> {
  __$$_ExtendCopyWithImpl(_$_Extend _value, $Res Function(_$_Extend) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? attendanceInfo = freezed,
    Object? burialResources = freezed,
    Object? lessonTaskList = freezed,
    Object? assets = freezed,
  }) {
    return _then(_$_Extend(
      attendanceInfo: freezed == attendanceInfo
          ? _value.attendanceInfo
          : attendanceInfo // ignore: cast_nullable_to_non_nullable
              as AttendanceInfo?,
      burialResources: freezed == burialResources
          ? _value.burialResources
          : burialResources // ignore: cast_nullable_to_non_nullable
              as BurialResources?,
      lessonTaskList: freezed == lessonTaskList
          ? _value._lessonTaskList
          : lessonTaskList // ignore: cast_nullable_to_non_nullable
              as List<LessonTaskList>?,
      assets: freezed == assets
          ? _value._assets
          : assets // ignore: cast_nullable_to_non_nullable
              as List<Asset>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Extend implements _Extend {
  const _$_Extend(
      {this.attendanceInfo,
      this.burialResources,
      final List<LessonTaskList>? lessonTaskList,
      final List<Asset>? assets})
      : _lessonTaskList = lessonTaskList,
        _assets = assets;

  factory _$_Extend.fromJson(Map<String, dynamic> json) =>
      _$$_ExtendFromJson(json);

  @override
  final AttendanceInfo? attendanceInfo;
  @override
  final BurialResources? burialResources;
  final List<LessonTaskList>? _lessonTaskList;
  @override
  List<LessonTaskList>? get lessonTaskList {
    final value = _lessonTaskList;
    if (value == null) return null;
    if (_lessonTaskList is EqualUnmodifiableListView) return _lessonTaskList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Asset>? _assets;
  @override
  List<Asset>? get assets {
    final value = _assets;
    if (value == null) return null;
    if (_assets is EqualUnmodifiableListView) return _assets;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Extend(attendanceInfo: $attendanceInfo, burialResources: $burialResources, lessonTaskList: $lessonTaskList, assets: $assets)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Extend &&
            (identical(other.attendanceInfo, attendanceInfo) ||
                other.attendanceInfo == attendanceInfo) &&
            (identical(other.burialResources, burialResources) ||
                other.burialResources == burialResources) &&
            const DeepCollectionEquality()
                .equals(other._lessonTaskList, _lessonTaskList) &&
            const DeepCollectionEquality().equals(other._assets, _assets));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      attendanceInfo,
      burialResources,
      const DeepCollectionEquality().hash(_lessonTaskList),
      const DeepCollectionEquality().hash(_assets));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ExtendCopyWith<_$_Extend> get copyWith =>
      __$$_ExtendCopyWithImpl<_$_Extend>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ExtendToJson(
      this,
    );
  }
}

abstract class _Extend implements Extend {
  const factory _Extend(
      {final AttendanceInfo? attendanceInfo,
      final BurialResources? burialResources,
      final List<LessonTaskList>? lessonTaskList,
      final List<Asset>? assets}) = _$_Extend;

  factory _Extend.fromJson(Map<String, dynamic> json) = _$_Extend.fromJson;

  @override
  AttendanceInfo? get attendanceInfo;
  @override
  BurialResources? get burialResources;
  @override
  List<LessonTaskList>? get lessonTaskList;
  @override
  List<Asset>? get assets;
  @override
  @JsonKey(ignore: true)
  _$$_ExtendCopyWith<_$_Extend> get copyWith =>
      throw _privateConstructorUsedError;
}

Asset _$AssetFromJson(Map<String, dynamic> json) {
  return _Asset.fromJson(json);
}

/// @nodoc
mixin _$Asset {
  String? get propId => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError; // 1-复活道具
  int? get count => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AssetCopyWith<Asset> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AssetCopyWith<$Res> {
  factory $AssetCopyWith(Asset value, $Res Function(Asset) then) =
      _$AssetCopyWithImpl<$Res, Asset>;
  @useResult
  $Res call({String? propId, int? type, int? count});
}

/// @nodoc
class _$AssetCopyWithImpl<$Res, $Val extends Asset>
    implements $AssetCopyWith<$Res> {
  _$AssetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? propId = freezed,
    Object? type = freezed,
    Object? count = freezed,
  }) {
    return _then(_value.copyWith(
      propId: freezed == propId
          ? _value.propId
          : propId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_AssetCopyWith<$Res> implements $AssetCopyWith<$Res> {
  factory _$$_AssetCopyWith(_$_Asset value, $Res Function(_$_Asset) then) =
      __$$_AssetCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? propId, int? type, int? count});
}

/// @nodoc
class __$$_AssetCopyWithImpl<$Res> extends _$AssetCopyWithImpl<$Res, _$_Asset>
    implements _$$_AssetCopyWith<$Res> {
  __$$_AssetCopyWithImpl(_$_Asset _value, $Res Function(_$_Asset) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? propId = freezed,
    Object? type = freezed,
    Object? count = freezed,
  }) {
    return _then(_$_Asset(
      propId: freezed == propId
          ? _value.propId
          : propId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Asset implements _Asset {
  const _$_Asset({this.propId, this.type, this.count});

  factory _$_Asset.fromJson(Map<String, dynamic> json) =>
      _$$_AssetFromJson(json);

  @override
  final String? propId;
  @override
  final int? type;
// 1-复活道具
  @override
  final int? count;

  @override
  String toString() {
    return 'Asset(propId: $propId, type: $type, count: $count)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Asset &&
            (identical(other.propId, propId) || other.propId == propId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.count, count) || other.count == count));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, propId, type, count);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AssetCopyWith<_$_Asset> get copyWith =>
      __$$_AssetCopyWithImpl<_$_Asset>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AssetToJson(
      this,
    );
  }
}

abstract class _Asset implements Asset {
  const factory _Asset(
      {final String? propId, final int? type, final int? count}) = _$_Asset;

  factory _Asset.fromJson(Map<String, dynamic> json) = _$_Asset.fromJson;

  @override
  String? get propId;
  @override
  int? get type;
  @override // 1-复活道具
  int? get count;
  @override
  @JsonKey(ignore: true)
  _$$_AssetCopyWith<_$_Asset> get copyWith =>
      throw _privateConstructorUsedError;
}

BuyAsset _$BuyAssetFromJson(Map<String, dynamic> json) {
  return _BuyAsset.fromJson(json);
}

/// @nodoc
mixin _$BuyAsset {
  String? get propId => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError; // 2001-复活道具
  int? get count => throw _privateConstructorUsedError; // 数量
  int? get price => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BuyAssetCopyWith<BuyAsset> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyAssetCopyWith<$Res> {
  factory $BuyAssetCopyWith(BuyAsset value, $Res Function(BuyAsset) then) =
      _$BuyAssetCopyWithImpl<$Res, BuyAsset>;
  @useResult
  $Res call({String? propId, int? type, int? count, int? price});
}

/// @nodoc
class _$BuyAssetCopyWithImpl<$Res, $Val extends BuyAsset>
    implements $BuyAssetCopyWith<$Res> {
  _$BuyAssetCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? propId = freezed,
    Object? type = freezed,
    Object? count = freezed,
    Object? price = freezed,
  }) {
    return _then(_value.copyWith(
      propId: freezed == propId
          ? _value.propId
          : propId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BuyAssetCopyWith<$Res> implements $BuyAssetCopyWith<$Res> {
  factory _$$_BuyAssetCopyWith(
          _$_BuyAsset value, $Res Function(_$_BuyAsset) then) =
      __$$_BuyAssetCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? propId, int? type, int? count, int? price});
}

/// @nodoc
class __$$_BuyAssetCopyWithImpl<$Res>
    extends _$BuyAssetCopyWithImpl<$Res, _$_BuyAsset>
    implements _$$_BuyAssetCopyWith<$Res> {
  __$$_BuyAssetCopyWithImpl(
      _$_BuyAsset _value, $Res Function(_$_BuyAsset) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? propId = freezed,
    Object? type = freezed,
    Object? count = freezed,
    Object? price = freezed,
  }) {
    return _then(_$_BuyAsset(
      propId: freezed == propId
          ? _value.propId
          : propId // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      count: freezed == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BuyAsset implements _BuyAsset {
  const _$_BuyAsset({this.propId, this.type, this.count, this.price});

  factory _$_BuyAsset.fromJson(Map<String, dynamic> json) =>
      _$$_BuyAssetFromJson(json);

  @override
  final String? propId;
  @override
  final int? type;
// 2001-复活道具
  @override
  final int? count;
// 数量
  @override
  final int? price;

  @override
  String toString() {
    return 'BuyAsset(propId: $propId, type: $type, count: $count, price: $price)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BuyAsset &&
            (identical(other.propId, propId) || other.propId == propId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.price, price) || other.price == price));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, propId, type, count, price);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BuyAssetCopyWith<_$_BuyAsset> get copyWith =>
      __$$_BuyAssetCopyWithImpl<_$_BuyAsset>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BuyAssetToJson(
      this,
    );
  }
}

abstract class _BuyAsset implements BuyAsset {
  const factory _BuyAsset(
      {final String? propId,
      final int? type,
      final int? count,
      final int? price}) = _$_BuyAsset;

  factory _BuyAsset.fromJson(Map<String, dynamic> json) = _$_BuyAsset.fromJson;

  @override
  String? get propId;
  @override
  int? get type;
  @override // 2001-复活道具
  int? get count;
  @override // 数量
  int? get price;
  @override
  @JsonKey(ignore: true)
  _$$_BuyAssetCopyWith<_$_BuyAsset> get copyWith =>
      throw _privateConstructorUsedError;
}

AttendanceInfo _$AttendanceInfoFromJson(Map<String, dynamic> json) {
  return _AttendanceInfo.fromJson(json);
}

/// @nodoc
mixin _$AttendanceInfo {
  int? get state => throw _privateConstructorUsedError; // 状态（1-笑脸、2-冻结、3-嗝屁）
  int? get lastState =>
      throw _privateConstructorUsedError; // 上一次状态（1-笑脸、2-冻结、3-嗝屁）
  int? get dayCount => throw _privateConstructorUsedError; // 连续打卡天数
  int? get historyCount => throw _privateConstructorUsedError; // 历史连续打卡天数
  int? get bestDayCount => throw _privateConstructorUsedError; // 最佳连续打卡天数
  int? get featureCount => throw _privateConstructorUsedError; // 预计算天数
  int? get featureBestCount =>
      throw _privateConstructorUsedError; // 使用道具后最佳连续天数
  int? get frostDayCount => throw _privateConstructorUsedError; // 冻结天数
  int? get canIncCount => throw _privateConstructorUsedError; // 完成当前课时可增加多少天数
  String? get stateTips => throw _privateConstructorUsedError; // 不同状态下的提升文案
  int? get ipType =>
      throw _privateConstructorUsedError; // 形象IP类型（根据科目配置返回, 1-叫叫，2-绿豆）
  int? get useAssetType => throw _privateConstructorUsedError; // 资产类型 (1学豆 4宝石)
  List<Asset>? get prop => throw _privateConstructorUsedError; // 道具
  List<BuyAsset>? get buyProp => throw _privateConstructorUsedError; // 可购买道具
  String? get courseKey => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  String? get dieEnterAudioUrl => throw _privateConstructorUsedError; // 嗝屁进入音频
  String? get audioUrl => throw _privateConstructorUsedError; // 火焰变化语音
  GuidePage? get guidePage => throw _privateConstructorUsedError;
  List<SceneValue>? get audioList => throw _privateConstructorUsedError; //音频列表
  List<SceneValue>? get pageTipsList =>
      throw _privateConstructorUsedError; // tips列表
  ShareInfo? get shareInfo => throw _privateConstructorUsedError; // 分享信息
  int? get purchaseSwitch => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AttendanceInfoCopyWith<AttendanceInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AttendanceInfoCopyWith<$Res> {
  factory $AttendanceInfoCopyWith(
          AttendanceInfo value, $Res Function(AttendanceInfo) then) =
      _$AttendanceInfoCopyWithImpl<$Res, AttendanceInfo>;
  @useResult
  $Res call(
      {int? state,
      int? lastState,
      int? dayCount,
      int? historyCount,
      int? bestDayCount,
      int? featureCount,
      int? featureBestCount,
      int? frostDayCount,
      int? canIncCount,
      String? stateTips,
      int? ipType,
      int? useAssetType,
      List<Asset>? prop,
      List<BuyAsset>? buyProp,
      String? courseKey,
      int? lessonId,
      String? dieEnterAudioUrl,
      String? audioUrl,
      GuidePage? guidePage,
      List<SceneValue>? audioList,
      List<SceneValue>? pageTipsList,
      ShareInfo? shareInfo,
      int? purchaseSwitch});

  $GuidePageCopyWith<$Res>? get guidePage;
  $ShareInfoCopyWith<$Res>? get shareInfo;
}

/// @nodoc
class _$AttendanceInfoCopyWithImpl<$Res, $Val extends AttendanceInfo>
    implements $AttendanceInfoCopyWith<$Res> {
  _$AttendanceInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = freezed,
    Object? lastState = freezed,
    Object? dayCount = freezed,
    Object? historyCount = freezed,
    Object? bestDayCount = freezed,
    Object? featureCount = freezed,
    Object? featureBestCount = freezed,
    Object? frostDayCount = freezed,
    Object? canIncCount = freezed,
    Object? stateTips = freezed,
    Object? ipType = freezed,
    Object? useAssetType = freezed,
    Object? prop = freezed,
    Object? buyProp = freezed,
    Object? courseKey = freezed,
    Object? lessonId = freezed,
    Object? dieEnterAudioUrl = freezed,
    Object? audioUrl = freezed,
    Object? guidePage = freezed,
    Object? audioList = freezed,
    Object? pageTipsList = freezed,
    Object? shareInfo = freezed,
    Object? purchaseSwitch = freezed,
  }) {
    return _then(_value.copyWith(
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      lastState: freezed == lastState
          ? _value.lastState
          : lastState // ignore: cast_nullable_to_non_nullable
              as int?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      historyCount: freezed == historyCount
          ? _value.historyCount
          : historyCount // ignore: cast_nullable_to_non_nullable
              as int?,
      bestDayCount: freezed == bestDayCount
          ? _value.bestDayCount
          : bestDayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      featureCount: freezed == featureCount
          ? _value.featureCount
          : featureCount // ignore: cast_nullable_to_non_nullable
              as int?,
      featureBestCount: freezed == featureBestCount
          ? _value.featureBestCount
          : featureBestCount // ignore: cast_nullable_to_non_nullable
              as int?,
      frostDayCount: freezed == frostDayCount
          ? _value.frostDayCount
          : frostDayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      canIncCount: freezed == canIncCount
          ? _value.canIncCount
          : canIncCount // ignore: cast_nullable_to_non_nullable
              as int?,
      stateTips: freezed == stateTips
          ? _value.stateTips
          : stateTips // ignore: cast_nullable_to_non_nullable
              as String?,
      ipType: freezed == ipType
          ? _value.ipType
          : ipType // ignore: cast_nullable_to_non_nullable
              as int?,
      useAssetType: freezed == useAssetType
          ? _value.useAssetType
          : useAssetType // ignore: cast_nullable_to_non_nullable
              as int?,
      prop: freezed == prop
          ? _value.prop
          : prop // ignore: cast_nullable_to_non_nullable
              as List<Asset>?,
      buyProp: freezed == buyProp
          ? _value.buyProp
          : buyProp // ignore: cast_nullable_to_non_nullable
              as List<BuyAsset>?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      dieEnterAudioUrl: freezed == dieEnterAudioUrl
          ? _value.dieEnterAudioUrl
          : dieEnterAudioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: freezed == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      guidePage: freezed == guidePage
          ? _value.guidePage
          : guidePage // ignore: cast_nullable_to_non_nullable
              as GuidePage?,
      audioList: freezed == audioList
          ? _value.audioList
          : audioList // ignore: cast_nullable_to_non_nullable
              as List<SceneValue>?,
      pageTipsList: freezed == pageTipsList
          ? _value.pageTipsList
          : pageTipsList // ignore: cast_nullable_to_non_nullable
              as List<SceneValue>?,
      shareInfo: freezed == shareInfo
          ? _value.shareInfo
          : shareInfo // ignore: cast_nullable_to_non_nullable
              as ShareInfo?,
      purchaseSwitch: freezed == purchaseSwitch
          ? _value.purchaseSwitch
          : purchaseSwitch // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GuidePageCopyWith<$Res>? get guidePage {
    if (_value.guidePage == null) {
      return null;
    }

    return $GuidePageCopyWith<$Res>(_value.guidePage!, (value) {
      return _then(_value.copyWith(guidePage: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $ShareInfoCopyWith<$Res>? get shareInfo {
    if (_value.shareInfo == null) {
      return null;
    }

    return $ShareInfoCopyWith<$Res>(_value.shareInfo!, (value) {
      return _then(_value.copyWith(shareInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_AttendanceInfoCopyWith<$Res>
    implements $AttendanceInfoCopyWith<$Res> {
  factory _$$_AttendanceInfoCopyWith(
          _$_AttendanceInfo value, $Res Function(_$_AttendanceInfo) then) =
      __$$_AttendanceInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? state,
      int? lastState,
      int? dayCount,
      int? historyCount,
      int? bestDayCount,
      int? featureCount,
      int? featureBestCount,
      int? frostDayCount,
      int? canIncCount,
      String? stateTips,
      int? ipType,
      int? useAssetType,
      List<Asset>? prop,
      List<BuyAsset>? buyProp,
      String? courseKey,
      int? lessonId,
      String? dieEnterAudioUrl,
      String? audioUrl,
      GuidePage? guidePage,
      List<SceneValue>? audioList,
      List<SceneValue>? pageTipsList,
      ShareInfo? shareInfo,
      int? purchaseSwitch});

  @override
  $GuidePageCopyWith<$Res>? get guidePage;
  @override
  $ShareInfoCopyWith<$Res>? get shareInfo;
}

/// @nodoc
class __$$_AttendanceInfoCopyWithImpl<$Res>
    extends _$AttendanceInfoCopyWithImpl<$Res, _$_AttendanceInfo>
    implements _$$_AttendanceInfoCopyWith<$Res> {
  __$$_AttendanceInfoCopyWithImpl(
      _$_AttendanceInfo _value, $Res Function(_$_AttendanceInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = freezed,
    Object? lastState = freezed,
    Object? dayCount = freezed,
    Object? historyCount = freezed,
    Object? bestDayCount = freezed,
    Object? featureCount = freezed,
    Object? featureBestCount = freezed,
    Object? frostDayCount = freezed,
    Object? canIncCount = freezed,
    Object? stateTips = freezed,
    Object? ipType = freezed,
    Object? useAssetType = freezed,
    Object? prop = freezed,
    Object? buyProp = freezed,
    Object? courseKey = freezed,
    Object? lessonId = freezed,
    Object? dieEnterAudioUrl = freezed,
    Object? audioUrl = freezed,
    Object? guidePage = freezed,
    Object? audioList = freezed,
    Object? pageTipsList = freezed,
    Object? shareInfo = freezed,
    Object? purchaseSwitch = freezed,
  }) {
    return _then(_$_AttendanceInfo(
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      lastState: freezed == lastState
          ? _value.lastState
          : lastState // ignore: cast_nullable_to_non_nullable
              as int?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      historyCount: freezed == historyCount
          ? _value.historyCount
          : historyCount // ignore: cast_nullable_to_non_nullable
              as int?,
      bestDayCount: freezed == bestDayCount
          ? _value.bestDayCount
          : bestDayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      featureCount: freezed == featureCount
          ? _value.featureCount
          : featureCount // ignore: cast_nullable_to_non_nullable
              as int?,
      featureBestCount: freezed == featureBestCount
          ? _value.featureBestCount
          : featureBestCount // ignore: cast_nullable_to_non_nullable
              as int?,
      frostDayCount: freezed == frostDayCount
          ? _value.frostDayCount
          : frostDayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      canIncCount: freezed == canIncCount
          ? _value.canIncCount
          : canIncCount // ignore: cast_nullable_to_non_nullable
              as int?,
      stateTips: freezed == stateTips
          ? _value.stateTips
          : stateTips // ignore: cast_nullable_to_non_nullable
              as String?,
      ipType: freezed == ipType
          ? _value.ipType
          : ipType // ignore: cast_nullable_to_non_nullable
              as int?,
      useAssetType: freezed == useAssetType
          ? _value.useAssetType
          : useAssetType // ignore: cast_nullable_to_non_nullable
              as int?,
      prop: freezed == prop
          ? _value._prop
          : prop // ignore: cast_nullable_to_non_nullable
              as List<Asset>?,
      buyProp: freezed == buyProp
          ? _value._buyProp
          : buyProp // ignore: cast_nullable_to_non_nullable
              as List<BuyAsset>?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      dieEnterAudioUrl: freezed == dieEnterAudioUrl
          ? _value.dieEnterAudioUrl
          : dieEnterAudioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: freezed == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      guidePage: freezed == guidePage
          ? _value.guidePage
          : guidePage // ignore: cast_nullable_to_non_nullable
              as GuidePage?,
      audioList: freezed == audioList
          ? _value._audioList
          : audioList // ignore: cast_nullable_to_non_nullable
              as List<SceneValue>?,
      pageTipsList: freezed == pageTipsList
          ? _value._pageTipsList
          : pageTipsList // ignore: cast_nullable_to_non_nullable
              as List<SceneValue>?,
      shareInfo: freezed == shareInfo
          ? _value.shareInfo
          : shareInfo // ignore: cast_nullable_to_non_nullable
              as ShareInfo?,
      purchaseSwitch: freezed == purchaseSwitch
          ? _value.purchaseSwitch
          : purchaseSwitch // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_AttendanceInfo implements _AttendanceInfo {
  const _$_AttendanceInfo(
      {this.state,
      this.lastState,
      this.dayCount,
      this.historyCount,
      this.bestDayCount,
      this.featureCount,
      this.featureBestCount,
      this.frostDayCount,
      this.canIncCount,
      this.stateTips,
      this.ipType,
      this.useAssetType,
      final List<Asset>? prop,
      final List<BuyAsset>? buyProp,
      this.courseKey,
      this.lessonId,
      this.dieEnterAudioUrl,
      this.audioUrl,
      this.guidePage,
      final List<SceneValue>? audioList,
      final List<SceneValue>? pageTipsList,
      this.shareInfo,
      this.purchaseSwitch})
      : _prop = prop,
        _buyProp = buyProp,
        _audioList = audioList,
        _pageTipsList = pageTipsList;

  factory _$_AttendanceInfo.fromJson(Map<String, dynamic> json) =>
      _$$_AttendanceInfoFromJson(json);

  @override
  final int? state;
// 状态（1-笑脸、2-冻结、3-嗝屁）
  @override
  final int? lastState;
// 上一次状态（1-笑脸、2-冻结、3-嗝屁）
  @override
  final int? dayCount;
// 连续打卡天数
  @override
  final int? historyCount;
// 历史连续打卡天数
  @override
  final int? bestDayCount;
// 最佳连续打卡天数
  @override
  final int? featureCount;
// 预计算天数
  @override
  final int? featureBestCount;
// 使用道具后最佳连续天数
  @override
  final int? frostDayCount;
// 冻结天数
  @override
  final int? canIncCount;
// 完成当前课时可增加多少天数
  @override
  final String? stateTips;
// 不同状态下的提升文案
  @override
  final int? ipType;
// 形象IP类型（根据科目配置返回, 1-叫叫，2-绿豆）
  @override
  final int? useAssetType;
// 资产类型 (1学豆 4宝石)
  final List<Asset>? _prop;
// 资产类型 (1学豆 4宝石)
  @override
  List<Asset>? get prop {
    final value = _prop;
    if (value == null) return null;
    if (_prop is EqualUnmodifiableListView) return _prop;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 道具
  final List<BuyAsset>? _buyProp;
// 道具
  @override
  List<BuyAsset>? get buyProp {
    final value = _buyProp;
    if (value == null) return null;
    if (_buyProp is EqualUnmodifiableListView) return _buyProp;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// 可购买道具
  @override
  final String? courseKey;
  @override
  final int? lessonId;
  @override
  final String? dieEnterAudioUrl;
// 嗝屁进入音频
  @override
  final String? audioUrl;
// 火焰变化语音
  @override
  final GuidePage? guidePage;
  final List<SceneValue>? _audioList;
  @override
  List<SceneValue>? get audioList {
    final value = _audioList;
    if (value == null) return null;
    if (_audioList is EqualUnmodifiableListView) return _audioList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//音频列表
  final List<SceneValue>? _pageTipsList;
//音频列表
  @override
  List<SceneValue>? get pageTipsList {
    final value = _pageTipsList;
    if (value == null) return null;
    if (_pageTipsList is EqualUnmodifiableListView) return _pageTipsList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// tips列表
  @override
  final ShareInfo? shareInfo;
// 分享信息
  @override
  final int? purchaseSwitch;

  @override
  String toString() {
    return 'AttendanceInfo(state: $state, lastState: $lastState, dayCount: $dayCount, historyCount: $historyCount, bestDayCount: $bestDayCount, featureCount: $featureCount, featureBestCount: $featureBestCount, frostDayCount: $frostDayCount, canIncCount: $canIncCount, stateTips: $stateTips, ipType: $ipType, useAssetType: $useAssetType, prop: $prop, buyProp: $buyProp, courseKey: $courseKey, lessonId: $lessonId, dieEnterAudioUrl: $dieEnterAudioUrl, audioUrl: $audioUrl, guidePage: $guidePage, audioList: $audioList, pageTipsList: $pageTipsList, shareInfo: $shareInfo, purchaseSwitch: $purchaseSwitch)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_AttendanceInfo &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.lastState, lastState) ||
                other.lastState == lastState) &&
            (identical(other.dayCount, dayCount) ||
                other.dayCount == dayCount) &&
            (identical(other.historyCount, historyCount) ||
                other.historyCount == historyCount) &&
            (identical(other.bestDayCount, bestDayCount) ||
                other.bestDayCount == bestDayCount) &&
            (identical(other.featureCount, featureCount) ||
                other.featureCount == featureCount) &&
            (identical(other.featureBestCount, featureBestCount) ||
                other.featureBestCount == featureBestCount) &&
            (identical(other.frostDayCount, frostDayCount) ||
                other.frostDayCount == frostDayCount) &&
            (identical(other.canIncCount, canIncCount) ||
                other.canIncCount == canIncCount) &&
            (identical(other.stateTips, stateTips) ||
                other.stateTips == stateTips) &&
            (identical(other.ipType, ipType) || other.ipType == ipType) &&
            (identical(other.useAssetType, useAssetType) ||
                other.useAssetType == useAssetType) &&
            const DeepCollectionEquality().equals(other._prop, _prop) &&
            const DeepCollectionEquality().equals(other._buyProp, _buyProp) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.dieEnterAudioUrl, dieEnterAudioUrl) ||
                other.dieEnterAudioUrl == dieEnterAudioUrl) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.guidePage, guidePage) ||
                other.guidePage == guidePage) &&
            const DeepCollectionEquality()
                .equals(other._audioList, _audioList) &&
            const DeepCollectionEquality()
                .equals(other._pageTipsList, _pageTipsList) &&
            (identical(other.shareInfo, shareInfo) ||
                other.shareInfo == shareInfo) &&
            (identical(other.purchaseSwitch, purchaseSwitch) ||
                other.purchaseSwitch == purchaseSwitch));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        state,
        lastState,
        dayCount,
        historyCount,
        bestDayCount,
        featureCount,
        featureBestCount,
        frostDayCount,
        canIncCount,
        stateTips,
        ipType,
        useAssetType,
        const DeepCollectionEquality().hash(_prop),
        const DeepCollectionEquality().hash(_buyProp),
        courseKey,
        lessonId,
        dieEnterAudioUrl,
        audioUrl,
        guidePage,
        const DeepCollectionEquality().hash(_audioList),
        const DeepCollectionEquality().hash(_pageTipsList),
        shareInfo,
        purchaseSwitch
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_AttendanceInfoCopyWith<_$_AttendanceInfo> get copyWith =>
      __$$_AttendanceInfoCopyWithImpl<_$_AttendanceInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_AttendanceInfoToJson(
      this,
    );
  }
}

abstract class _AttendanceInfo implements AttendanceInfo {
  const factory _AttendanceInfo(
      {final int? state,
      final int? lastState,
      final int? dayCount,
      final int? historyCount,
      final int? bestDayCount,
      final int? featureCount,
      final int? featureBestCount,
      final int? frostDayCount,
      final int? canIncCount,
      final String? stateTips,
      final int? ipType,
      final int? useAssetType,
      final List<Asset>? prop,
      final List<BuyAsset>? buyProp,
      final String? courseKey,
      final int? lessonId,
      final String? dieEnterAudioUrl,
      final String? audioUrl,
      final GuidePage? guidePage,
      final List<SceneValue>? audioList,
      final List<SceneValue>? pageTipsList,
      final ShareInfo? shareInfo,
      final int? purchaseSwitch}) = _$_AttendanceInfo;

  factory _AttendanceInfo.fromJson(Map<String, dynamic> json) =
      _$_AttendanceInfo.fromJson;

  @override
  int? get state;
  @override // 状态（1-笑脸、2-冻结、3-嗝屁）
  int? get lastState;
  @override // 上一次状态（1-笑脸、2-冻结、3-嗝屁）
  int? get dayCount;
  @override // 连续打卡天数
  int? get historyCount;
  @override // 历史连续打卡天数
  int? get bestDayCount;
  @override // 最佳连续打卡天数
  int? get featureCount;
  @override // 预计算天数
  int? get featureBestCount;
  @override // 使用道具后最佳连续天数
  int? get frostDayCount;
  @override // 冻结天数
  int? get canIncCount;
  @override // 完成当前课时可增加多少天数
  String? get stateTips;
  @override // 不同状态下的提升文案
  int? get ipType;
  @override // 形象IP类型（根据科目配置返回, 1-叫叫，2-绿豆）
  int? get useAssetType;
  @override // 资产类型 (1学豆 4宝石)
  List<Asset>? get prop;
  @override // 道具
  List<BuyAsset>? get buyProp;
  @override // 可购买道具
  String? get courseKey;
  @override
  int? get lessonId;
  @override
  String? get dieEnterAudioUrl;
  @override // 嗝屁进入音频
  String? get audioUrl;
  @override // 火焰变化语音
  GuidePage? get guidePage;
  @override
  List<SceneValue>? get audioList;
  @override //音频列表
  List<SceneValue>? get pageTipsList;
  @override // tips列表
  ShareInfo? get shareInfo;
  @override // 分享信息
  int? get purchaseSwitch;
  @override
  @JsonKey(ignore: true)
  _$$_AttendanceInfoCopyWith<_$_AttendanceInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

ShareInfo _$ShareInfoFromJson(Map<String, dynamic> json) {
  return _ShareInfo.fromJson(json);
}

/// @nodoc
mixin _$ShareInfo {
  String? get introduceUrl => throw _privateConstructorUsedError; // 转介绍地址
  String? get backgroundImg => throw _privateConstructorUsedError; // 背景图
  String? get dressImg => throw _privateConstructorUsedError; // 装扮图
  String? get avatarFrameImg => throw _privateConstructorUsedError; // 头像框图
  String? get subjectName => throw _privateConstructorUsedError; // 科目名称
  String? get nickname => throw _privateConstructorUsedError; // 脱敏后的昵称
  String? get shareDesc => throw _privateConstructorUsedError;
  String? get backgroundText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ShareInfoCopyWith<ShareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShareInfoCopyWith<$Res> {
  factory $ShareInfoCopyWith(ShareInfo value, $Res Function(ShareInfo) then) =
      _$ShareInfoCopyWithImpl<$Res, ShareInfo>;
  @useResult
  $Res call(
      {String? introduceUrl,
      String? backgroundImg,
      String? dressImg,
      String? avatarFrameImg,
      String? subjectName,
      String? nickname,
      String? shareDesc,
      String? backgroundText});
}

/// @nodoc
class _$ShareInfoCopyWithImpl<$Res, $Val extends ShareInfo>
    implements $ShareInfoCopyWith<$Res> {
  _$ShareInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? introduceUrl = freezed,
    Object? backgroundImg = freezed,
    Object? dressImg = freezed,
    Object? avatarFrameImg = freezed,
    Object? subjectName = freezed,
    Object? nickname = freezed,
    Object? shareDesc = freezed,
    Object? backgroundText = freezed,
  }) {
    return _then(_value.copyWith(
      introduceUrl: freezed == introduceUrl
          ? _value.introduceUrl
          : introduceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundImg: freezed == backgroundImg
          ? _value.backgroundImg
          : backgroundImg // ignore: cast_nullable_to_non_nullable
              as String?,
      dressImg: freezed == dressImg
          ? _value.dressImg
          : dressImg // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarFrameImg: freezed == avatarFrameImg
          ? _value.avatarFrameImg
          : avatarFrameImg // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      shareDesc: freezed == shareDesc
          ? _value.shareDesc
          : shareDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundText: freezed == backgroundText
          ? _value.backgroundText
          : backgroundText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ShareInfoCopyWith<$Res> implements $ShareInfoCopyWith<$Res> {
  factory _$$_ShareInfoCopyWith(
          _$_ShareInfo value, $Res Function(_$_ShareInfo) then) =
      __$$_ShareInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? introduceUrl,
      String? backgroundImg,
      String? dressImg,
      String? avatarFrameImg,
      String? subjectName,
      String? nickname,
      String? shareDesc,
      String? backgroundText});
}

/// @nodoc
class __$$_ShareInfoCopyWithImpl<$Res>
    extends _$ShareInfoCopyWithImpl<$Res, _$_ShareInfo>
    implements _$$_ShareInfoCopyWith<$Res> {
  __$$_ShareInfoCopyWithImpl(
      _$_ShareInfo _value, $Res Function(_$_ShareInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? introduceUrl = freezed,
    Object? backgroundImg = freezed,
    Object? dressImg = freezed,
    Object? avatarFrameImg = freezed,
    Object? subjectName = freezed,
    Object? nickname = freezed,
    Object? shareDesc = freezed,
    Object? backgroundText = freezed,
  }) {
    return _then(_$_ShareInfo(
      introduceUrl: freezed == introduceUrl
          ? _value.introduceUrl
          : introduceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundImg: freezed == backgroundImg
          ? _value.backgroundImg
          : backgroundImg // ignore: cast_nullable_to_non_nullable
              as String?,
      dressImg: freezed == dressImg
          ? _value.dressImg
          : dressImg // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarFrameImg: freezed == avatarFrameImg
          ? _value.avatarFrameImg
          : avatarFrameImg // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      shareDesc: freezed == shareDesc
          ? _value.shareDesc
          : shareDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundText: freezed == backgroundText
          ? _value.backgroundText
          : backgroundText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ShareInfo implements _ShareInfo {
  const _$_ShareInfo(
      {this.introduceUrl,
      this.backgroundImg,
      this.dressImg,
      this.avatarFrameImg,
      this.subjectName,
      this.nickname,
      this.shareDesc,
      this.backgroundText});

  factory _$_ShareInfo.fromJson(Map<String, dynamic> json) =>
      _$$_ShareInfoFromJson(json);

  @override
  final String? introduceUrl;
// 转介绍地址
  @override
  final String? backgroundImg;
// 背景图
  @override
  final String? dressImg;
// 装扮图
  @override
  final String? avatarFrameImg;
// 头像框图
  @override
  final String? subjectName;
// 科目名称
  @override
  final String? nickname;
// 脱敏后的昵称
  @override
  final String? shareDesc;
  @override
  final String? backgroundText;

  @override
  String toString() {
    return 'ShareInfo(introduceUrl: $introduceUrl, backgroundImg: $backgroundImg, dressImg: $dressImg, avatarFrameImg: $avatarFrameImg, subjectName: $subjectName, nickname: $nickname, shareDesc: $shareDesc, backgroundText: $backgroundText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ShareInfo &&
            (identical(other.introduceUrl, introduceUrl) ||
                other.introduceUrl == introduceUrl) &&
            (identical(other.backgroundImg, backgroundImg) ||
                other.backgroundImg == backgroundImg) &&
            (identical(other.dressImg, dressImg) ||
                other.dressImg == dressImg) &&
            (identical(other.avatarFrameImg, avatarFrameImg) ||
                other.avatarFrameImg == avatarFrameImg) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.shareDesc, shareDesc) ||
                other.shareDesc == shareDesc) &&
            (identical(other.backgroundText, backgroundText) ||
                other.backgroundText == backgroundText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      introduceUrl,
      backgroundImg,
      dressImg,
      avatarFrameImg,
      subjectName,
      nickname,
      shareDesc,
      backgroundText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ShareInfoCopyWith<_$_ShareInfo> get copyWith =>
      __$$_ShareInfoCopyWithImpl<_$_ShareInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ShareInfoToJson(
      this,
    );
  }
}

abstract class _ShareInfo implements ShareInfo {
  const factory _ShareInfo(
      {final String? introduceUrl,
      final String? backgroundImg,
      final String? dressImg,
      final String? avatarFrameImg,
      final String? subjectName,
      final String? nickname,
      final String? shareDesc,
      final String? backgroundText}) = _$_ShareInfo;

  factory _ShareInfo.fromJson(Map<String, dynamic> json) =
      _$_ShareInfo.fromJson;

  @override
  String? get introduceUrl;
  @override // 转介绍地址
  String? get backgroundImg;
  @override // 背景图
  String? get dressImg;
  @override // 装扮图
  String? get avatarFrameImg;
  @override // 头像框图
  String? get subjectName;
  @override // 科目名称
  String? get nickname;
  @override // 脱敏后的昵称
  String? get shareDesc;
  @override
  String? get backgroundText;
  @override
  @JsonKey(ignore: true)
  _$$_ShareInfoCopyWith<_$_ShareInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

SceneValue _$SceneValueFromJson(Map<String, dynamic> json) {
  return _SceneValue.fromJson(json);
}

/// @nodoc
mixin _$SceneValue {
  String? get scene => throw _privateConstructorUsedError;
  String? get value => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SceneValueCopyWith<SceneValue> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SceneValueCopyWith<$Res> {
  factory $SceneValueCopyWith(
          SceneValue value, $Res Function(SceneValue) then) =
      _$SceneValueCopyWithImpl<$Res, SceneValue>;
  @useResult
  $Res call({String? scene, String? value});
}

/// @nodoc
class _$SceneValueCopyWithImpl<$Res, $Val extends SceneValue>
    implements $SceneValueCopyWith<$Res> {
  _$SceneValueCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scene = freezed,
    Object? value = freezed,
  }) {
    return _then(_value.copyWith(
      scene: freezed == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as String?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SceneValueCopyWith<$Res>
    implements $SceneValueCopyWith<$Res> {
  factory _$$_SceneValueCopyWith(
          _$_SceneValue value, $Res Function(_$_SceneValue) then) =
      __$$_SceneValueCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? scene, String? value});
}

/// @nodoc
class __$$_SceneValueCopyWithImpl<$Res>
    extends _$SceneValueCopyWithImpl<$Res, _$_SceneValue>
    implements _$$_SceneValueCopyWith<$Res> {
  __$$_SceneValueCopyWithImpl(
      _$_SceneValue _value, $Res Function(_$_SceneValue) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? scene = freezed,
    Object? value = freezed,
  }) {
    return _then(_$_SceneValue(
      scene: freezed == scene
          ? _value.scene
          : scene // ignore: cast_nullable_to_non_nullable
              as String?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SceneValue implements _SceneValue {
  const _$_SceneValue({this.scene, this.value});

  factory _$_SceneValue.fromJson(Map<String, dynamic> json) =>
      _$$_SceneValueFromJson(json);

  @override
  final String? scene;
  @override
  final String? value;

  @override
  String toString() {
    return 'SceneValue(scene: $scene, value: $value)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SceneValue &&
            (identical(other.scene, scene) || other.scene == scene) &&
            (identical(other.value, value) || other.value == value));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, scene, value);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SceneValueCopyWith<_$_SceneValue> get copyWith =>
      __$$_SceneValueCopyWithImpl<_$_SceneValue>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SceneValueToJson(
      this,
    );
  }
}

abstract class _SceneValue implements SceneValue {
  const factory _SceneValue({final String? scene, final String? value}) =
      _$_SceneValue;

  factory _SceneValue.fromJson(Map<String, dynamic> json) =
      _$_SceneValue.fromJson;

  @override
  String? get scene;
  @override
  String? get value;
  @override
  @JsonKey(ignore: true)
  _$$_SceneValueCopyWith<_$_SceneValue> get copyWith =>
      throw _privateConstructorUsedError;
}

GuidePage _$GuidePageFromJson(Map<String, dynamic> json) {
  return _GuidePage.fromJson(json);
}

/// @nodoc
mixin _$GuidePage {
  int? get pageType =>
      throw _privateConstructorUsedError; // 页面类型（1-连胜，2-有冰冻，3-只有嗝屁，4-正学未完成，5-里程碑奖励）
  List<Calendar>? get calendar => throw _privateConstructorUsedError;
  List<Btn>? get btns => throw _privateConstructorUsedError;
  String? get pageTips => throw _privateConstructorUsedError;
  String? get audioUrl => throw _privateConstructorUsedError; //结束页面语音
  String? get dieToFrozenAudioUrl =>
      throw _privateConstructorUsedError; // 结束嗝屁使用道具成功音频
  MilestoneInfo? get milestoneInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GuidePageCopyWith<GuidePage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GuidePageCopyWith<$Res> {
  factory $GuidePageCopyWith(GuidePage value, $Res Function(GuidePage) then) =
      _$GuidePageCopyWithImpl<$Res, GuidePage>;
  @useResult
  $Res call(
      {int? pageType,
      List<Calendar>? calendar,
      List<Btn>? btns,
      String? pageTips,
      String? audioUrl,
      String? dieToFrozenAudioUrl,
      MilestoneInfo? milestoneInfo});

  $MilestoneInfoCopyWith<$Res>? get milestoneInfo;
}

/// @nodoc
class _$GuidePageCopyWithImpl<$Res, $Val extends GuidePage>
    implements $GuidePageCopyWith<$Res> {
  _$GuidePageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageType = freezed,
    Object? calendar = freezed,
    Object? btns = freezed,
    Object? pageTips = freezed,
    Object? audioUrl = freezed,
    Object? dieToFrozenAudioUrl = freezed,
    Object? milestoneInfo = freezed,
  }) {
    return _then(_value.copyWith(
      pageType: freezed == pageType
          ? _value.pageType
          : pageType // ignore: cast_nullable_to_non_nullable
              as int?,
      calendar: freezed == calendar
          ? _value.calendar
          : calendar // ignore: cast_nullable_to_non_nullable
              as List<Calendar>?,
      btns: freezed == btns
          ? _value.btns
          : btns // ignore: cast_nullable_to_non_nullable
              as List<Btn>?,
      pageTips: freezed == pageTips
          ? _value.pageTips
          : pageTips // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: freezed == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      dieToFrozenAudioUrl: freezed == dieToFrozenAudioUrl
          ? _value.dieToFrozenAudioUrl
          : dieToFrozenAudioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      milestoneInfo: freezed == milestoneInfo
          ? _value.milestoneInfo
          : milestoneInfo // ignore: cast_nullable_to_non_nullable
              as MilestoneInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MilestoneInfoCopyWith<$Res>? get milestoneInfo {
    if (_value.milestoneInfo == null) {
      return null;
    }

    return $MilestoneInfoCopyWith<$Res>(_value.milestoneInfo!, (value) {
      return _then(_value.copyWith(milestoneInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_GuidePageCopyWith<$Res> implements $GuidePageCopyWith<$Res> {
  factory _$$_GuidePageCopyWith(
          _$_GuidePage value, $Res Function(_$_GuidePage) then) =
      __$$_GuidePageCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? pageType,
      List<Calendar>? calendar,
      List<Btn>? btns,
      String? pageTips,
      String? audioUrl,
      String? dieToFrozenAudioUrl,
      MilestoneInfo? milestoneInfo});

  @override
  $MilestoneInfoCopyWith<$Res>? get milestoneInfo;
}

/// @nodoc
class __$$_GuidePageCopyWithImpl<$Res>
    extends _$GuidePageCopyWithImpl<$Res, _$_GuidePage>
    implements _$$_GuidePageCopyWith<$Res> {
  __$$_GuidePageCopyWithImpl(
      _$_GuidePage _value, $Res Function(_$_GuidePage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pageType = freezed,
    Object? calendar = freezed,
    Object? btns = freezed,
    Object? pageTips = freezed,
    Object? audioUrl = freezed,
    Object? dieToFrozenAudioUrl = freezed,
    Object? milestoneInfo = freezed,
  }) {
    return _then(_$_GuidePage(
      pageType: freezed == pageType
          ? _value.pageType
          : pageType // ignore: cast_nullable_to_non_nullable
              as int?,
      calendar: freezed == calendar
          ? _value._calendar
          : calendar // ignore: cast_nullable_to_non_nullable
              as List<Calendar>?,
      btns: freezed == btns
          ? _value._btns
          : btns // ignore: cast_nullable_to_non_nullable
              as List<Btn>?,
      pageTips: freezed == pageTips
          ? _value.pageTips
          : pageTips // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: freezed == audioUrl
          ? _value.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      dieToFrozenAudioUrl: freezed == dieToFrozenAudioUrl
          ? _value.dieToFrozenAudioUrl
          : dieToFrozenAudioUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      milestoneInfo: freezed == milestoneInfo
          ? _value.milestoneInfo
          : milestoneInfo // ignore: cast_nullable_to_non_nullable
              as MilestoneInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GuidePage implements _GuidePage {
  const _$_GuidePage(
      {this.pageType,
      final List<Calendar>? calendar,
      final List<Btn>? btns,
      this.pageTips,
      this.audioUrl,
      this.dieToFrozenAudioUrl,
      this.milestoneInfo})
      : _calendar = calendar,
        _btns = btns;

  factory _$_GuidePage.fromJson(Map<String, dynamic> json) =>
      _$$_GuidePageFromJson(json);

  @override
  final int? pageType;
// 页面类型（1-连胜，2-有冰冻，3-只有嗝屁，4-正学未完成，5-里程碑奖励）
  final List<Calendar>? _calendar;
// 页面类型（1-连胜，2-有冰冻，3-只有嗝屁，4-正学未完成，5-里程碑奖励）
  @override
  List<Calendar>? get calendar {
    final value = _calendar;
    if (value == null) return null;
    if (_calendar is EqualUnmodifiableListView) return _calendar;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Btn>? _btns;
  @override
  List<Btn>? get btns {
    final value = _btns;
    if (value == null) return null;
    if (_btns is EqualUnmodifiableListView) return _btns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? pageTips;
  @override
  final String? audioUrl;
//结束页面语音
  @override
  final String? dieToFrozenAudioUrl;
// 结束嗝屁使用道具成功音频
  @override
  final MilestoneInfo? milestoneInfo;

  @override
  String toString() {
    return 'GuidePage(pageType: $pageType, calendar: $calendar, btns: $btns, pageTips: $pageTips, audioUrl: $audioUrl, dieToFrozenAudioUrl: $dieToFrozenAudioUrl, milestoneInfo: $milestoneInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GuidePage &&
            (identical(other.pageType, pageType) ||
                other.pageType == pageType) &&
            const DeepCollectionEquality().equals(other._calendar, _calendar) &&
            const DeepCollectionEquality().equals(other._btns, _btns) &&
            (identical(other.pageTips, pageTips) ||
                other.pageTips == pageTips) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.dieToFrozenAudioUrl, dieToFrozenAudioUrl) ||
                other.dieToFrozenAudioUrl == dieToFrozenAudioUrl) &&
            (identical(other.milestoneInfo, milestoneInfo) ||
                other.milestoneInfo == milestoneInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      pageType,
      const DeepCollectionEquality().hash(_calendar),
      const DeepCollectionEquality().hash(_btns),
      pageTips,
      audioUrl,
      dieToFrozenAudioUrl,
      milestoneInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GuidePageCopyWith<_$_GuidePage> get copyWith =>
      __$$_GuidePageCopyWithImpl<_$_GuidePage>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GuidePageToJson(
      this,
    );
  }
}

abstract class _GuidePage implements GuidePage {
  const factory _GuidePage(
      {final int? pageType,
      final List<Calendar>? calendar,
      final List<Btn>? btns,
      final String? pageTips,
      final String? audioUrl,
      final String? dieToFrozenAudioUrl,
      final MilestoneInfo? milestoneInfo}) = _$_GuidePage;

  factory _GuidePage.fromJson(Map<String, dynamic> json) =
      _$_GuidePage.fromJson;

  @override
  int? get pageType;
  @override // 页面类型（1-连胜，2-有冰冻，3-只有嗝屁，4-正学未完成，5-里程碑奖励）
  List<Calendar>? get calendar;
  @override
  List<Btn>? get btns;
  @override
  String? get pageTips;
  @override
  String? get audioUrl;
  @override //结束页面语音
  String? get dieToFrozenAudioUrl;
  @override // 结束嗝屁使用道具成功音频
  MilestoneInfo? get milestoneInfo;
  @override
  @JsonKey(ignore: true)
  _$$_GuidePageCopyWith<_$_GuidePage> get copyWith =>
      throw _privateConstructorUsedError;
}

MilestoneInfo _$MilestoneInfoFromJson(Map<String, dynamic> json) {
  return _MilestoneInfo.fromJson(json);
}

/// @nodoc
mixin _$MilestoneInfo {
  /// 是否显示进度(1是0否) 包括进度条和日历样式
  int? get isShowProgress => throw _privateConstructorUsedError;

  ///进度样式(日历calendar，进度条progress)
  String? get progressStyle => throw _privateConstructorUsedError;

  /// 目标天数 进度分母
  int? get targetDay => throw _privateConstructorUsedError;
  List<MilestoneReward>? get rewardList => throw _privateConstructorUsedError;
  List<MilestoneReward>? get targetRewardList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MilestoneInfoCopyWith<MilestoneInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MilestoneInfoCopyWith<$Res> {
  factory $MilestoneInfoCopyWith(
          MilestoneInfo value, $Res Function(MilestoneInfo) then) =
      _$MilestoneInfoCopyWithImpl<$Res, MilestoneInfo>;
  @useResult
  $Res call(
      {int? isShowProgress,
      String? progressStyle,
      int? targetDay,
      List<MilestoneReward>? rewardList,
      List<MilestoneReward>? targetRewardList});
}

/// @nodoc
class _$MilestoneInfoCopyWithImpl<$Res, $Val extends MilestoneInfo>
    implements $MilestoneInfoCopyWith<$Res> {
  _$MilestoneInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isShowProgress = freezed,
    Object? progressStyle = freezed,
    Object? targetDay = freezed,
    Object? rewardList = freezed,
    Object? targetRewardList = freezed,
  }) {
    return _then(_value.copyWith(
      isShowProgress: freezed == isShowProgress
          ? _value.isShowProgress
          : isShowProgress // ignore: cast_nullable_to_non_nullable
              as int?,
      progressStyle: freezed == progressStyle
          ? _value.progressStyle
          : progressStyle // ignore: cast_nullable_to_non_nullable
              as String?,
      targetDay: freezed == targetDay
          ? _value.targetDay
          : targetDay // ignore: cast_nullable_to_non_nullable
              as int?,
      rewardList: freezed == rewardList
          ? _value.rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<MilestoneReward>?,
      targetRewardList: freezed == targetRewardList
          ? _value.targetRewardList
          : targetRewardList // ignore: cast_nullable_to_non_nullable
              as List<MilestoneReward>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MilestoneInfoCopyWith<$Res>
    implements $MilestoneInfoCopyWith<$Res> {
  factory _$$_MilestoneInfoCopyWith(
          _$_MilestoneInfo value, $Res Function(_$_MilestoneInfo) then) =
      __$$_MilestoneInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? isShowProgress,
      String? progressStyle,
      int? targetDay,
      List<MilestoneReward>? rewardList,
      List<MilestoneReward>? targetRewardList});
}

/// @nodoc
class __$$_MilestoneInfoCopyWithImpl<$Res>
    extends _$MilestoneInfoCopyWithImpl<$Res, _$_MilestoneInfo>
    implements _$$_MilestoneInfoCopyWith<$Res> {
  __$$_MilestoneInfoCopyWithImpl(
      _$_MilestoneInfo _value, $Res Function(_$_MilestoneInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isShowProgress = freezed,
    Object? progressStyle = freezed,
    Object? targetDay = freezed,
    Object? rewardList = freezed,
    Object? targetRewardList = freezed,
  }) {
    return _then(_$_MilestoneInfo(
      isShowProgress: freezed == isShowProgress
          ? _value.isShowProgress
          : isShowProgress // ignore: cast_nullable_to_non_nullable
              as int?,
      progressStyle: freezed == progressStyle
          ? _value.progressStyle
          : progressStyle // ignore: cast_nullable_to_non_nullable
              as String?,
      targetDay: freezed == targetDay
          ? _value.targetDay
          : targetDay // ignore: cast_nullable_to_non_nullable
              as int?,
      rewardList: freezed == rewardList
          ? _value._rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<MilestoneReward>?,
      targetRewardList: freezed == targetRewardList
          ? _value._targetRewardList
          : targetRewardList // ignore: cast_nullable_to_non_nullable
              as List<MilestoneReward>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MilestoneInfo implements _MilestoneInfo {
  const _$_MilestoneInfo(
      {this.isShowProgress,
      this.progressStyle,
      this.targetDay,
      final List<MilestoneReward>? rewardList,
      final List<MilestoneReward>? targetRewardList})
      : _rewardList = rewardList,
        _targetRewardList = targetRewardList;

  factory _$_MilestoneInfo.fromJson(Map<String, dynamic> json) =>
      _$$_MilestoneInfoFromJson(json);

  /// 是否显示进度(1是0否) 包括进度条和日历样式
  @override
  final int? isShowProgress;

  ///进度样式(日历calendar，进度条progress)
  @override
  final String? progressStyle;

  /// 目标天数 进度分母
  @override
  final int? targetDay;
  final List<MilestoneReward>? _rewardList;
  @override
  List<MilestoneReward>? get rewardList {
    final value = _rewardList;
    if (value == null) return null;
    if (_rewardList is EqualUnmodifiableListView) return _rewardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<MilestoneReward>? _targetRewardList;
  @override
  List<MilestoneReward>? get targetRewardList {
    final value = _targetRewardList;
    if (value == null) return null;
    if (_targetRewardList is EqualUnmodifiableListView)
      return _targetRewardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MilestoneInfo(isShowProgress: $isShowProgress, progressStyle: $progressStyle, targetDay: $targetDay, rewardList: $rewardList, targetRewardList: $targetRewardList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MilestoneInfo &&
            (identical(other.isShowProgress, isShowProgress) ||
                other.isShowProgress == isShowProgress) &&
            (identical(other.progressStyle, progressStyle) ||
                other.progressStyle == progressStyle) &&
            (identical(other.targetDay, targetDay) ||
                other.targetDay == targetDay) &&
            const DeepCollectionEquality()
                .equals(other._rewardList, _rewardList) &&
            const DeepCollectionEquality()
                .equals(other._targetRewardList, _targetRewardList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isShowProgress,
      progressStyle,
      targetDay,
      const DeepCollectionEquality().hash(_rewardList),
      const DeepCollectionEquality().hash(_targetRewardList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MilestoneInfoCopyWith<_$_MilestoneInfo> get copyWith =>
      __$$_MilestoneInfoCopyWithImpl<_$_MilestoneInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MilestoneInfoToJson(
      this,
    );
  }
}

abstract class _MilestoneInfo implements MilestoneInfo {
  const factory _MilestoneInfo(
      {final int? isShowProgress,
      final String? progressStyle,
      final int? targetDay,
      final List<MilestoneReward>? rewardList,
      final List<MilestoneReward>? targetRewardList}) = _$_MilestoneInfo;

  factory _MilestoneInfo.fromJson(Map<String, dynamic> json) =
      _$_MilestoneInfo.fromJson;

  @override

  /// 是否显示进度(1是0否) 包括进度条和日历样式
  int? get isShowProgress;
  @override

  ///进度样式(日历calendar，进度条progress)
  String? get progressStyle;
  @override

  /// 目标天数 进度分母
  int? get targetDay;
  @override
  List<MilestoneReward>? get rewardList;
  @override
  List<MilestoneReward>? get targetRewardList;
  @override
  @JsonKey(ignore: true)
  _$$_MilestoneInfoCopyWith<_$_MilestoneInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

MilestoneReward _$MilestoneRewardFromJson(Map<String, dynamic> json) {
  return _MilestoneReward.fromJson(json);
}

/// @nodoc
mixin _$MilestoneReward {
  String? get name => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MilestoneRewardCopyWith<MilestoneReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MilestoneRewardCopyWith<$Res> {
  factory $MilestoneRewardCopyWith(
          MilestoneReward value, $Res Function(MilestoneReward) then) =
      _$MilestoneRewardCopyWithImpl<$Res, MilestoneReward>;
  @useResult
  $Res call({String? name, String? type, String? desc, String? img});
}

/// @nodoc
class _$MilestoneRewardCopyWithImpl<$Res, $Val extends MilestoneReward>
    implements $MilestoneRewardCopyWith<$Res> {
  _$MilestoneRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? type = freezed,
    Object? desc = freezed,
    Object? img = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MilestoneRewardCopyWith<$Res>
    implements $MilestoneRewardCopyWith<$Res> {
  factory _$$_MilestoneRewardCopyWith(
          _$_MilestoneReward value, $Res Function(_$_MilestoneReward) then) =
      __$$_MilestoneRewardCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, String? type, String? desc, String? img});
}

/// @nodoc
class __$$_MilestoneRewardCopyWithImpl<$Res>
    extends _$MilestoneRewardCopyWithImpl<$Res, _$_MilestoneReward>
    implements _$$_MilestoneRewardCopyWith<$Res> {
  __$$_MilestoneRewardCopyWithImpl(
      _$_MilestoneReward _value, $Res Function(_$_MilestoneReward) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? type = freezed,
    Object? desc = freezed,
    Object? img = freezed,
  }) {
    return _then(_$_MilestoneReward(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MilestoneReward implements _MilestoneReward {
  const _$_MilestoneReward({this.name, this.type, this.desc, this.img});

  factory _$_MilestoneReward.fromJson(Map<String, dynamic> json) =>
      _$$_MilestoneRewardFromJson(json);

  @override
  final String? name;
  @override
  final String? type;
  @override
  final String? desc;
  @override
  final String? img;

  @override
  String toString() {
    return 'MilestoneReward(name: $name, type: $type, desc: $desc, img: $img)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MilestoneReward &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.img, img) || other.img == img));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, type, desc, img);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MilestoneRewardCopyWith<_$_MilestoneReward> get copyWith =>
      __$$_MilestoneRewardCopyWithImpl<_$_MilestoneReward>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MilestoneRewardToJson(
      this,
    );
  }
}

abstract class _MilestoneReward implements MilestoneReward {
  const factory _MilestoneReward(
      {final String? name,
      final String? type,
      final String? desc,
      final String? img}) = _$_MilestoneReward;

  factory _MilestoneReward.fromJson(Map<String, dynamic> json) =
      _$_MilestoneReward.fromJson;

  @override
  String? get name;
  @override
  String? get type;
  @override
  String? get desc;
  @override
  String? get img;
  @override
  @JsonKey(ignore: true)
  _$$_MilestoneRewardCopyWith<_$_MilestoneReward> get copyWith =>
      throw _privateConstructorUsedError;
}

Btn _$BtnFromJson(Map<String, dynamic> json) {
  return _Btn.fromJson(json);
}

/// @nodoc
mixin _$Btn {
  String? get btnName => throw _privateConstructorUsedError;
  String? get btnRouter => throw _privateConstructorUsedError;
  BtnParams? get btnParams => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BtnCopyWith<Btn> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BtnCopyWith<$Res> {
  factory $BtnCopyWith(Btn value, $Res Function(Btn) then) =
      _$BtnCopyWithImpl<$Res, Btn>;
  @useResult
  $Res call({String? btnName, String? btnRouter, BtnParams? btnParams});

  $BtnParamsCopyWith<$Res>? get btnParams;
}

/// @nodoc
class _$BtnCopyWithImpl<$Res, $Val extends Btn> implements $BtnCopyWith<$Res> {
  _$BtnCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? btnName = freezed,
    Object? btnRouter = freezed,
    Object? btnParams = freezed,
  }) {
    return _then(_value.copyWith(
      btnName: freezed == btnName
          ? _value.btnName
          : btnName // ignore: cast_nullable_to_non_nullable
              as String?,
      btnRouter: freezed == btnRouter
          ? _value.btnRouter
          : btnRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      btnParams: freezed == btnParams
          ? _value.btnParams
          : btnParams // ignore: cast_nullable_to_non_nullable
              as BtnParams?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $BtnParamsCopyWith<$Res>? get btnParams {
    if (_value.btnParams == null) {
      return null;
    }

    return $BtnParamsCopyWith<$Res>(_value.btnParams!, (value) {
      return _then(_value.copyWith(btnParams: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_BtnCopyWith<$Res> implements $BtnCopyWith<$Res> {
  factory _$$_BtnCopyWith(_$_Btn value, $Res Function(_$_Btn) then) =
      __$$_BtnCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? btnName, String? btnRouter, BtnParams? btnParams});

  @override
  $BtnParamsCopyWith<$Res>? get btnParams;
}

/// @nodoc
class __$$_BtnCopyWithImpl<$Res> extends _$BtnCopyWithImpl<$Res, _$_Btn>
    implements _$$_BtnCopyWith<$Res> {
  __$$_BtnCopyWithImpl(_$_Btn _value, $Res Function(_$_Btn) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? btnName = freezed,
    Object? btnRouter = freezed,
    Object? btnParams = freezed,
  }) {
    return _then(_$_Btn(
      btnName: freezed == btnName
          ? _value.btnName
          : btnName // ignore: cast_nullable_to_non_nullable
              as String?,
      btnRouter: freezed == btnRouter
          ? _value.btnRouter
          : btnRouter // ignore: cast_nullable_to_non_nullable
              as String?,
      btnParams: freezed == btnParams
          ? _value.btnParams
          : btnParams // ignore: cast_nullable_to_non_nullable
              as BtnParams?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Btn implements _Btn {
  const _$_Btn({this.btnName, this.btnRouter, this.btnParams});

  factory _$_Btn.fromJson(Map<String, dynamic> json) => _$$_BtnFromJson(json);

  @override
  final String? btnName;
  @override
  final String? btnRouter;
  @override
  final BtnParams? btnParams;

  @override
  String toString() {
    return 'Btn(btnName: $btnName, btnRouter: $btnRouter, btnParams: $btnParams)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Btn &&
            (identical(other.btnName, btnName) || other.btnName == btnName) &&
            (identical(other.btnRouter, btnRouter) ||
                other.btnRouter == btnRouter) &&
            (identical(other.btnParams, btnParams) ||
                other.btnParams == btnParams));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, btnName, btnRouter, btnParams);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BtnCopyWith<_$_Btn> get copyWith =>
      __$$_BtnCopyWithImpl<_$_Btn>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BtnToJson(
      this,
    );
  }
}

abstract class _Btn implements Btn {
  const factory _Btn(
      {final String? btnName,
      final String? btnRouter,
      final BtnParams? btnParams}) = _$_Btn;

  factory _Btn.fromJson(Map<String, dynamic> json) = _$_Btn.fromJson;

  @override
  String? get btnName;
  @override
  String? get btnRouter;
  @override
  BtnParams? get btnParams;
  @override
  @JsonKey(ignore: true)
  _$$_BtnCopyWith<_$_Btn> get copyWith => throw _privateConstructorUsedError;
}

BtnParams _$BtnParamsFromJson(Map<String, dynamic> json) {
  return _BtnParams.fromJson(json);
}

/// @nodoc
mixin _$BtnParams {
  String? get classKey => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BtnParamsCopyWith<BtnParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BtnParamsCopyWith<$Res> {
  factory $BtnParamsCopyWith(BtnParams value, $Res Function(BtnParams) then) =
      _$BtnParamsCopyWithImpl<$Res, BtnParams>;
  @useResult
  $Res call({String? classKey, String? courseKey, int? lessonId});
}

/// @nodoc
class _$BtnParamsCopyWithImpl<$Res, $Val extends BtnParams>
    implements $BtnParamsCopyWith<$Res> {
  _$BtnParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classKey = freezed,
    Object? courseKey = freezed,
    Object? lessonId = freezed,
  }) {
    return _then(_value.copyWith(
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BtnParamsCopyWith<$Res> implements $BtnParamsCopyWith<$Res> {
  factory _$$_BtnParamsCopyWith(
          _$_BtnParams value, $Res Function(_$_BtnParams) then) =
      __$$_BtnParamsCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? classKey, String? courseKey, int? lessonId});
}

/// @nodoc
class __$$_BtnParamsCopyWithImpl<$Res>
    extends _$BtnParamsCopyWithImpl<$Res, _$_BtnParams>
    implements _$$_BtnParamsCopyWith<$Res> {
  __$$_BtnParamsCopyWithImpl(
      _$_BtnParams _value, $Res Function(_$_BtnParams) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classKey = freezed,
    Object? courseKey = freezed,
    Object? lessonId = freezed,
  }) {
    return _then(_$_BtnParams(
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BtnParams implements _BtnParams {
  const _$_BtnParams({this.classKey, this.courseKey, this.lessonId});

  factory _$_BtnParams.fromJson(Map<String, dynamic> json) =>
      _$$_BtnParamsFromJson(json);

  @override
  final String? classKey;
  @override
  final String? courseKey;
  @override
  final int? lessonId;

  @override
  String toString() {
    return 'BtnParams(classKey: $classKey, courseKey: $courseKey, lessonId: $lessonId)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BtnParams &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, classKey, courseKey, lessonId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BtnParamsCopyWith<_$_BtnParams> get copyWith =>
      __$$_BtnParamsCopyWithImpl<_$_BtnParams>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BtnParamsToJson(
      this,
    );
  }
}

abstract class _BtnParams implements BtnParams {
  const factory _BtnParams(
      {final String? classKey,
      final String? courseKey,
      final int? lessonId}) = _$_BtnParams;

  factory _BtnParams.fromJson(Map<String, dynamic> json) =
      _$_BtnParams.fromJson;

  @override
  String? get classKey;
  @override
  String? get courseKey;
  @override
  int? get lessonId;
  @override
  @JsonKey(ignore: true)
  _$$_BtnParamsCopyWith<_$_BtnParams> get copyWith =>
      throw _privateConstructorUsedError;
}

Calendar _$CalendarFromJson(Map<String, dynamic> json) {
  return _Calendar.fromJson(json);
}

/// @nodoc
mixin _$Calendar {
  int? get date => throw _privateConstructorUsedError; // 日历节点的时间（毫秒时间戳）
  int? get state =>
      throw _privateConstructorUsedError; // 状态（0-占位，1-笑脸、2-冻结、3-嗝屁）
  String? get router => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CalendarCopyWith<Calendar> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CalendarCopyWith<$Res> {
  factory $CalendarCopyWith(Calendar value, $Res Function(Calendar) then) =
      _$CalendarCopyWithImpl<$Res, Calendar>;
  @useResult
  $Res call({int? date, int? state, String? router});
}

/// @nodoc
class _$CalendarCopyWithImpl<$Res, $Val extends Calendar>
    implements $CalendarCopyWith<$Res> {
  _$CalendarCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? state = freezed,
    Object? router = freezed,
  }) {
    return _then(_value.copyWith(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as int?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_CalendarCopyWith<$Res> implements $CalendarCopyWith<$Res> {
  factory _$$_CalendarCopyWith(
          _$_Calendar value, $Res Function(_$_Calendar) then) =
      __$$_CalendarCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? date, int? state, String? router});
}

/// @nodoc
class __$$_CalendarCopyWithImpl<$Res>
    extends _$CalendarCopyWithImpl<$Res, _$_Calendar>
    implements _$$_CalendarCopyWith<$Res> {
  __$$_CalendarCopyWithImpl(
      _$_Calendar _value, $Res Function(_$_Calendar) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? state = freezed,
    Object? router = freezed,
  }) {
    return _then(_$_Calendar(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as int?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as int?,
      router: freezed == router
          ? _value.router
          : router // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Calendar implements _Calendar {
  const _$_Calendar({this.date, this.state, this.router});

  factory _$_Calendar.fromJson(Map<String, dynamic> json) =>
      _$$_CalendarFromJson(json);

  @override
  final int? date;
// 日历节点的时间（毫秒时间戳）
  @override
  final int? state;
// 状态（0-占位，1-笑脸、2-冻结、3-嗝屁）
  @override
  final String? router;

  @override
  String toString() {
    return 'Calendar(date: $date, state: $state, router: $router)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Calendar &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.router, router) || other.router == router));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, date, state, router);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_CalendarCopyWith<_$_Calendar> get copyWith =>
      __$$_CalendarCopyWithImpl<_$_Calendar>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_CalendarToJson(
      this,
    );
  }
}

abstract class _Calendar implements Calendar {
  const factory _Calendar(
      {final int? date, final int? state, final String? router}) = _$_Calendar;

  factory _Calendar.fromJson(Map<String, dynamic> json) = _$_Calendar.fromJson;

  @override
  int? get date;
  @override // 日历节点的时间（毫秒时间戳）
  int? get state;
  @override // 状态（0-占位，1-笑脸、2-冻结、3-嗝屁）
  String? get router;
  @override
  @JsonKey(ignore: true)
  _$$_CalendarCopyWith<_$_Calendar> get copyWith =>
      throw _privateConstructorUsedError;
}

BurialResources _$BurialResourcesFromJson(Map<String, dynamic> json) {
  return _BurialResources.fromJson(json);
}

/// @nodoc
mixin _$BurialResources {
  String? get courseName => throw _privateConstructorUsedError;
  String? get classId => throw _privateConstructorUsedError;
  String? get courseStage => throw _privateConstructorUsedError;
  String? get courseType => throw _privateConstructorUsedError;
  String? get classKey => throw _privateConstructorUsedError;
  String? get courseKey => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BurialResourcesCopyWith<BurialResources> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BurialResourcesCopyWith<$Res> {
  factory $BurialResourcesCopyWith(
          BurialResources value, $Res Function(BurialResources) then) =
      _$BurialResourcesCopyWithImpl<$Res, BurialResources>;
  @useResult
  $Res call(
      {String? courseName,
      String? classId,
      String? courseStage,
      String? courseType,
      String? classKey,
      String? courseKey});
}

/// @nodoc
class _$BurialResourcesCopyWithImpl<$Res, $Val extends BurialResources>
    implements $BurialResourcesCopyWith<$Res> {
  _$BurialResourcesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? classId = freezed,
    Object? courseStage = freezed,
    Object? courseType = freezed,
    Object? classKey = freezed,
    Object? courseKey = freezed,
  }) {
    return _then(_value.copyWith(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStage: freezed == courseStage
          ? _value.courseStage
          : courseStage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_BurialResourcesCopyWith<$Res>
    implements $BurialResourcesCopyWith<$Res> {
  factory _$$_BurialResourcesCopyWith(
          _$_BurialResources value, $Res Function(_$_BurialResources) then) =
      __$$_BurialResourcesCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? courseName,
      String? classId,
      String? courseStage,
      String? courseType,
      String? classKey,
      String? courseKey});
}

/// @nodoc
class __$$_BurialResourcesCopyWithImpl<$Res>
    extends _$BurialResourcesCopyWithImpl<$Res, _$_BurialResources>
    implements _$$_BurialResourcesCopyWith<$Res> {
  __$$_BurialResourcesCopyWithImpl(
      _$_BurialResources _value, $Res Function(_$_BurialResources) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? courseName = freezed,
    Object? classId = freezed,
    Object? courseStage = freezed,
    Object? courseType = freezed,
    Object? classKey = freezed,
    Object? courseKey = freezed,
  }) {
    return _then(_$_BurialResources(
      courseName: freezed == courseName
          ? _value.courseName
          : courseName // ignore: cast_nullable_to_non_nullable
              as String?,
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as String?,
      courseStage: freezed == courseStage
          ? _value.courseStage
          : courseStage // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String?,
      classKey: freezed == classKey
          ? _value.classKey
          : classKey // ignore: cast_nullable_to_non_nullable
              as String?,
      courseKey: freezed == courseKey
          ? _value.courseKey
          : courseKey // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BurialResources implements _BurialResources {
  const _$_BurialResources(
      {this.courseName,
      this.classId,
      this.courseStage,
      this.courseType,
      this.classKey,
      this.courseKey});

  factory _$_BurialResources.fromJson(Map<String, dynamic> json) =>
      _$$_BurialResourcesFromJson(json);

  @override
  final String? courseName;
  @override
  final String? classId;
  @override
  final String? courseStage;
  @override
  final String? courseType;
  @override
  final String? classKey;
  @override
  final String? courseKey;

  @override
  String toString() {
    return 'BurialResources(courseName: $courseName, classId: $classId, courseStage: $courseStage, courseType: $courseType, classKey: $classKey, courseKey: $courseKey)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BurialResources &&
            (identical(other.courseName, courseName) ||
                other.courseName == courseName) &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.courseStage, courseStage) ||
                other.courseStage == courseStage) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.classKey, classKey) ||
                other.classKey == classKey) &&
            (identical(other.courseKey, courseKey) ||
                other.courseKey == courseKey));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, courseName, classId, courseStage,
      courseType, classKey, courseKey);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BurialResourcesCopyWith<_$_BurialResources> get copyWith =>
      __$$_BurialResourcesCopyWithImpl<_$_BurialResources>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BurialResourcesToJson(
      this,
    );
  }
}

abstract class _BurialResources implements BurialResources {
  const factory _BurialResources(
      {final String? courseName,
      final String? classId,
      final String? courseStage,
      final String? courseType,
      final String? classKey,
      final String? courseKey}) = _$_BurialResources;

  factory _BurialResources.fromJson(Map<String, dynamic> json) =
      _$_BurialResources.fromJson;

  @override
  String? get courseName;
  @override
  String? get classId;
  @override
  String? get courseStage;
  @override
  String? get courseType;
  @override
  String? get classKey;
  @override
  String? get courseKey;
  @override
  @JsonKey(ignore: true)
  _$$_BurialResourcesCopyWith<_$_BurialResources> get copyWith =>
      throw _privateConstructorUsedError;
}

LessonTaskList _$LessonTaskListFromJson(Map<String, dynamic> json) {
  return _LessonTaskList.fromJson(json);
}

/// @nodoc
mixin _$LessonTaskList {
  String? get id => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  int? get status => throw _privateConstructorUsedError;
  List<RewardList>? get rewardList => throw _privateConstructorUsedError;
  int? get current => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LessonTaskListCopyWith<LessonTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LessonTaskListCopyWith<$Res> {
  factory $LessonTaskListCopyWith(
          LessonTaskList value, $Res Function(LessonTaskList) then) =
      _$LessonTaskListCopyWithImpl<$Res, LessonTaskList>;
  @useResult
  $Res call(
      {String? id,
      int? type,
      String? name,
      String? desc,
      int? status,
      List<RewardList>? rewardList,
      int? current,
      int? total});
}

/// @nodoc
class _$LessonTaskListCopyWithImpl<$Res, $Val extends LessonTaskList>
    implements $LessonTaskListCopyWith<$Res> {
  _$LessonTaskListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? status = freezed,
    Object? rewardList = freezed,
    Object? current = freezed,
    Object? total = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      rewardList: freezed == rewardList
          ? _value.rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<RewardList>?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LessonTaskListCopyWith<$Res>
    implements $LessonTaskListCopyWith<$Res> {
  factory _$$_LessonTaskListCopyWith(
          _$_LessonTaskList value, $Res Function(_$_LessonTaskList) then) =
      __$$_LessonTaskListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      int? type,
      String? name,
      String? desc,
      int? status,
      List<RewardList>? rewardList,
      int? current,
      int? total});
}

/// @nodoc
class __$$_LessonTaskListCopyWithImpl<$Res>
    extends _$LessonTaskListCopyWithImpl<$Res, _$_LessonTaskList>
    implements _$$_LessonTaskListCopyWith<$Res> {
  __$$_LessonTaskListCopyWithImpl(
      _$_LessonTaskList _value, $Res Function(_$_LessonTaskList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? status = freezed,
    Object? rewardList = freezed,
    Object? current = freezed,
    Object? total = freezed,
  }) {
    return _then(_$_LessonTaskList(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      rewardList: freezed == rewardList
          ? _value._rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<RewardList>?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LessonTaskList implements _LessonTaskList {
  const _$_LessonTaskList(
      {this.id,
      this.type,
      this.name,
      this.desc,
      this.status,
      final List<RewardList>? rewardList,
      this.current,
      this.total})
      : _rewardList = rewardList;

  factory _$_LessonTaskList.fromJson(Map<String, dynamic> json) =>
      _$$_LessonTaskListFromJson(json);

  @override
  final String? id;
  @override
  final int? type;
  @override
  final String? name;
  @override
  final String? desc;
  @override
  final int? status;
  final List<RewardList>? _rewardList;
  @override
  List<RewardList>? get rewardList {
    final value = _rewardList;
    if (value == null) return null;
    if (_rewardList is EqualUnmodifiableListView) return _rewardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? current;
  @override
  final int? total;

  @override
  String toString() {
    return 'LessonTaskList(id: $id, type: $type, name: $name, desc: $desc, status: $status, rewardList: $rewardList, current: $current, total: $total)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LessonTaskList &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.status, status) || other.status == status) &&
            const DeepCollectionEquality()
                .equals(other._rewardList, _rewardList) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.total, total) || other.total == total));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, name, desc, status,
      const DeepCollectionEquality().hash(_rewardList), current, total);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LessonTaskListCopyWith<_$_LessonTaskList> get copyWith =>
      __$$_LessonTaskListCopyWithImpl<_$_LessonTaskList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LessonTaskListToJson(
      this,
    );
  }
}

abstract class _LessonTaskList implements LessonTaskList {
  const factory _LessonTaskList(
      {final String? id,
      final int? type,
      final String? name,
      final String? desc,
      final int? status,
      final List<RewardList>? rewardList,
      final int? current,
      final int? total}) = _$_LessonTaskList;

  factory _LessonTaskList.fromJson(Map<String, dynamic> json) =
      _$_LessonTaskList.fromJson;

  @override
  String? get id;
  @override
  int? get type;
  @override
  String? get name;
  @override
  String? get desc;
  @override
  int? get status;
  @override
  List<RewardList>? get rewardList;
  @override
  int? get current;
  @override
  int? get total;
  @override
  @JsonKey(ignore: true)
  _$$_LessonTaskListCopyWith<_$_LessonTaskList> get copyWith =>
      throw _privateConstructorUsedError;
}

RewardList _$RewardListFromJson(Map<String, dynamic> json) {
  return _RewardList.fromJson(json);
}

/// @nodoc
mixin _$RewardList {
  String? get id => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  int? get value => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  RewardData? get rewardData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RewardListCopyWith<RewardList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardListCopyWith<$Res> {
  factory $RewardListCopyWith(
          RewardList value, $Res Function(RewardList) then) =
      _$RewardListCopyWithImpl<$Res, RewardList>;
  @useResult
  $Res call(
      {String? id,
      int? type,
      int? value,
      String? name,
      RewardData? rewardData});

  $RewardDataCopyWith<$Res>? get rewardData;
}

/// @nodoc
class _$RewardListCopyWithImpl<$Res, $Val extends RewardList>
    implements $RewardListCopyWith<$Res> {
  _$RewardListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? value = freezed,
    Object? name = freezed,
    Object? rewardData = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardData: freezed == rewardData
          ? _value.rewardData
          : rewardData // ignore: cast_nullable_to_non_nullable
              as RewardData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $RewardDataCopyWith<$Res>? get rewardData {
    if (_value.rewardData == null) {
      return null;
    }

    return $RewardDataCopyWith<$Res>(_value.rewardData!, (value) {
      return _then(_value.copyWith(rewardData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_RewardListCopyWith<$Res>
    implements $RewardListCopyWith<$Res> {
  factory _$$_RewardListCopyWith(
          _$_RewardList value, $Res Function(_$_RewardList) then) =
      __$$_RewardListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      int? type,
      int? value,
      String? name,
      RewardData? rewardData});

  @override
  $RewardDataCopyWith<$Res>? get rewardData;
}

/// @nodoc
class __$$_RewardListCopyWithImpl<$Res>
    extends _$RewardListCopyWithImpl<$Res, _$_RewardList>
    implements _$$_RewardListCopyWith<$Res> {
  __$$_RewardListCopyWithImpl(
      _$_RewardList _value, $Res Function(_$_RewardList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? value = freezed,
    Object? name = freezed,
    Object? rewardData = freezed,
  }) {
    return _then(_$_RewardList(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardData: freezed == rewardData
          ? _value.rewardData
          : rewardData // ignore: cast_nullable_to_non_nullable
              as RewardData?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RewardList implements _RewardList {
  const _$_RewardList(
      {this.id, this.type, this.value, this.name, this.rewardData});

  factory _$_RewardList.fromJson(Map<String, dynamic> json) =>
      _$$_RewardListFromJson(json);

  @override
  final String? id;
  @override
  final int? type;
  @override
  final int? value;
  @override
  final String? name;
  @override
  final RewardData? rewardData;

  @override
  String toString() {
    return 'RewardList(id: $id, type: $type, value: $value, name: $name, rewardData: $rewardData)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RewardList &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.rewardData, rewardData) ||
                other.rewardData == rewardData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, type, value, name, rewardData);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RewardListCopyWith<_$_RewardList> get copyWith =>
      __$$_RewardListCopyWithImpl<_$_RewardList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RewardListToJson(
      this,
    );
  }
}

abstract class _RewardList implements RewardList {
  const factory _RewardList(
      {final String? id,
      final int? type,
      final int? value,
      final String? name,
      final RewardData? rewardData}) = _$_RewardList;

  factory _RewardList.fromJson(Map<String, dynamic> json) =
      _$_RewardList.fromJson;

  @override
  String? get id;
  @override
  int? get type;
  @override
  int? get value;
  @override
  String? get name;
  @override
  RewardData? get rewardData;
  @override
  @JsonKey(ignore: true)
  _$$_RewardListCopyWith<_$_RewardList> get copyWith =>
      throw _privateConstructorUsedError;
}

RewardData _$RewardDataFromJson(Map<String, dynamic> json) {
  return _RewardData.fromJson(json);
}

/// @nodoc
mixin _$RewardData {
  String? get medalImg => throw _privateConstructorUsedError;
  String? get medalUpgradeImg => throw _privateConstructorUsedError;
  String? get medalAudio => throw _privateConstructorUsedError;
  String? get flutterRes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RewardDataCopyWith<RewardData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RewardDataCopyWith<$Res> {
  factory $RewardDataCopyWith(
          RewardData value, $Res Function(RewardData) then) =
      _$RewardDataCopyWithImpl<$Res, RewardData>;
  @useResult
  $Res call(
      {String? medalImg,
      String? medalUpgradeImg,
      String? medalAudio,
      String? flutterRes});
}

/// @nodoc
class _$RewardDataCopyWithImpl<$Res, $Val extends RewardData>
    implements $RewardDataCopyWith<$Res> {
  _$RewardDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medalImg = freezed,
    Object? medalUpgradeImg = freezed,
    Object? medalAudio = freezed,
    Object? flutterRes = freezed,
  }) {
    return _then(_value.copyWith(
      medalImg: freezed == medalImg
          ? _value.medalImg
          : medalImg // ignore: cast_nullable_to_non_nullable
              as String?,
      medalUpgradeImg: freezed == medalUpgradeImg
          ? _value.medalUpgradeImg
          : medalUpgradeImg // ignore: cast_nullable_to_non_nullable
              as String?,
      medalAudio: freezed == medalAudio
          ? _value.medalAudio
          : medalAudio // ignore: cast_nullable_to_non_nullable
              as String?,
      flutterRes: freezed == flutterRes
          ? _value.flutterRes
          : flutterRes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_RewardDataCopyWith<$Res>
    implements $RewardDataCopyWith<$Res> {
  factory _$$_RewardDataCopyWith(
          _$_RewardData value, $Res Function(_$_RewardData) then) =
      __$$_RewardDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? medalImg,
      String? medalUpgradeImg,
      String? medalAudio,
      String? flutterRes});
}

/// @nodoc
class __$$_RewardDataCopyWithImpl<$Res>
    extends _$RewardDataCopyWithImpl<$Res, _$_RewardData>
    implements _$$_RewardDataCopyWith<$Res> {
  __$$_RewardDataCopyWithImpl(
      _$_RewardData _value, $Res Function(_$_RewardData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medalImg = freezed,
    Object? medalUpgradeImg = freezed,
    Object? medalAudio = freezed,
    Object? flutterRes = freezed,
  }) {
    return _then(_$_RewardData(
      medalImg: freezed == medalImg
          ? _value.medalImg
          : medalImg // ignore: cast_nullable_to_non_nullable
              as String?,
      medalUpgradeImg: freezed == medalUpgradeImg
          ? _value.medalUpgradeImg
          : medalUpgradeImg // ignore: cast_nullable_to_non_nullable
              as String?,
      medalAudio: freezed == medalAudio
          ? _value.medalAudio
          : medalAudio // ignore: cast_nullable_to_non_nullable
              as String?,
      flutterRes: freezed == flutterRes
          ? _value.flutterRes
          : flutterRes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_RewardData implements _RewardData {
  const _$_RewardData(
      {this.medalImg, this.medalUpgradeImg, this.medalAudio, this.flutterRes});

  factory _$_RewardData.fromJson(Map<String, dynamic> json) =>
      _$$_RewardDataFromJson(json);

  @override
  final String? medalImg;
  @override
  final String? medalUpgradeImg;
  @override
  final String? medalAudio;
  @override
  final String? flutterRes;

  @override
  String toString() {
    return 'RewardData(medalImg: $medalImg, medalUpgradeImg: $medalUpgradeImg, medalAudio: $medalAudio, flutterRes: $flutterRes)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_RewardData &&
            (identical(other.medalImg, medalImg) ||
                other.medalImg == medalImg) &&
            (identical(other.medalUpgradeImg, medalUpgradeImg) ||
                other.medalUpgradeImg == medalUpgradeImg) &&
            (identical(other.medalAudio, medalAudio) ||
                other.medalAudio == medalAudio) &&
            (identical(other.flutterRes, flutterRes) ||
                other.flutterRes == flutterRes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, medalImg, medalUpgradeImg, medalAudio, flutterRes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_RewardDataCopyWith<_$_RewardData> get copyWith =>
      __$$_RewardDataCopyWithImpl<_$_RewardData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_RewardDataToJson(
      this,
    );
  }
}

abstract class _RewardData implements RewardData {
  const factory _RewardData(
      {final String? medalImg,
      final String? medalUpgradeImg,
      final String? medalAudio,
      final String? flutterRes}) = _$_RewardData;

  factory _RewardData.fromJson(Map<String, dynamic> json) =
      _$_RewardData.fromJson;

  @override
  String? get medalImg;
  @override
  String? get medalUpgradeImg;
  @override
  String? get medalAudio;
  @override
  String? get flutterRes;
  @override
  @JsonKey(ignore: true)
  _$$_RewardDataCopyWith<_$_RewardData> get copyWith =>
      throw _privateConstructorUsedError;
}

Res _$ResFromJson(Map<String, dynamic> json) {
  return _Res.fromJson(json);
}

/// @nodoc
mixin _$Res {
  String? get img => throw _privateConstructorUsedError;
  String? get audio => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ResCopyWith<Res> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResCopyWith<$Res> {
  factory $ResCopyWith(Res value, $Res Function(Res) then) =
      _$ResCopyWithImpl<$Res, Res>;
  @useResult
  $Res call({String? img, String? audio});
}

/// @nodoc
class _$ResCopyWithImpl<$Res, $Val extends Res> implements $ResCopyWith<$Res> {
  _$ResCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? img = freezed,
    Object? audio = freezed,
  }) {
    return _then(_value.copyWith(
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ResCopyWith<$Res> implements $ResCopyWith<$Res> {
  factory _$$_ResCopyWith(_$_Res value, $Res Function(_$_Res) then) =
      __$$_ResCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? img, String? audio});
}

/// @nodoc
class __$$_ResCopyWithImpl<$Res> extends _$ResCopyWithImpl<$Res, _$_Res>
    implements _$$_ResCopyWith<$Res> {
  __$$_ResCopyWithImpl(_$_Res _value, $Res Function(_$_Res) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? img = freezed,
    Object? audio = freezed,
  }) {
    return _then(_$_Res(
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      audio: freezed == audio
          ? _value.audio
          : audio // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Res implements _Res {
  const _$_Res({this.img, this.audio});

  factory _$_Res.fromJson(Map<String, dynamic> json) => _$$_ResFromJson(json);

  @override
  final String? img;
  @override
  final String? audio;

  @override
  String toString() {
    return 'Res(img: $img, audio: $audio)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Res &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.audio, audio) || other.audio == audio));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, img, audio);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ResCopyWith<_$_Res> get copyWith =>
      __$$_ResCopyWithImpl<_$_Res>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ResToJson(
      this,
    );
  }
}

abstract class _Res implements Res {
  const factory _Res({final String? img, final String? audio}) = _$_Res;

  factory _Res.fromJson(Map<String, dynamic> json) = _$_Res.fromJson;

  @override
  String? get img;
  @override
  String? get audio;
  @override
  @JsonKey(ignore: true)
  _$$_ResCopyWith<_$_Res> get copyWith => throw _privateConstructorUsedError;
}
