import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/segments_data.dart';

import '../../common/host_env/host_env.dart';
import 'model/art_material_preview_model.dart';
import 'service/art_material_preview_api.dart';
import 'state.dart';

class ArtMaterialPreviewController extends Cubit<ArtMaterialPreviewState> {
  static const String tag = "ArtMaterialPreviewController";
  final String classKey;
  final int initialSegmentId;
  final String initialLessonKey;
  final ArtMaterialPreviewApi api;

  late PageController pageController;
  int currentSegmentId = 0;
  int currentPageIndex = 0;

  /// 主题月数据
  SegmentsData? initialSegmentData;
  final List<SegmentInfoModel> segmentList = [];

  /// 画材预览数据
  final Map<int, ArtMaterialPreviewListModel> materialPreviewData = {};

  late ValueNotifier<int> pageWillChangeNotifier = ValueNotifier(-1);

  ArtMaterialPreviewController(
    this.api,
    this.classKey,
    this.initialSegmentId,
    this.initialLessonKey,
  ) : super(ArtMaterialPreviewState(PageStatus.loading));

  ArtMaterialPreviewController.withDefault({
    required this.classKey,
    required this.initialSegmentId,
    required this.initialLessonKey,
  })  : api = proArtMaterialPreviewApi,
        super(ArtMaterialPreviewState(PageStatus.loading)) {
    initialRefreshData();
  }

  updateState(ArtMaterialPreviewState state) {
    emit(state);
  }

  changeToNextPage() async {
    if (currentPageIndex + 1 < segmentList.length) {
      await changeToPage(currentPageIndex + 1, animate: true);
    }
  }

  changeToLastPage() async {
    if (currentPageIndex - 1 >= 0) {
      await changeToPage(currentPageIndex - 1, animate: true);
    }
  }

  /// 跳转指定主题月
  changeToPage(
    int pageIndex, {
    bool animate = false,
    bool needSensor = true,
    bool needJump = true,
  }) async {
    if (needSensor) {
      sensorShow();
    }

    currentPageIndex = pageIndex;
    currentSegmentId = getSegmentIdByIndex(pageIndex);
    if (needJump) {
      goToPage(pageIndex, animate: animate);
    }
    await childRefreshData(currentSegmentId);
    await preRequest();
  }

  sensorShow() {
    RunEnv.sensorsTrack('\$AppClick', {
      '\$screen_name': '画材预览详情页',
      '\$element_name': '点击切换主题',
    });
  }

  goToPage(int pageIndex, {bool animate = false}) async {
    if (animate) {
      pageWillChangeNotifier.value = pageIndex;
      await pageController.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 500),
        curve: Curves.ease,
      );
    } else {
      pageController.jumpToPage(pageIndex);
    }
  }

  int getIndexBySegmentId(int segmentId) {
    int index = 0;
    for (var i = 0; i < segmentList.length; i++) {
      if (segmentList[i].segmentId == segmentId) {
        index = i;
      }
    }
    return index;
  }

  int getSegmentIdByIndex(int index) {
    int segmentId = segmentList[0].segmentId ?? initialSegmentId;
    if (index < segmentList.length) {
      segmentId = segmentList[index].segmentId ?? segmentId;
    }
    return segmentId;
  }

  initialRefreshData() async {
    currentSegmentId = initialSegmentId;
    await refreshAllData(
        segmentId: initialSegmentId, lessonKey: initialLessonKey);
  }

  errorRefreshData() async {
    await refreshAllData(segmentId: currentSegmentId, lessonKey: null);
  }

  refreshAllData({required int segmentId, String? lessonKey}) async {
    emit(ArtMaterialPreviewState(PageStatus.loading));

    var segmentData = await getSegments();
    if (segmentData != null) {
      currentPageIndex = getIndexBySegmentId(segmentId);
      pageController = PageController(initialPage: currentPageIndex);
      initialSegmentData = segmentData;

      var previewData = await getMaterialPreviewData(currentSegmentId);
      if (previewData != null) {
        updateSuccess(segmentId, lessonKey: lessonKey);
      }
      preRequest();
    }
  }

  childRefreshData(int segmentId) async {
    if (materialPreviewData.keys.contains(segmentId)) {
      updateSuccess(segmentId, lessonKey: null);
      return;
    }

    updateState(state.copyWith(
      PageStatus.success,
      childPageStatus: PageStatus.loading,
    ));

    var previewData = await getMaterialPreviewData(currentSegmentId);
    if (previewData != null) {
      updateSuccess(segmentId, lessonKey: null);
    }
  }

  updateSuccess(int segmentId, {String? lessonKey}) {
    updateState(state.copyWith(
      PageStatus.success,
      segmentList: segmentList,
      materialPreviewData: materialPreviewData,
      segmentId: segmentId,
      lessonKey: lessonKey,
      childPageStatus: PageStatus.success,
    ));
  }

  preRequest() async {
    if (currentPageIndex - 1 >= 0) {
      var lastSegmentId = getSegmentIdByIndex(currentPageIndex - 1);
      if (!materialPreviewData.keys.contains(lastSegmentId)) {
        await getMaterialPreviewData(lastSegmentId);
      }
    }

    if (currentPageIndex + 1 < segmentList.length) {
      var nextSegmentId = getSegmentIdByIndex(currentPageIndex + 1);
      if (!materialPreviewData.keys.contains(nextSegmentId)) {
        await getMaterialPreviewData(nextSegmentId);
      }
    }
  }

  Future<SegmentsData?> getSegments() async {
    var segmentData = await requestSegments();
    if (segmentData != null) {
      segmentList.removeRange(0, segmentList.length);
      segmentData.itemList?.forEach((item) {
        var segmentInfo = item.segmentInfo;
        if (segmentInfo != null) {
          segmentList.add(segmentInfo);
        }
      });
    }
    return segmentData;
  }

  Future<SegmentsData?> requestSegments() async {
    if (initialSegmentData == null) {
      try {
        var segmentData = await api.getSegments(classKey, 'LESSON_PREPARATION');
        return segmentData;
      } catch (e) {
        emit(state.copyWith(
          PageStatus.error,
          exception: (e is Exception) ? e : Exception(e.toString()),
        ));
        return null;
      }
    }
    return initialSegmentData;
  }

  Future<ArtMaterialPreviewListModel?> getMaterialPreviewData(
      int segmentId) async {
    var data = await requestMaterialPreviewData(segmentId);
    if (data != null) {
      materialPreviewData[segmentId] = data;
      return data;
    }
    return null;
  }

  Future<ArtMaterialPreviewListModel?> requestMaterialPreviewData(
      int segmentId) async {
    try {
      var data =
          await api.getStudyPrepareResources(classKey, segmentId.toString());
      return data;
    } catch (e) {
      emit(state.copyWith(
        PageStatus.error,
        exception: (e is Exception) ? e : Exception(e.toString()),
      ));
    }
    return null;
  }

  dispose() {
    pageController.dispose();
    pageWillChangeNotifier.dispose();
    initialSegmentData = null;
  }
}
