import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

import '../../controller.dart';
import '../../state.dart';
import 'material_content_list.dart';

class MaterialContentWidget extends StatefulWidget {
  final ArtMaterialPreviewState state;
  final Color subjectColor;
  final double contentWidth;

  const MaterialContentWidget({
    super.key,
    required this.state,
    required this.subjectColor, 
    required this.contentWidth,
  });

  @override
  State<MaterialContentWidget> createState() => _MaterialContentWidget();
}

class _MaterialContentWidget extends State<MaterialContentWidget>
    with AutomaticKeepAliveClientMixin {
  late ArtMaterialPreviewController _controller;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _controller = context.read<ArtMaterialPreviewController>();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var segmentList = widget.state.segmentList;
    var materialData = widget.state.materialPreviewData ?? {};
    int count = segmentList?.length ?? 0;
    
    return Container(
      color: Colors.white,
      child: PageView.builder(
        controller: _controller.pageController,
        scrollDirection: Axis.vertical,
        itemCount: count,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          var segmentId = segmentList?[index].segmentId;
          var model = materialData[segmentId];
          return MaterialContentListWidget(
            model: model,
            locateLessonKey: widget.state.lessonKey,
            index: index,
            subjectColor: widget.subjectColor,
            contentWidth: widget.contentWidth,
            onRefresh: index > 0
                ? () {
                    _controller.changeToLastPage();
                  }
                : null,
            onLoad: index < count - 1
                ? () {
                    _controller.changeToNextPage();
                  }
                : null,
          );
        },
        onPageChanged: (value) { },
      ),
    );
  }
}
