import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:jojo_flutter_base/base.dart';

class MaterialCustomHeader extends Header {
  MaterialCustomHeader() : super(
          extent: 60.rdp,
          triggerDistance: 60.rdp,
          completeDuration: const Duration(milliseconds: 300),
          enableInfiniteRefresh: false,
          enableHapticFeedback: true,
          overScroll: true,
        );

  @override
  Widget contentBuilder(
      BuildContext context,
      RefreshMode refreshState,
      double pulledExtent,
      double refreshTriggerPullDistance,
      double refreshIndicatorExtent,
      AxisDirection axisDirection,
      bool float,
      Duration? completeDuration,
      bool enableInfiniteRefresh,
      bool success,
      bool noMore) {
    return Container(
      // color: Colors.amber,
    );
  }
}

class MaterialCustomFooter extends Footer {
  MaterialCustomFooter() : super(
          extent: 60.rdp,
          triggerDistance: 60.rdp,
          completeDuration: const Duration(seconds: 300),
          enableInfiniteLoad: false,
          enableHapticFeedback: true,
          overScroll: true,
          safeArea: false,
        );

  @override
  Widget contentBuilder(
      BuildContext context,
      LoadMode loadState,
      double pulledExtent,
      double loadTriggerPullDistance,
      double loadIndicatorExtent,
      AxisDirection axisDirection,
      bool float,
      Duration? completeDuration,
      bool enableInfiniteLoad,
      bool success,
      bool noMore) {
    return Container(
      // color: Colors.green,
    );
  }
}
