import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';

import '../../model/art_material_preview_model.dart';

class ArtMaterialPreviewItem extends StatelessWidget {
  final ArtMaterialPreviewModel? model;
  final Color subjectColor;
  final double itemHeight;
  final double imageHeight;
  final double imageWidth;

  const ArtMaterialPreviewItem({
    super.key,
    required this.model,
    required this.subjectColor,
    required this.imageWidth,
    required this.itemHeight,
    required this.imageHeight,
  });

  double fit(BuildContext context, double size) {
    return size * (layout.isTablet(context) ? 1.25 : 1.0);
  }

  @override
  Widget build(BuildContext context) {
    var scale = MediaQuery.of(context).devicePixelRatio.toInt();

    return Container(
      padding: EdgeInsets.only(top: fit(context, 20.rdp)),
      width: imageWidth,
      height: itemHeight,
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(right: 24.rdp),
            child: _titleWidget(context),
          ),
          Expanded(child: Container()),
          Container(
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              color: context.appColors.colorVariant2(subjectColor),
              borderRadius: BorderRadius.circular(fit(context, 8.rdp)),
            ),
            child: ImageNetworkCached(
              width: imageWidth,
              height: imageHeight,
              fit: BoxFit.contain,
              imageUrl: model?.lessonPreparationImage ?? '',
              memCacheWidth: imageWidth.toInt() * scale,
            ),
          ),
        ],
      ),
    );
  }

  Widget _titleWidget(BuildContext context) {
    final lessonOrderDesc = model?.lessonOrderDesc ?? '';
    final lessonName = model?.lessonName ?? '';

    final style = context.textstyles.bodyTextEmphasis.pf.copyWith(
      color: context.appColors.jColorGray6,
      fontSize: fit(context, 16.rdp),
    );

    List<InlineSpan>? textSpans = [];
    textSpans.add(TextSpan(text: lessonOrderDesc, style: style));
    textSpans.add(TextSpan(text: ' $lessonName', style: style));

    return RichText(
      textAlign: TextAlign.left,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(children: textSpans),
    );
  }
}
