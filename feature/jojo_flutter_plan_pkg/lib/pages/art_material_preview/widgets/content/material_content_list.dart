import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/cached_network_image.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/pages/art_material_preview/widgets/content/material_content_item.dart';

import '../../../../common/config/config.dart';
import '../../../../static/img.dart';
import '../../controller.dart';
import '../../model/art_material_preview_model.dart';
import '../../../review_detail/widget/review_refresh_footer.dart';
import '../../../review_detail/widget/review_refresh_header.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:jojo_pull_to_refresh/pull_to_refresh.dart';

import 'material_content_subview.dart';

class MaterialContentListWidget extends StatefulWidget {
  final ArtMaterialPreviewListModel? model;
  final String? locateLessonKey;
  final VoidCallback? onRefresh;
  final VoidCallback? onLoad;
  final int index;
  final Color subjectColor;
  final double contentWidth;

  const MaterialContentListWidget({
    super.key,
    this.onRefresh,
    this.onLoad,
    required this.index,
    required this.model,
    this.locateLessonKey,
    required this.subjectColor,
    required this.contentWidth,
  });

  @override
  State<MaterialContentListWidget> createState() =>
      _MaterialContentListWidgetState();
}

class _MaterialContentListWidgetState extends State<MaterialContentListWidget> {
  final ScrollController _scrollController = ScrollController();
  final EasyRefreshController _refreshController = EasyRefreshController();
  late ArtMaterialPreviewController _controller;
  late double _widgetHeight;

  double fit(double size) {
    return size * (layout.isTablet(context) ? 1.25 : 1.0);
  }

  final double _itemImageScale = 660 / 1200; // 线上配置的图片高度/宽度
  late double _listPadding;
  late double _itemTopPadding;
  late double _itemHeight;
  late double _imageWidth;
  late double _imageHeight;

  @override
  void initState() {
    super.initState();
    _controller = context.read<ArtMaterialPreviewController>();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToInitialIndex();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  _scrollToInitialIndex() {
    int locateIndex = 0;
    var locateLessonKey = widget.locateLessonKey;
    if (locateLessonKey != null) {
      locateIndex = widget.model?.lessonResList
              ?.indexWhere((element) => element.lessonKey == locateLessonKey) ??
          0;
    }

    // if (locateIndex == 0) {
    //   return;
    // }

    double listTopPadding = fit(8.rdp);
    double listBottomPadding =
        fit(20.rdp) + MediaQuery.of(context).padding.bottom;
    //var itemHeight = fit(290.rdp);
    double itemCenterPosition = (locateIndex + 0.5) * _itemHeight + listTopPadding; // item中心位置
    double fullHeight =
        (widget.model?.lessonResList?.length ?? 0) * _itemHeight +
            listTopPadding +
            listBottomPadding; // 整个列表高度
    if (itemCenterPosition < _widgetHeight / 2 || fullHeight < _widgetHeight) {
      // item位于上半屏 或者 整个列表高度没有屏幕高，不需要滑动
      return;
    }

    double offset = itemCenterPosition - _widgetHeight / 2 + 10.rdp; // 滚动item到屏幕中间需要的距离
    offset = min(offset, fullHeight - _widgetHeight); // 不能超出最大滚动距离
    _scrollController.jumpTo(offset);
  }

  @override
  Widget build(BuildContext context) {
    _listPadding = fit(24.rdp);
    _itemTopPadding = fit(20.rdp);
    _imageWidth = widget.contentWidth - _listPadding * 2;
    _imageHeight = _imageWidth * _itemImageScale;
    _itemHeight = _imageHeight + fit(32.rdp) + _itemTopPadding;
    _widgetHeight = MediaQuery.of(context).size.height;

    return Container(
      height: _widgetHeight,
      width: MediaQuery.of(context).size.width,
      color: Colors.white,
      child: EasyRefresh(
        controller: _refreshController,
        header: MaterialCustomHeader(),
        footer: MaterialCustomFooter(),
        onRefresh: widget.onRefresh != null
            ? () async {
                widget.onRefresh?.call();
              }
            : null,
        onLoad: widget.onLoad != null
            ? () async {
                widget.onLoad?.call();
              }
            : null,
        child: _getBody(context),
      ),
    );
  }

  Widget _getBody(BuildContext context) {
    var model = widget.model;
    if (model != null) {
      if ((model.lessonResList ?? []).isNotEmpty) {
        return _buildList();
      } else {
        return Container();
      }
    } else {
      return _buildLoading(context);
    }
  }

  Widget _buildList() {
    return ListView.builder(
      padding: EdgeInsets.only(
        top: fit(8.rdp),
        left: _listPadding,
        bottom: fit(20.rdp) + MediaQuery.of(context).padding.bottom,
      ),
      controller: _scrollController,
      itemCount: widget.model?.lessonResList?.length ?? 0,
      itemBuilder: (context, index) {
        return ArtMaterialPreviewItem(
          model: widget.model?.lessonResList?[index],
          subjectColor: widget.subjectColor,
          imageHeight: _imageHeight,
          imageWidth: _imageWidth,
          itemHeight: _itemHeight,
        );
      },
    );
  }

  Widget _buildLoading(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      alignment: Alignment.center, // 保持居中
      child: ImageAssetWeb(
        assetName: AssetsImg.SXZ,
        width: 130.rdp,
        height: 100.rdp,
        package: Config.package,
        fit: BoxFit.contain,
      ),
    );
  }
}
