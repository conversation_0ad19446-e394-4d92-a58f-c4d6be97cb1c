import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';

import '../../controller.dart';

class MaterialTitleListWidget extends StatefulWidget {
  final int segmentId;
  final Color subjectColor;

  const MaterialTitleListWidget({
    super.key,
    required this.segmentId,
    required this.subjectColor,
  });

  @override
  State<MaterialTitleListWidget> createState() =>
      _MaterialTitleListWidgetState();
}

class _MaterialTitleListWidgetState extends State<MaterialTitleListWidget>
    with AutomaticKeepAliveClientMixin {
  final double itemHeight = 43.rdp;
  final double itemWidth = 150.rdp;

  final ScrollController _scrollController = ScrollController();
  late ArtMaterialPreviewController _controller;
  late double _widgetHeight;

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  double fit(double size) {
    return size * (layout.isTablet(context) ? 1.25 : 1.0);
  }

  @override
  void initState() {
    super.initState();

    _controller = context.read<ArtMaterialPreviewController>();
    _controller.pageWillChangeNotifier.addListener(() {
      int pageIndex = _controller.pageWillChangeNotifier.value;
      if (pageIndex >= 0 && pageIndex < _controller.segmentList.length) {
        scrollToIndex(pageIndex);
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToInitialIndex(_controller.currentPageIndex);
    });
  }

  _scrollToInitialIndex(int index) {
    if (_controller.segmentList.isEmpty) {
      return;
    }

    // item中心位置
    double itemCenterPosition = index * fit(itemHeight) + fit(itemHeight) / 2 + fit(20.rdp);

    // 整个列表高度
    double fullHeight = _controller.segmentList.length * fit(itemHeight) + fit(20.rdp);

    // item位于上半屏 或者 整个列表高度没有屏幕高，不需要滑动
    if (itemCenterPosition < _widgetHeight / 2 || fullHeight < _widgetHeight) {
      return;
    }

    // 滚动item到屏幕中间需要的距离
    double offset = itemCenterPosition - _widgetHeight / 2;

    // 不能超出最大滚动距离 (如果item是最后一个，只要让item显示出来就可以)
    offset = min(offset, fullHeight - _widgetHeight);

    _scrollController.jumpTo(offset);
  }

  scrollToIndex(int index) {
    double currentOffset = _scrollController.offset;
    double scrollOffset = currentOffset;

    /// item在屏幕上方,向下滑动出现item
    if (index * fit(itemHeight) + fit(20.rdp) < currentOffset) {
      scrollOffset = index * fit(itemHeight);
    }

    /// item在屏幕下方,向上滑动出现item
    if ((index + 1) * fit(itemHeight) + fit(20.rdp) >
        currentOffset + _widgetHeight) {
      scrollOffset = (index + 1) * fit(itemHeight) - _widgetHeight + fit(40.rdp);
    }

    if (scrollOffset != currentOffset) {
      _scrollController.animateTo(
        scrollOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (_controller.segmentList.isEmpty) {
      return Container();
    }

    _widgetHeight = MediaQuery.of(context).size.height;
    return ListView.builder(
      padding: EdgeInsets.only(
        top: fit(20.rdp),
        bottom: fit(10.rdp) + MediaQuery.of(context).padding.bottom,
      ),
      controller: _scrollController,
      itemCount: _controller.segmentList.length,
      itemBuilder: (BuildContext context, int index) {
        return _buildItem(context, index);
      },
    );
  }

  Widget _buildItem(
    BuildContext context,
    int index,
  ) {
    var segmentModel = _controller.segmentList[index];
    var selected = index == _controller.currentPageIndex;

    var selectStyle = TextStyle(
      fontSize: fit(16.rdp),
      color: context.appColors.colorVariant6(widget.subjectColor),
      fontFamily: 'PingFang SC',
      fontWeight: FontWeight.w500,
    );

    var unSelectStyle = TextStyle(
      fontSize: fit(16.rdp),
      color:
          context.appColors.colorVariant6(widget.subjectColor).withOpacity(0.6),
      fontFamily: 'PingFang SC',
      fontWeight: FontWeight.w500,
    );

    return GestureDetector(
      child: Container(
        color: Colors.transparent,
        width: fit(itemWidth),
        height: fit(itemHeight),
        child: Row(
          children: [
            SizedBox(width: fit(14.rdp)),
            selected
                ? Container(
                    width: fit(4.rdp),
                    height: fit(20.rdp),
                    decoration: BoxDecoration(
                      color:
                          context.appColors.colorVariant4(widget.subjectColor),
                      borderRadius: BorderRadius.circular(fit(2.rdp)),
                    ),
                  )
                : SizedBox(
                    width: fit(4.rdp),
                  ),
            SizedBox(width: fit(6.rdp)),
            Expanded(
              child: Text(
                segmentModel.segmentName ?? '',
                style: selected ? selectStyle : unSelectStyle,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: fit(12.rdp)),
          ],
        ),
      ),
      onTap: () {
        _controller.changeToPage(index);
        scrollToIndex(index);
      },
    );
  }
}
