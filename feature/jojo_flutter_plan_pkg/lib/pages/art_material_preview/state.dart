import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/segments_data.dart';

import 'model/art_material_preview_model.dart';

class ArtMaterialPreviewState {
  final PageStatus pageStatus;
  final Exception? exception;
  
  final int? segmentId;
  final String? lessonKey;
  final PageStatus? childPageStatus;

  /// 主题月数据
  final List<SegmentInfoModel>? segmentList;
  
  /// 画材预览数据
  final Map<int, ArtMaterialPreviewListModel?>? materialPreviewData;

  ArtMaterialPreviewState(this.pageStatus,
      {this.exception,
      this.segmentList,
      this.materialPreviewData,
      this.segmentId,
      this.lessonKey,
      this.childPageStatus});

  ArtMaterialPreviewState copyWith(
    PageStatus? pageStatus, {
    Exception? exception,
    List<SegmentInfoModel>? segmentList,
    Map<int, ArtMaterialPreviewListModel>? materialPreviewData,
    int? segmentId,
    String? lessonKey,
    PageStatus? childPageStatus,
  }) {
    return ArtMaterialPreviewState(
      pageStatus ?? this.pageStatus,
      exception: exception ?? this.exception,
      materialPreviewData: materialPreviewData ?? this.materialPreviewData,
      segmentList: segmentList ?? this.segmentList,
      segmentId: segmentId ?? this.segmentId,
      lessonKey: lessonKey,
      childPageStatus: childPageStatus ?? this.childPageStatus,
    );
  }
}
