import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

import '../../static/img.dart';
import 'state.dart';
import 'widgets/content/material_content.dart';
import 'widgets/title/material_title_widget.dart';

class ArtMaterialPreviewView extends StatefulHookWidget {
  final ArtMaterialPreviewState state;
  final Color subjectColor;

  const ArtMaterialPreviewView({
    Key? key,
    required this.state,
    required this.subjectColor,
  }) : super(key: key);

  @override
  State<ArtMaterialPreviewView> createState() => _ArtMaterialPreviewViewState();
}

class _ArtMaterialPreviewViewState extends State<ArtMaterialPreviewView> {
  // 根据设备类型（平板或手机）调整尺寸的辅助函数
  double fit(double size) {
    return size * (layout.isTablet(context) ? 1.25 : 1.0);
  }

  @override
  Widget build(BuildContext context) {
    var backLeft = fit(24.rdp);     // 返回按钮距离左侧的距离
    var backTop = fit(24.rdp);      // 返回按钮距离顶部的距离
    var backWidth = fit(47.rdp);    // 返回按钮宽度
    var backHeight = fit(51.rdp);   // 返回按钮高度
    
    // 定义标题列表的宽度和内容区域的最大宽度
    var titleWidth = fit(198.rdp);      // 左侧标题列表的固定宽度
    var maxContentWidth = fit(480.rdp); // 内容区域的最大宽度

    // 初始化内容区域宽度为最大宽度
    var contentWidth = maxContentWidth;
    var safeLeftPadding = backLeft + backWidth;  // 左侧安全边距（返回按钮左侧位置+宽度）
    var screenWidth = MediaQuery.of(context).size.width; // 获取屏幕宽度
    
    // 检查屏幕宽度是否足够容纳所有元素（左侧返回按钮、标题列表、内容区域和右侧边距）
    if (screenWidth < safeLeftPadding * 2 + titleWidth + maxContentWidth) {
      // 小屏幕，左侧返回按钮会与标题列表重叠，需要调整内容宽度
      var smallContentWidth = screenWidth - safeLeftPadding * 2 - titleWidth;
      contentWidth = min(maxContentWidth, smallContentWidth);
    }

    return Container(
      color: context.appColors.colorVariant2(widget.subjectColor),
      child: Stack(
        children: [
          Positioned(
            left: backLeft,
            top: backTop,
            child: GestureDetector(
              onTap: () {
                RunEnv.pop();
              },
              child: ImageAssetWeb(
                assetName: AssetsImg.COURSE_SESSION_LIST_ARROW_YELLOW_BACK,
                package: Config.package,
                height: backHeight,
                width: backWidth,
                fit: BoxFit.fill,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: titleWidth,
                height: MediaQuery.of(context).size.height,
                child: MaterialTitleListWidget(
                  segmentId: widget.state.segmentId ?? 0,
                  subjectColor: widget.subjectColor,
                ),
              ),
              SizedBox(
                width: contentWidth,
                height: MediaQuery.of(context).size.height,
                child: MaterialContentWidget(
                  state: widget.state,
                  subjectColor: widget.subjectColor,
                  contentWidth: contentWidth,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
