import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../../common/config/config.dart';
import '../../common/host_env/host_env.dart';
import '../../static/img.dart';
import 'controller.dart';
import 'state.dart';
import 'view.dart';

class ArtMaterialPreviewPage extends BasePage {
  final String classKey;
  final int segmentId;
  final String lessonKey;
  final int? loadingScene;
  final String? subjectColor;

  const ArtMaterialPreviewPage({
    Key? key,
    required this.classKey,
    required this.segmentId,
    required this.lessonKey,
    this.loadingScene,
    this.subjectColor,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => ArtMaterialPreviewPageState();
}

class ArtMaterialPreviewPageState extends BaseState<ArtMaterialPreviewPage> {
  late ArtMaterialPreviewController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ArtMaterialPreviewController.withDefault(
      classKey: widget.classKey,
      initialSegmentId: widget.segmentId,
      initialLessonKey: widget.lessonKey,
    );
    _initSysStatus(true);

    RunEnv.sensorsTrack('\$AppViewScreen', {
      '\$screen_name': '画材预览详情页',
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
    _initSysStatus(false);
  }

  // 设置横屏、状态栏
  void _initSysStatus(bool init) {
    if (init) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.manual,
        overlays: [],
      );
    } else {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: [],
      );
    }
  }

  double fit(double size) {
    return size * (layout.isTablet(context) ? 1.25 : 1.0);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => _controller,
      child: Builder(
        builder: (innerContext) {
          return BlocBuilder<ArtMaterialPreviewController,
              ArtMaterialPreviewState>(
            builder: (context, state) {
              return Scaffold(
                body: JoJoPageLoadingV25(
                  scene: PageScene.fromValue(widget.loadingScene ?? 3) ??
                      PageScene.getDefault(),
                  hideProgress: true,
                  status: state.pageStatus,
                  retry: () {
                    _controller.errorRefreshData();
                  },
                  backWidget: _getBackButton(),
                  child: ArtMaterialPreviewView(
                    state: state,
                    subjectColor: _getSubjectColor(),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Color _getSubjectColor() {
    var color = widget.subjectColor ?? '#AE84E3';
    return HexColor(color);
  }

  Widget _getBackButton() {
    return Positioned(
      left: fit(24.rdp),
      top: fit(24.rdp),
      child: GestureDetector(
        onTap: () {
          RunEnv.pop();
        },
        child: ImageAssetWeb(
          assetName: AssetsImg.COURSE_SESSION_LIST_ARROW_YELLOW_BACK,
          package: Config.package,
          height: fit(51.rdp),
          width: fit(47.rdp),
          fit: BoxFit.fill,
        ),
      ),
    );
  }
}
