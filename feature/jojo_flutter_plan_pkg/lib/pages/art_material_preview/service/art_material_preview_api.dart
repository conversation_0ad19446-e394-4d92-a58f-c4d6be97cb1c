import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/common/dio/use.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/segments_data.dart';
import 'package:retrofit/http.dart';

import '../model/art_material_preview_model.dart';

part 'art_material_preview_api.g.dart';

@RestApi()
abstract class ArtMaterialPreviewApi {
  factory ArtMaterialPreviewApi(Dio dio, {String baseUrl}) =
      _ArtMaterialPreviewApi;

  /// 主题月列表接口
  /// 画材预览场景，sceneType=LESSON_PREPARATION
  @GET("/api/pagani/classes/{classKey}/segments")
  Future<SegmentsData> getSegments(
    @Path('classKey') String classKey,
    @Query('sceneType') String sceneType,
  );

  /// 新增查询课前准备资源（画材图片+音频）列表接口
  @GET("/api/pagani/classes/{classKey}/study-prepare-resources")
  Future<ArtMaterialPreviewListModel> getStudyPrepareResources(
    @Path('classKey') String classKey,
    @Query('segmentIds') String segmentIds,
  );
}

final proArtMaterialPreviewApi = ArtMaterialPreviewApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);

