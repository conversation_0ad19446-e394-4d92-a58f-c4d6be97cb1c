import 'package:freezed_annotation/freezed_annotation.dart';

part 'art_material_preview_model.freezed.dart';
part 'art_material_preview_model.g.dart';

@freezed
class ArtMaterialPreviewListModel with _$ArtMaterialPreviewListModel {
  factory ArtMaterialPreviewListModel({
    List<ArtMaterialPreviewModel>? lessonResList,
  }) = _ArtMaterialPreviewListModel;

  factory ArtMaterialPreviewListModel.fromJson(Map<String, dynamic> json) =>
      _$ArtMaterialPreviewListModelFromJson(json);
}

@freezed
class ArtMaterialPreviewModel with _$ArtMaterialPreviewModel {
  factory ArtMaterialPreviewModel({
    int? classId,
    int? segmentId,
    int? lessonId,
    String? lessonKey,
    /// 课时主标题
    String? lessonName,
    /// 副标题
    String? lessonSubTitle,
    /// 第1次
    String? lessonOrderDesc,
    /// 预览图
    String? lessonPreparationImage,
    String? lessonPreparationAudio,
  }) = _ArtMaterialPreviewModel;

  factory ArtMaterialPreviewModel.fromJson(Map<String, dynamic> json) =>
      _$ArtMaterialPreviewModelFromJson(json);
}
