// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'art_material_preview_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

ArtMaterialPreviewListModel _$ArtMaterialPreviewListModelFromJson(
    Map<String, dynamic> json) {
  return _ArtMaterialPreviewListModel.fromJson(json);
}

/// @nodoc
mixin _$ArtMaterialPreviewListModel {
  List<ArtMaterialPreviewModel>? get lessonResList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ArtMaterialPreviewListModelCopyWith<ArtMaterialPreviewListModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArtMaterialPreviewListModelCopyWith<$Res> {
  factory $ArtMaterialPreviewListModelCopyWith(
          ArtMaterialPreviewListModel value,
          $Res Function(ArtMaterialPreviewListModel) then) =
      _$ArtMaterialPreviewListModelCopyWithImpl<$Res,
          ArtMaterialPreviewListModel>;
  @useResult
  $Res call({List<ArtMaterialPreviewModel>? lessonResList});
}

/// @nodoc
class _$ArtMaterialPreviewListModelCopyWithImpl<$Res,
        $Val extends ArtMaterialPreviewListModel>
    implements $ArtMaterialPreviewListModelCopyWith<$Res> {
  _$ArtMaterialPreviewListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonResList = freezed,
  }) {
    return _then(_value.copyWith(
      lessonResList: freezed == lessonResList
          ? _value.lessonResList
          : lessonResList // ignore: cast_nullable_to_non_nullable
              as List<ArtMaterialPreviewModel>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ArtMaterialPreviewListModelCopyWith<$Res>
    implements $ArtMaterialPreviewListModelCopyWith<$Res> {
  factory _$$_ArtMaterialPreviewListModelCopyWith(
          _$_ArtMaterialPreviewListModel value,
          $Res Function(_$_ArtMaterialPreviewListModel) then) =
      __$$_ArtMaterialPreviewListModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ArtMaterialPreviewModel>? lessonResList});
}

/// @nodoc
class __$$_ArtMaterialPreviewListModelCopyWithImpl<$Res>
    extends _$ArtMaterialPreviewListModelCopyWithImpl<$Res,
        _$_ArtMaterialPreviewListModel>
    implements _$$_ArtMaterialPreviewListModelCopyWith<$Res> {
  __$$_ArtMaterialPreviewListModelCopyWithImpl(
      _$_ArtMaterialPreviewListModel _value,
      $Res Function(_$_ArtMaterialPreviewListModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lessonResList = freezed,
  }) {
    return _then(_$_ArtMaterialPreviewListModel(
      lessonResList: freezed == lessonResList
          ? _value._lessonResList
          : lessonResList // ignore: cast_nullable_to_non_nullable
              as List<ArtMaterialPreviewModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ArtMaterialPreviewListModel implements _ArtMaterialPreviewListModel {
  _$_ArtMaterialPreviewListModel(
      {final List<ArtMaterialPreviewModel>? lessonResList})
      : _lessonResList = lessonResList;

  factory _$_ArtMaterialPreviewListModel.fromJson(Map<String, dynamic> json) =>
      _$$_ArtMaterialPreviewListModelFromJson(json);

  final List<ArtMaterialPreviewModel>? _lessonResList;
  @override
  List<ArtMaterialPreviewModel>? get lessonResList {
    final value = _lessonResList;
    if (value == null) return null;
    if (_lessonResList is EqualUnmodifiableListView) return _lessonResList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ArtMaterialPreviewListModel(lessonResList: $lessonResList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ArtMaterialPreviewListModel &&
            const DeepCollectionEquality()
                .equals(other._lessonResList, _lessonResList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_lessonResList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ArtMaterialPreviewListModelCopyWith<_$_ArtMaterialPreviewListModel>
      get copyWith => __$$_ArtMaterialPreviewListModelCopyWithImpl<
          _$_ArtMaterialPreviewListModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ArtMaterialPreviewListModelToJson(
      this,
    );
  }
}

abstract class _ArtMaterialPreviewListModel
    implements ArtMaterialPreviewListModel {
  factory _ArtMaterialPreviewListModel(
          {final List<ArtMaterialPreviewModel>? lessonResList}) =
      _$_ArtMaterialPreviewListModel;

  factory _ArtMaterialPreviewListModel.fromJson(Map<String, dynamic> json) =
      _$_ArtMaterialPreviewListModel.fromJson;

  @override
  List<ArtMaterialPreviewModel>? get lessonResList;
  @override
  @JsonKey(ignore: true)
  _$$_ArtMaterialPreviewListModelCopyWith<_$_ArtMaterialPreviewListModel>
      get copyWith => throw _privateConstructorUsedError;
}

ArtMaterialPreviewModel _$ArtMaterialPreviewModelFromJson(
    Map<String, dynamic> json) {
  return _ArtMaterialPreviewModel.fromJson(json);
}

/// @nodoc
mixin _$ArtMaterialPreviewModel {
  int? get classId => throw _privateConstructorUsedError;
  int? get segmentId => throw _privateConstructorUsedError;
  int? get lessonId => throw _privateConstructorUsedError;
  String? get lessonKey => throw _privateConstructorUsedError;

  /// 课时主标题
  String? get lessonName => throw _privateConstructorUsedError;

  /// 副标题
  String? get lessonSubTitle => throw _privateConstructorUsedError;

  /// 第1次
  String? get lessonOrderDesc => throw _privateConstructorUsedError;

  /// 预览图
  String? get lessonPreparationImage => throw _privateConstructorUsedError;
  String? get lessonPreparationAudio => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ArtMaterialPreviewModelCopyWith<ArtMaterialPreviewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ArtMaterialPreviewModelCopyWith<$Res> {
  factory $ArtMaterialPreviewModelCopyWith(ArtMaterialPreviewModel value,
          $Res Function(ArtMaterialPreviewModel) then) =
      _$ArtMaterialPreviewModelCopyWithImpl<$Res, ArtMaterialPreviewModel>;
  @useResult
  $Res call(
      {int? classId,
      int? segmentId,
      int? lessonId,
      String? lessonKey,
      String? lessonName,
      String? lessonSubTitle,
      String? lessonOrderDesc,
      String? lessonPreparationImage,
      String? lessonPreparationAudio});
}

/// @nodoc
class _$ArtMaterialPreviewModelCopyWithImpl<$Res,
        $Val extends ArtMaterialPreviewModel>
    implements $ArtMaterialPreviewModelCopyWith<$Res> {
  _$ArtMaterialPreviewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? segmentId = freezed,
    Object? lessonId = freezed,
    Object? lessonKey = freezed,
    Object? lessonName = freezed,
    Object? lessonSubTitle = freezed,
    Object? lessonOrderDesc = freezed,
    Object? lessonPreparationImage = freezed,
    Object? lessonPreparationAudio = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSubTitle: freezed == lessonSubTitle
          ? _value.lessonSubTitle
          : lessonSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrderDesc: freezed == lessonOrderDesc
          ? _value.lessonOrderDesc
          : lessonOrderDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonPreparationImage: freezed == lessonPreparationImage
          ? _value.lessonPreparationImage
          : lessonPreparationImage // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonPreparationAudio: freezed == lessonPreparationAudio
          ? _value.lessonPreparationAudio
          : lessonPreparationAudio // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ArtMaterialPreviewModelCopyWith<$Res>
    implements $ArtMaterialPreviewModelCopyWith<$Res> {
  factory _$$_ArtMaterialPreviewModelCopyWith(_$_ArtMaterialPreviewModel value,
          $Res Function(_$_ArtMaterialPreviewModel) then) =
      __$$_ArtMaterialPreviewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? classId,
      int? segmentId,
      int? lessonId,
      String? lessonKey,
      String? lessonName,
      String? lessonSubTitle,
      String? lessonOrderDesc,
      String? lessonPreparationImage,
      String? lessonPreparationAudio});
}

/// @nodoc
class __$$_ArtMaterialPreviewModelCopyWithImpl<$Res>
    extends _$ArtMaterialPreviewModelCopyWithImpl<$Res,
        _$_ArtMaterialPreviewModel>
    implements _$$_ArtMaterialPreviewModelCopyWith<$Res> {
  __$$_ArtMaterialPreviewModelCopyWithImpl(_$_ArtMaterialPreviewModel _value,
      $Res Function(_$_ArtMaterialPreviewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? segmentId = freezed,
    Object? lessonId = freezed,
    Object? lessonKey = freezed,
    Object? lessonName = freezed,
    Object? lessonSubTitle = freezed,
    Object? lessonOrderDesc = freezed,
    Object? lessonPreparationImage = freezed,
    Object? lessonPreparationAudio = freezed,
  }) {
    return _then(_$_ArtMaterialPreviewModel(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      segmentId: freezed == segmentId
          ? _value.segmentId
          : segmentId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonId: freezed == lessonId
          ? _value.lessonId
          : lessonId // ignore: cast_nullable_to_non_nullable
              as int?,
      lessonKey: freezed == lessonKey
          ? _value.lessonKey
          : lessonKey // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonName: freezed == lessonName
          ? _value.lessonName
          : lessonName // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonSubTitle: freezed == lessonSubTitle
          ? _value.lessonSubTitle
          : lessonSubTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonOrderDesc: freezed == lessonOrderDesc
          ? _value.lessonOrderDesc
          : lessonOrderDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonPreparationImage: freezed == lessonPreparationImage
          ? _value.lessonPreparationImage
          : lessonPreparationImage // ignore: cast_nullable_to_non_nullable
              as String?,
      lessonPreparationAudio: freezed == lessonPreparationAudio
          ? _value.lessonPreparationAudio
          : lessonPreparationAudio // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ArtMaterialPreviewModel implements _ArtMaterialPreviewModel {
  _$_ArtMaterialPreviewModel(
      {this.classId,
      this.segmentId,
      this.lessonId,
      this.lessonKey,
      this.lessonName,
      this.lessonSubTitle,
      this.lessonOrderDesc,
      this.lessonPreparationImage,
      this.lessonPreparationAudio});

  factory _$_ArtMaterialPreviewModel.fromJson(Map<String, dynamic> json) =>
      _$$_ArtMaterialPreviewModelFromJson(json);

  @override
  final int? classId;
  @override
  final int? segmentId;
  @override
  final int? lessonId;
  @override
  final String? lessonKey;

  /// 课时主标题
  @override
  final String? lessonName;

  /// 副标题
  @override
  final String? lessonSubTitle;

  /// 第1次
  @override
  final String? lessonOrderDesc;

  /// 预览图
  @override
  final String? lessonPreparationImage;
  @override
  final String? lessonPreparationAudio;

  @override
  String toString() {
    return 'ArtMaterialPreviewModel(classId: $classId, segmentId: $segmentId, lessonId: $lessonId, lessonKey: $lessonKey, lessonName: $lessonName, lessonSubTitle: $lessonSubTitle, lessonOrderDesc: $lessonOrderDesc, lessonPreparationImage: $lessonPreparationImage, lessonPreparationAudio: $lessonPreparationAudio)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ArtMaterialPreviewModel &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.segmentId, segmentId) ||
                other.segmentId == segmentId) &&
            (identical(other.lessonId, lessonId) ||
                other.lessonId == lessonId) &&
            (identical(other.lessonKey, lessonKey) ||
                other.lessonKey == lessonKey) &&
            (identical(other.lessonName, lessonName) ||
                other.lessonName == lessonName) &&
            (identical(other.lessonSubTitle, lessonSubTitle) ||
                other.lessonSubTitle == lessonSubTitle) &&
            (identical(other.lessonOrderDesc, lessonOrderDesc) ||
                other.lessonOrderDesc == lessonOrderDesc) &&
            (identical(other.lessonPreparationImage, lessonPreparationImage) ||
                other.lessonPreparationImage == lessonPreparationImage) &&
            (identical(other.lessonPreparationAudio, lessonPreparationAudio) ||
                other.lessonPreparationAudio == lessonPreparationAudio));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      classId,
      segmentId,
      lessonId,
      lessonKey,
      lessonName,
      lessonSubTitle,
      lessonOrderDesc,
      lessonPreparationImage,
      lessonPreparationAudio);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ArtMaterialPreviewModelCopyWith<_$_ArtMaterialPreviewModel>
      get copyWith =>
          __$$_ArtMaterialPreviewModelCopyWithImpl<_$_ArtMaterialPreviewModel>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ArtMaterialPreviewModelToJson(
      this,
    );
  }
}

abstract class _ArtMaterialPreviewModel implements ArtMaterialPreviewModel {
  factory _ArtMaterialPreviewModel(
      {final int? classId,
      final int? segmentId,
      final int? lessonId,
      final String? lessonKey,
      final String? lessonName,
      final String? lessonSubTitle,
      final String? lessonOrderDesc,
      final String? lessonPreparationImage,
      final String? lessonPreparationAudio}) = _$_ArtMaterialPreviewModel;

  factory _ArtMaterialPreviewModel.fromJson(Map<String, dynamic> json) =
      _$_ArtMaterialPreviewModel.fromJson;

  @override
  int? get classId;
  @override
  int? get segmentId;
  @override
  int? get lessonId;
  @override
  String? get lessonKey;
  @override

  /// 课时主标题
  String? get lessonName;
  @override

  /// 副标题
  String? get lessonSubTitle;
  @override

  /// 第1次
  String? get lessonOrderDesc;
  @override

  /// 预览图
  String? get lessonPreparationImage;
  @override
  String? get lessonPreparationAudio;
  @override
  @JsonKey(ignore: true)
  _$$_ArtMaterialPreviewModelCopyWith<_$_ArtMaterialPreviewModel>
      get copyWith => throw _privateConstructorUsedError;
}
