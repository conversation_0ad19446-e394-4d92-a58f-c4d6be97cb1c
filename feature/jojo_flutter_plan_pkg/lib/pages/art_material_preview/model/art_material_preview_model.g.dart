// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'art_material_preview_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_ArtMaterialPreviewListModel _$$_ArtMaterialPreviewListModelFromJson(
        Map<String, dynamic> json) =>
    _$_ArtMaterialPreviewListModel(
      lessonResList: (json['lessonResList'] as List<dynamic>?)
          ?.map((e) =>
              ArtMaterialPreviewModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_ArtMaterialPreviewListModelToJson(
        _$_ArtMaterialPreviewListModel instance) =>
    <String, dynamic>{
      'lessonResList': instance.lessonResList,
    };

_$_ArtMaterialPreviewModel _$$_ArtMaterialPreviewModelFromJson(
        Map<String, dynamic> json) =>
    _$_ArtMaterialPreviewModel(
      classId: json['classId'] as int?,
      segmentId: json['segmentId'] as int?,
      lessonId: json['lessonId'] as int?,
      lessonKey: json['lessonKey'] as String?,
      lessonName: json['lessonName'] as String?,
      lessonSubTitle: json['lessonSubTitle'] as String?,
      lessonOrderDesc: json['lessonOrderDesc'] as String?,
      lessonPreparationImage: json['lessonPreparationImage'] as String?,
      lessonPreparationAudio: json['lessonPreparationAudio'] as String?,
    );

Map<String, dynamic> _$$_ArtMaterialPreviewModelToJson(
        _$_ArtMaterialPreviewModel instance) =>
    <String, dynamic>{
      'classId': instance.classId,
      'segmentId': instance.segmentId,
      'lessonId': instance.lessonId,
      'lessonKey': instance.lessonKey,
      'lessonName': instance.lessonName,
      'lessonSubTitle': instance.lessonSubTitle,
      'lessonOrderDesc': instance.lessonOrderDesc,
      'lessonPreparationImage': instance.lessonPreparationImage,
      'lessonPreparationAudio': instance.lessonPreparationAudio,
    };
