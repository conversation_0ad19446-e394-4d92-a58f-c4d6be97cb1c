import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/models/graiy_info.dart';

part 'ai_frequency.freezed.dart';
part 'ai_frequency.g.dart';

@freezed
class AiFrequency with _$AiFrequency {
  const factory AiFrequency({
    String? callVoice, // 拨打电话等待提示音
    String? localCallVoice, // 拨打电话等待提示音 本地文件
    int? dayCallFrequency, // 会话总次数
    String? ableCallVoice, // 可拨打时语音提示音
    String? localAbleCallVoice, // 可拨打时语音提示音 本地文件
    String? ableCallTip, // 可拨打时提示文案
    String? disableCallVoice, // 不可拨打时语音提示音
    String? localDisableCallVoice, // 不可拨打时语音提示音 本地文件
    String? disableCallTip, // 不可拨打时提示文案
    int? remaining, // 剩余次数
    int? callDuration, // 最大通话分钟数
    String? hangupCallVoice, // 挂断语音提示音频
    String? localHangupCallVoice, // 挂断语音提示音频 本地文件
    int? callSecondDuration, // app版本 2.1.0 开始支持，最大通话时长 单位：秒
    List<String>? segmentCodeList,
    List<int>? classIds,
    String? customState,
    GrayInfo? grayInfo,
    List<GrayInfo>? grayInfoList,
    EncouragementConversationParamsVo? encouragementConversationParamsVo,
  }) = _AiFrequency;

  factory AiFrequency.fromJson(Map<String, dynamic> json) =>
      _$AiFrequencyFromJson(json);
}

@unfreezed
class EncouragementConversationParamsVo with _$EncouragementConversationParamsVo {
  factory EncouragementConversationParamsVo({
    bool? canPopupAiCall,//完成里程碑奖励的最近时间
    String? encouragementConversationIcon,//AI学伴激励电话Icon
    String? encouragementConversationTitle,//AI学伴激励电话页面标题
    String? encouragementConversationSubTitle,//AI学伴激励电话页面副标题
    String? encouragementConversationBgm,//AI学伴激励电话背景音
    String? encouragementConversationVoice,//AI学伴激励电话呼叫语音
    String? encouragementConversationRouter,//AI学伴电话路由
    String? localEncouragementConversationBgm,
    String? localEncouragementConversationVoice,
    String? roomTaskId,
  }) = _EncouragementConversationParamsVo;

  factory EncouragementConversationParamsVo.fromJson(
          Map<String, dynamic> json) =>
      _$EncouragementConversationParamsVoFromJson(json);
}

