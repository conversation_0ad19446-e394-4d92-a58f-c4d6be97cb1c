
import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/adaptive_orientation_layout.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/utils/promote_finish_buried_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/eventbus/event_bus_location_classkey_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/animation/spine_animation_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/course_promote_finish_animation_dialog_btn.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class CoursePromoteFinishAnimationDialogMadelGetWidget extends StatefulWidget {
  final bool isAllLessonFirstGet;  // 是否是首次所有课程完课时获得
  final bool isFromClick;  // 是否来自点击操作
  final String? subjectColor;
  final PromoteLessonFinishModel? model;
  final LessonInProgressGiftModel? giftModel;
  final Function()? disMissCallBack;

  const CoursePromoteFinishAnimationDialogMadelGetWidget({
    Key? key,
    required this.model,
    required this.giftModel,
    required this.isFromClick,
    required this.subjectColor,
    required this.isAllLessonFirstGet,
    required this.disMissCallBack
  }) : super(key: key);

  @override
  CoursePromoteFinishAnimationDialogMadelGetWidgetState createState() => CoursePromoteFinishAnimationDialogMadelGetWidgetState();
}

class CoursePromoteFinishAnimationDialogMadelGetWidgetState extends State<CoursePromoteFinishAnimationDialogMadelGetWidget> {

  final _badgeGetSpineController = JoJoSpineAnimationController();
  final _gotSpineController = JoJoSpineAnimationController();
  String _badgeBgAnimationName = SpineAnimationConstants.badgeBgAnimation;
  bool _showBtnWidget = false;
  bool _isLandSpace = false;
  bool _animationInitialized = false; // 动画资源是否已经准备好
  int _currentRewardIndex = 0;  // 当前阶段需要展示的奖励索引
  StreamSubscription? _lifeCycleEventBus;
  StreamSubscription? _activityPageLifeCycleEventBus;
  PromoteLessonFinishSpineResourceInfo? _spineResourceInfo;
  AudioPlayer? _audioPlayer;  // 音频管理
  AudioPlayer? _audioPlayerDubbing;  // 配音音频管理
  AudioPlayer? _audioPlayerSoundEffects;  // 音效音频管理

  @override
  void initState() {
    super.initState();
    _spineResourceInfo = widget.model?.lessonInProgress?.spineResourceInfo;
    if (_spineResourceInfo == null) {
      // 数据不合要求，直接关闭弹窗
      widget.disMissCallBack?.call();
      return;
    }
    _listenLifeCycleEventBus();
    _showBtnWidget = !widget.isAllLessonFirstGet;
    // 播放奖章出现时的音效
    _playMedalDisplayAudio();
    // 浏览埋点
    buriedDataView();
  }

  @override
  void dispose() {
    _badgeGetSpineController.dispose();
    _gotSpineController.dispose();
    _lifeCycleEventBus?.cancel();
    _lifeCycleEventBus = null;
    _activityPageLifeCycleEventBus?.cancel();
    _activityPageLifeCycleEventBus = null;
    _disposeAudioPlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveOrientationLayout(
        portrait: (BuildContext context) {
          _isLandSpace = false;
          return _buildContentView();
        }, landscape: (BuildContext context) {
          _isLandSpace = true;
          return _buildContentView();
    });
  }

  // 浏览埋点
  void buriedDataView() {
    // 埋点
    int activityId = widget.model?.activityId ?? 0;
    int status = widget.model?.status ?? 0;
    try {
      LessonInProgressGiftRewardModel? rewardModel = widget.giftModel?.rewardList[_currentRewardIndex];
      JoJoPromoteFinishBuriedUtils.ActivityMedalGitScreen(activityId, status, widget.model?.lessonInfo, rewardModel?.type);
    } catch (e) {
      l.e("完课活动", "奖励发放页,浏览埋点异常: $e");
    }
  }

  Widget _buildContentView() {
    return Stack(
        children: [
          _buildBgWidget(),
          _buildBgAnimationWidget(),
          _buildMedalAnimationWidget(),
          _buildTitleWidget(),
          _buildBtnWidget(),
          _buildBackBtnWidget(),
        ]
    );
  }

  void _listenLifeCycleEventBus() {
    // 上课页首页的生命周期
    _lifeCycleEventBus = jojoEventBus.on<EventBusPromoteFinishLifecycleData>().listen((event) async {
      // 页面不可见时干掉音频逻辑
      if (event.lifecycleType == LifecycleType.paused) {
        _disposeAudioPlayer();
      }
    });
    // 落地页的生命周期
    _activityPageLifeCycleEventBus = jojoEventBus.on<EventBusActivityDetailLifecycleData>().listen((event) async {
      if (event.lifecycleType == LifecycleType.paused) {
        // 页面不可见时干掉音频逻辑
        _disposeAudioPlayer();
      }
    });
  }

  void _playAnimation() {
    LessonInProgressGiftRewardModel? rewardModel = widget.giftModel?.rewardList[_currentRewardIndex];
    _gotSpineController.playAnimation(JoJoSpineAnimation(
        animaitonName: rewardModel?.animationName ?? SpineAnimationConstants.badgeInfoAnimationFull,
        trackIndex: 0,
        loop: false,
        delay: 0, listener: null));
  }

  void _playAnimationAndAudio() {
    setState(() {
      bool hasAnimation = _checkRewardAnimationResource(true);
      if (hasAnimation) {
        // 播放动画
        _playAnimation();
        // 播放音效
        _playMedalDisplayAudio();
        // 浏览埋点
        buriedDataView();
      } else {
        widget.disMissCallBack?.call();
      }
    });
  }

  // 验证奖励中是否存在对应的动效资源
  bool _checkRewardAnimationResource(bool needUpdate) {
    List<LessonInProgressGiftRewardModel> rewardList = widget.giftModel?.rewardList ?? [];
    for (int i = _currentRewardIndex + 1; i < rewardList.length; i++) {
      LessonInProgressGiftRewardModel rewardModel = rewardList[i];
      String animationName = rewardModel.animationName ?? "";
      if (animationName.isNotEmpty) {
        if (_gotSpineController.spineController?.skeletonData.findAnimation(animationName) != null) {
          if (needUpdate) {
            _currentRewardIndex = i;
          }
          return true;
        }
      }
    }
    return false;
  }

  // 播放勋章normal音频
  void _playMedalNormalAudio() {
    _audioPlayer ??= AudioPlayer();
    _playMedalAudio(widget.giftModel?.spineResourceVo?.getFilePath(AudioResourceConstants.audioEventMedalSummerNormal), _audioPlayer);
  }

  // 播放背景音效
  void _playMedalDisplayAudio() {
    LessonInProgressGiftRewardModel? rewardModel = widget.giftModel?.rewardList[_currentRewardIndex];
    String audioDubbingName = rewardModel?.audioDubbingName ?? AudioResourceConstants.audioEventDubbingFull;
    String audioEventSoundEffectsFull = rewardModel?.audioSoundEffectsName ?? AudioResourceConstants.audioEventSoundEffectsFull;
    // 阶段奖励：播放配音
    String? audioDubbingPath = widget.giftModel?.spineResourceVo?.getFilePath(audioDubbingName);
    if ((audioDubbingPath ?? "").isNotEmpty) {
      _audioPlayerDubbing = AudioPlayer();
    }
    _playMedalAudio(audioDubbingPath ?? "", _audioPlayerDubbing);
    // 阶段奖励：播放音效
    String? audioSoundEffectsPath = widget.giftModel?.spineResourceVo?.getFilePath(audioEventSoundEffectsFull);
    if ((audioSoundEffectsPath ?? "").isEmpty) {
      audioSoundEffectsPath = widget.giftModel?.spineResourceVo?.getFilePath(AudioResourceConstants.audioEventSoundEffectsOldFull);
    }
    if ((audioSoundEffectsPath ?? "").isNotEmpty) {
      _audioPlayerSoundEffects = AudioPlayer();
    }
    _playMedalAudio(audioSoundEffectsPath ?? "", _audioPlayerSoundEffects);
    // 普通活动（普通活动上方的音频都会是空的）
    _audioPlayer ??= AudioPlayer();
    _playMedalAudio(widget.giftModel?.spineResourceVo?.getFilePath(AudioResourceConstants.audioEventMedalSummerFull), _audioPlayer);
  }

  // 播放奖章相关音频
  void _playMedalAudio(String? audioFilePath, AudioPlayer? audioPlayer) {
    if (_spineResourceInfo == null || audioFilePath == null) return;
    _createAudioPlayerAndPlay(audioFilePath, audioPlayer);
  }

  // 创建音频播放器
  Future<void> _createAudioPlayerAndPlay(String filePath, AudioPlayer? audioPlayer) async {
    if (filePath.isEmpty || audioPlayer == null) {
      return;
    }
    try {
      audioPlayer.audioCache.prefix = '';
      await audioPlayer.play(DeviceFileSource(filePath));
    } catch (e) {
      print(e);
    }
  }

  // 暂停并销毁音频播放器
  void _disposeAudioPlayer() {
    if (_audioPlayer != null) {
      _audioPlayer?.stop();
      _audioPlayer?.dispose();
      _audioPlayer = null;
    }
    if (_audioPlayerDubbing != null) {
      _audioPlayerDubbing?.stop();
      _audioPlayerDubbing?.dispose();
      _audioPlayerDubbing = null;
    }
    if (_audioPlayerSoundEffects != null) {
      _audioPlayerSoundEffects?.stop();
      _audioPlayerSoundEffects?.dispose();
      _audioPlayerSoundEffects = null;
    }
  }

  // 背景色（取科目色）
  Widget _buildBgWidget() {
    return Container(
      color: context.appColors.colorVariant1(HexColor(widget.subjectColor ?? "#FCF3BA")),
    );
  }

  // 背景动效
  Widget _buildBgAnimationWidget() {
    if (_spineResourceInfo == null) return Container();
    String? atlasFilePath = _spineResourceInfo?.badgeBgResource.getFilePath(SpineResourceConstants.SPINE_ATS);
    String? skelFilePath = _spineResourceInfo?.badgeBgResource.getFilePath(SpineResourceConstants.SPINE_SKL);
    bool isMultistage = widget.model?.lessonInProgress?.isMultistage ?? false;
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    double itemHeight = max(screenWidth, screenHeight);
    double scale = itemHeight/ConfigSize.bgSpineUIWidth;
    Widget child;
    if (!widget.isAllLessonFirstGet && isMultistage) {
      // 来自阶段奖励的背景动画直接播放Loop
      child = CourseSpineAnimationWidget(
        animationController: _badgeGetSpineController,
        atlasFilePath: atlasFilePath,
        skelFilePath: skelFilePath,
        animationName: SpineAnimationConstants.badgeBgAnimationLoop,
        playAnimation: true,
        scale: scale, loop: true, callBack: (type) {},);
    } else {
      // 完课之后的背景动画需要先播放Full，然后播放Loop
      child = CourseSpineAnimationWidget(
        animationController: _badgeGetSpineController,
        atlasFilePath: atlasFilePath,
        skelFilePath: skelFilePath,
        animationName: SpineAnimationConstants.badgeBgAnimation,
        playAnimation: true,
        scale: scale, loop: false, callBack: (type) {
        if (type == AnimationEventType.complete && _badgeBgAnimationName == SpineAnimationConstants.badgeBgAnimation) {
          _badgeBgAnimationName = SpineAnimationConstants.badgeBgAnimationLoop;
          _playMedalNormalAudio();
          _badgeGetSpineController.playAnimation(JoJoSpineAnimation(
              animaitonName: _badgeBgAnimationName,
              trackIndex: 0,
              loop: true,
              delay: 0));
        }
      },);
    }
    return Stack(
        children: [
          Positioned(
            top: -(itemHeight - screenHeight)/2.0 - ConfigSize.medalGetPageMedalCenterSpace,
            left: -(itemHeight - screenWidth)/2.0,
            child: SizedBox(
                width: itemHeight,
                height: itemHeight,
                child: child
            ),
          ),
        ]
    );
  }

  // 勋章动效
  Widget _buildMedalAnimationWidget() {
    if (_spineResourceInfo == null) return Container();
    PromoteLessonFinishSpineResourceVo? spineResourceVo;
    if (widget.model?.lessonInProgress?.isMultistage == true) {
      spineResourceVo = widget.giftModel?.spineResourceVo;
    } else {
      spineResourceVo = _spineResourceInfo?.targetResource;
    }
    String? atlasFilePath = spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_ATS);
    String? skelFilePath = spineResourceVo?.getFilePath(SpineResourceConstants.SPINE_SKL);
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    double itemHeight = max(screenWidth, screenHeight);
    double medalItemSpineHeight = _isLandSpace && !widget.isFromClick ? ConfigSize.medalItemSpineIPadUIHeight : ConfigSize.medalItemSpinePhoneUIHeight;
    double scale = medalItemSpineHeight/ConfigSize.medalSpineUIWidth;
    LessonInProgressGiftRewardModel? rewardModel = widget.giftModel?.rewardList[_currentRewardIndex];
    String animationName = rewardModel?.animationName ?? SpineAnimationConstants.badgeInfoAnimationFull;
    return Stack(
        children: [
          Positioned(
            top: -(itemHeight - screenHeight)/2.0 - ConfigSize.medalGetPageMedalCenterSpace,
            left: -(itemHeight - screenWidth)/2.0,
            child: SizedBox(
                width: itemHeight,
                height: itemHeight,
                child: CourseSpineAnimationWidget(
                  animationController: _gotSpineController,
                  atlasFilePath: atlasFilePath,
                  skelFilePath: skelFilePath,
                  playAnimation: true,
                  animationName: animationName,
                  scale: scale, loop: false, callBack: (type) {
                  if (type == AnimationEventType.complete && widget.isAllLessonFirstGet) {
                    setState(() {
                      _showBtnWidget = true;
                    });
                  }
                }, initializedCallBack: (animationDuration) {
                  _animationInitialized = true;
                  if (_gotSpineController.spineController?.skeletonData.findAnimation(animationName) == null && widget.isAllLessonFirstGet) {
                    l.i("促完课活动", "首次获得奖励发放页,动效资源中缺少$animationName动画");
                    setState(() {
                      _showBtnWidget = true;
                    });
                  }
                },)
            ),
          ),
        ]
    );
  }

  // 标题副标题
  Widget _buildTitleWidget() {
    double screenHeight = MediaQuery.of(context).size.height;
    double medalItemSpineHeight = _isLandSpace ? ConfigSize.medalItemSpineIPadUIHeight : ConfigSize.medalItemSpinePhoneUIHeight;
    LessonInProgressGiftRewardModel? rewardModel = widget.giftModel?.rewardList[_currentRewardIndex];
    return Positioned.fill(
        child: Stack(
          children: [
            Positioned(
              top: screenHeight/2.0 - ConfigSize.medalGetPageMedalCenterSpace + medalItemSpineHeight/2.0 + ConfigSize.medalItemSpineTitleSpace,
              left: 0.rdp,
              right: 0.rdp,
              child: Container(
                height: 70.rdp,
                margin: EdgeInsets.only(left: 20.rdp, right: 20.rdp),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      rewardModel?.title ?? "",
                      style: TextStyle(
                        fontSize: 23.rdp,
                        fontWeight: RunEnv.isAndroid ? FontWeight.w500 : FontWeight.w600,
                        color: context.appColors.colorVariant6(HexColor(widget.subjectColor ?? "#6B2D03")),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    Text(
                      rewardModel?.subTitle ?? "",
                      style: TextStyle(
                        fontSize: 17.rdp,
                        fontWeight: FontWeight.w400,
                        color: context.appColors.colorVariant6(HexColor(widget.subjectColor ?? "#6B2D03")),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
            )
          ],
        )
    );
  }

  // 按钮
  Widget _buildBtnWidget() {
    bool isLastNode = _currentRewardIndex == (widget.giftModel?.rewardList ?? []).length - 1 || (!_checkRewardAnimationResource(false) && _animationInitialized);
    return Visibility(
      visible: _showBtnWidget,
      child: Positioned.fill(
          child: Stack(
            children: [
              Positioned(
                bottom: ConfigSize.giftGitPageButtonBottomSpace,
                left: 0.rdp,
                right: 0.rdp,
                child: CoursePromoteFinishAnimationDialogBtnWidget(
                  medalId: widget.giftModel?.medalId,
                  isLandSpace: _isLandSpace,
                  isLastNode: isLastNode,
                  model: widget.model,
                  currentRewardModel: widget.giftModel?.rewardList[_currentRewardIndex],
                  isFirstGet: widget.isAllLessonFirstGet,
                  giftModel: widget.giftModel,
                  onclickCallBack: () {
                    _disposeAudioPlayer();
                    if (isLastNode) {
                      widget.disMissCallBack?.call();
                      _jumpToMyGiftPage();
                    } else {
                      _playAnimationAndAudio();
                    }
                  },
                  disMissCallBack: () {
                    widget.disMissCallBack?.call();
                  },),
              )
            ],
          )
      ),
    );
  }

  // 跳转到我的奖励页
  void _jumpToMyGiftPage() {
    int activityId = widget.model?.activityId ?? 0;
    int status = widget.model?.status ?? 0;
    Map<String, dynamic> properties = JoJoPromoteFinishBuriedUtils.getBuriedParams(activityId, status, widget.model?.lessonInfo);
    String buriedString = Uri.encodeComponent(jsonEncode(properties));
    int classId = widget.model?.lessonInfo?.classId ?? 0;
    int courseId = widget.model?.lessonInfo?.courseId ?? 0;
    int advanceRedirectPageId = widget.model?.lessonInProgress?.advanceRedirectPageId ?? 0;
    int activityRedirectPageId = widget.model?.lessonInProgress?.activityRedirectPageId ?? 0;
    int pageId = status == PromoteLessonFinishStatus.IN_PROGRESS? activityRedirectPageId : advanceRedirectPageId;
    RunEnv.jumpLink("tinman-router://cn.tinman.jojoread/flutter/plan/activityMyGiftPage?windowType=window&subjectColor=${Uri.encodeComponent(widget.model?.lessonInfo?.subjectColor ?? "#FF9045")}&activityId=$activityId&classId=$classId&pageId=$pageId&courseId=$courseId&buriedString=$buriedString");
  }

  Widget _buildBackBtnWidget() {
    bool showBackBtn = _currentRewardIndex == (widget.giftModel?.rewardList ?? []).length - 1 || (!_checkRewardAnimationResource(false) && _animationInitialized);
    return Positioned.fill(
        child: SafeArea(
          child: Visibility(
            visible: showBackBtn,
            child: Stack(
              children: [
                Positioned(
                  left: 10.rdp,
                  top: 0.rdp,
                  child: GestureDetector(
                      onTap: () {
                        widget.disMissCallBack?.call();
                      },
                      child: ImageAssetWeb(
                        assetName: AssetsImg.PLAN_IMAGE_PROMOTE_GUIDE_DIALOG_BACK_ICON,
                        width: 60.rdp,
                        height: 60.rdp,
                        package: Config.package,
                      )
                  ),
                )
              ],
            ),
          ),
        )
    );
  }
}
