import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/ab_test/ab_test_params_storage.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/models/graiy_info.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_container_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/model/baby_profile_gray_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/utils/sensor_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/dialogs/dialogs_message.dart';
import 'package:jojo_flutter_plan_pkg/service/baby_info_api.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/utils/baby_profile_utils.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class BabyProfileDialog extends BaseDialogWidget {
  BabyProfileState? state;

  BabyProfileDialog({
    super.key,
  }) : super(
          pagePath: DialogsMessage.babyProfileDialog.pagePath,
          dialogKey: DialogsMessage.babyProfileDialog.key,
          dialogSort: DialogsMessage.babyProfileDialog.sort,
          dialogType: DialogsMessage.babyProfileDialog.dialogType,
        );

  @override
  Future<bool> canShowDialog() async {
    bool phone = await jojoNativeBridge
            .getDeviceInfo()
            .then((value) => value.data?.grayLandscape != "1") &&
        !screen.isIpad;
    if (!phone) {
      // 不是手机 不会命中当前页面
      return false;
    }
    try {
      // 接口请求
      // 1. 宝贝资料未编辑完成
      // 2. 设备类型为手机
      // 3. 灰度命中新版本
      // 4. 用户有课程
      // 5. 当天未显示，当月显示的次数不超过两次
      final controller = BabyProfileController(
        BabyProfileState(pageStatus: PageStatus.loading),
        babyInfoService,
        babyInfoUcService,
        commonBabyInfoUcService,
      );

      // 并行获取数据以提高性能
      final futures = Future.wait([
        controller.getBabyProfileData(true),
        controller.getBabyProfileGrayData(),
        BabyProfileDialogStorage.monthBabyInfoCanShow(),
      ], eagerError: true);
      final results = await futures;
      state = results[0] as BabyProfileState?;
      final babyProfileGrayData = results[1] as BabyProfileGrayData?;
      final canShow = results[2] as bool;
      ABTestParamsStorage.setParams(
          ABTestParamsStorage.babyProfileDialog, null);
      GrayInfo? grayInfo = babyProfileGrayData?.babyProfileStyleGray;
      if (grayInfo != null) {
        ABTestParamsStorage.setParams(
            ABTestParamsStorage.babyProfileDialog, [grayInfo]);
      }
      l.d(babyProfileLogTag,
          "canShowDialog  ${state?.pageStatus} babyProfileGrayData $babyProfileGrayData  localCanShow $canShow isComplete ${babyInfoIsComplete(state?.babyInfo)} showBabyProfile ${babyProfileGrayData?.showBabyProfile}");
      return state?.pageStatus == PageStatus.success &&
          !babyInfoIsComplete(state?.babyInfo) &&
          babyProfileGrayData?.babyProfileStyle == 1 &&
          babyProfileGrayData?.showBabyProfile == true &&
          canShow &&
          phone;
    } catch (e, stack) {
      l.e(babyProfileLogTag, 'BabyProfileDialog canShowDialog error: $e',
          error: e, stackTrace: stack);
      return false;
    }
  }

  @override
  BaseDialogWidgetState<BaseDialogWidget> createState() {
    return _BabyProfileDialogState();
  }
}

class _BabyProfileDialogState extends BaseDialogWidgetState<BabyProfileDialog> with WidgetsBindingObserver{
  bool callDismissInBuild = false;
  BabyProfileState? _state;

  @override
  Widget build(BuildContext context) {
    if (_state == null) {
      _dismissDialogSafely();
      return const SizedBox.shrink();
    } else {
      return Stack(
        children: [
          BabyProfileContainerWidget(
            state: _state!,
            needCheckAge: true,
            needCheckGrade: true,
            saveBabyInfoCallback: (bool isSaveed, bool isComplete) {
              if (mounted) {
                widget.dismissDialog();
              }
            },
            pageName: '2025改版后学习页',
          ),
          Positioned(
              top: 44.rdp,
              right: 0,
              child: ClickWidget(
                type: ClickType.debounce,
                onTap: () {
                  if (mounted) {
                    BabyProfileSensorUtils.elementClick(_state?.babyInfo, "宝贝资料_点击关闭按钮", "2025改版后学习页");
                    widget.dismissDialog();
                  }
                },
                child: ImageAssetWeb(
                  assetName: AssetsImg.BABY_PROFILE_BABY_PROFILE_CLOSE,
                  package: Config.package,
                  width: 60.rdp,
                  height: 60.rdp,
                  fit: BoxFit.fitWidth,
                ),
              )),
        ],
      );
    }
  }

  void _dismissDialogSafely() {
    if (!callDismissInBuild && mounted) {
      callDismissInBuild = true;
      // 在帧渲染完成后执行，避免在build过程中修改widget状态
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          widget.dismissDialog();
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _state = widget.state;
    if (_state != null) {
      BabyProfileDialogStorage.updateMonthBabyInfoShowTimes();
    }
    jojoNativeBridge.showHomePageTabs(show: false);
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    jojoNativeBridge.showHomePageTabs(show: true);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    /// iOS 从二级页面返回 tabBar 会出来 在调用一次 隐藏
    if (state == AppLifecycleState.resumed && mounted && RunEnv.isIOS) {
      jojoNativeBridge.showHomePageTabs(show: false);
    }
  }
}
