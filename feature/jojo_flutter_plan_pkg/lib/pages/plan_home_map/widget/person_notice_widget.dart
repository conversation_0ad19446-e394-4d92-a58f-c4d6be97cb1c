import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_map_home_user_info_data.dart';

import 'package:jojo_flutter_plan_pkg/static/img.dart';

class PersonNoticeWidget extends StatefulWidget {
  const PersonNoticeWidget({super.key,required this.noticeUrl});
  final String noticeUrl;

  @override
  State<PersonNoticeWidget> createState() => _PersonNoticeWidgetState();
}

class _PersonNoticeWidgetState extends State<PersonNoticeWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    )..repeat();

    const deg2rad = pi / 180;
    const degree = 20.0;
    _animation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween(begin: 0.0, end: degree * deg2rad),
        weight: 100,
      ),
      TweenSequenceItem(
        tween: Tween(begin: degree * deg2rad, end: -degree * deg2rad),
        weight: 100,
      ),
      TweenSequenceItem(
        tween: Tween(begin: -degree * deg2rad, end: degree * deg2rad),
        weight: 100,
      ),
      TweenSequenceItem(
        tween: Tween(begin: degree * deg2rad, end: 0),
        weight: 100,
      ),
      TweenSequenceItem(
        tween: ConstantTween(0),
        weight: 800,
      ),
    ]).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.linear,
    ));
  }


  @override
  Widget build(BuildContext context) {

    final imageNetworkCached = ImageNetworkCached(
      imageUrl: widget.noticeUrl,
      width: 32.rdp,
      height: 32.rdp,
    );
    return Positioned(
      top: 20.rdp,
      right: 0,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            AssetsImg.PLAN_IMAGE_DRESSUP_MALL_NEW_ARRIVAL_BADGE,
            width: 58.rdp,
            height: 58.rdp,
            package: RunEnv.package,
          ),
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              final value = _animation.value;
              return Transform.rotate(
                angle: value,
                child: child,
              );
            },
            child: imageNetworkCached,
          ),
        ],
      ),
    );
  }
}
