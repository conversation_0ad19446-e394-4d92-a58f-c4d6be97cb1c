import 'package:collection/collection.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_auto_transform/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/growth_info_data.dart';
import 'package:jojo_flutter_plan_pkg/service/find_study_partner_api.dart';
import 'package:jojo_flutter_plan_pkg/service/learning_incentives_api.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/utils/deliverer_encrypt.dart';

import '../../../service/home_map_page_api.dart';
import '../../../service/personal_home_info_api.dart';
import '../../../utils/personal_snapshop.dart';
import '../model/course_map_home_page_tab_data.dart';
import 'model/personal_info_data.dart';
import 'model/subject_type_modules_info_data.dart';
import 'state.dart';

class PersonalHomePageCtrl extends Cubit<PersonalHomePageState> {
  PersonalHomeApi? personalHomeApi;
  HomeMapPageApi? homePageApi;
  FindStudyPartnerApi? findStudyPartnerApi;
  int? defaultSubjectType;
  int? partnerId;
  String? classKey;
  UserInfo? info;

  PersonalHomePageCtrl({
    PersonalHomeApi? apiPersonalHome,
    HomeMapPageApi? apiHome,
    FindStudyPartnerApi? apiPartner,
    int? subjectype,
    int? partnerid,
    String? classkey,
  }) : super(
          PersonalHomePageState(
            pageStatus: PageStatus.loading,
            periodDataList: [],
            periodLearningGrowthList: [],
          ),
        ) {
    defaultSubjectType = subjectype;
    personalHomeApi = apiPersonalHome ?? personalHomeApiService;
    homePageApi = apiHome ?? homeMapPageApiService;
    findStudyPartnerApi = apiPartner ?? proFindStudyPartnerApi;
    partnerId = partnerid;
    classKey = classkey;
    onRefresh();
    fetchUserInfo();
  }

  fetchUserInfo() async {
    info = await jojoNativeBridge.getUserInfo().then((value) => value.data);
  }

  onResume() async {
    if (state.personalInfo == null) return;
    //获取当前头像是否有本地缓存
    String? localPersonalImagePath = await checkRecentImage();
    //请求个人顶部信息
    PersonalInfo? personalInfo =
        await personalHomeApi?.requestPersonalHomeInfo(partnerId, classKey);
    final newState = state.copyWith()
      ..localPersonalImagePath = localPersonalImagePath
      ..personalInfo = personalInfo;
    emit(newState);
  }

  onRefresh() async {
    //请求接口
    try {
      //请求个人顶部信息
      PersonalInfo? personalInfo =
          await personalHomeApi?.requestPersonalHomeInfo(partnerId, classKey);
      //请求科目tab信息
      CourseSubjectTabData? courseSubjectTabData =
          await personalHomeApi?.getUserSubjectClass(partnerId);
      List<SubjectList>? hadSubjectList =
          getUserHadSubjects(courseSubjectTabData);
      //请求科目下的模块信息
      SubjectList? firstsubject =
          hadSubjectList?.isNotEmpty == true ? hadSubjectList?.first : null;
      int defaultRequestSubject =
          defaultSubjectType ?? firstsubject?.subjectType ?? 0;
      SubjectTypeModulesInfo? subjectTypeModulesInfo = await personalHomeApi
          ?.requestSubjectTypeModulesInfo(defaultRequestSubject, partnerId);
      List<SubjectTypeModulesInfo>? subjectTypeModulesInfoList = [];
      subjectTypeModulesInfoList
          .add(subjectTypeModulesInfo ?? const SubjectTypeModulesInfo());
      // 找到 subjectType 与 defaultRequestSubject 相同的 SubjectList 元素的索引
      int? defaultSubjectIndex = courseSubjectTabData?.subjectClassList
          ?.indexWhere(
              (subject) => subject.subjectType == defaultRequestSubject);
      //获取当前头像是否有本地缓存
      String? localPersonalImagePath = await checkRecentImage();
      SubjectList? subjectList = courseSubjectTabData?.subjectList?.firstWhere(
        (element) =>
            element.subjectType ==
            (defaultSubjectType ?? defaultRequestSubject),
      );

      onGrowthRetry(defaultRequestSubject, subjectTypeModulesInfo);

      final newState = state.copyWith()
        ..pageStatus = PageStatus.success
        ..personalInfo = personalInfo
        ..hadSubjectList = hadSubjectList
        ..courseSubjectTabData = courseSubjectTabData
        ..defaultSelectedTabIndex = defaultSubjectIndex
        ..defaultSelectedTabName = subjectList?.subjectName
        ..subjectTypeModulesInfoList = subjectTypeModulesInfoList
        ..localPersonalImagePath = localPersonalImagePath
        ..selectSubjectType = defaultRequestSubject;
      emit(newState);
    } catch (e) {
      //请求失败
      var exception = Exception("请求接口失败,$e");
      if (e is Exception) {
        exception = e;
      }
      final newState = state.copyWith()
        ..pageStatus = PageStatus.error
        ..exception = exception;
      emit(newState);
    }
  }

  /// tab科目切换
  requestTabsubjectInfoWithSubjectType(int subjectType) async {
    // 检查是否已经存在相同 subjectType 的 SubjectTypeModulesInfo
    // if (state.subjectTypeModulesInfoList
    //         ?.any((info) => info.subjectType == subjectType) ==
    //     true) {
    //   return; // 如果存在，则不请求
    // }

    SubjectTypeModulesInfo? subjectTypeModulesInfo = await personalHomeApi
        ?.requestSubjectTypeModulesInfo(subjectType, partnerId);

    // 成长曲线获取数据
    onGrowthRetry(subjectType, subjectTypeModulesInfo);

    List<SubjectTypeModulesInfo>? subjectTypeModulesInfoList =
        state.subjectTypeModulesInfoList ?? [];

    // 查找待插入元素的索引（通过唯一标识匹配）
    final index = subjectTypeModulesInfoList.indexWhere(
      (item) => item.subjectType == subjectTypeModulesInfo?.subjectType,
    );
    if (index != -1) {
      // 已存在：替换该位置的元素
      subjectTypeModulesInfoList[index] = subjectTypeModulesInfo!;
    } else {
      // 不存在：追加新元素
      subjectTypeModulesInfoList
          .add(subjectTypeModulesInfo ?? const SubjectTypeModulesInfo());
    }

    final newState = state.copyWith()
      ..subjectTypeModulesInfoList = subjectTypeModulesInfoList
      ..selectSubjectType = subjectType;
    emit(newState);
  }

  getUserHadSubjects(CourseSubjectTabData? courseSubjectTabData) {
    // 获取用户已拥有的科目tab信息
    List<SubjectList>? hasSubjectList = [];
    List<SubjectList>? subjectList =
        courseSubjectTabData?.subjectList; // 全部科目tab信息
    List<SubjectClassList>? subjectClassList =
        courseSubjectTabData?.subjectClassList; // 拥有的科目tab信息
    if (subjectList != null && subjectClassList != null) {
      Set<int?> subjectClassTypes = subjectClassList
          .map((subjectClass) => subjectClass.subjectType)
          .toSet();

      for (int? subjectType in subjectClassTypes) {
        for (SubjectList subject in subjectList) {
          if (subject.subjectType == subjectType) {
            hasSubjectList.add(subject);
            break;
          }
        }
      }
    }
    return hasSubjectList;
  }

  /// 根据接口modules type==growthData 找到数据growthDataClassId
  Module? getSubjectModule(
    SubjectTypeModulesInfo? subjectModulesInfo,
    int subjectType,
  ) {
    if (subjectType != 1 &&
        subjectType != 2 &&
        subjectType != 3 &&
        subjectType != 6) {
      return null;
    }
    // 目前就益智跟阅读需要展示成长曲线
    if (subjectModulesInfo != null &&
        subjectModulesInfo.modules?.isNotEmpty == true) {
      // 代表需要展示成长曲线
      Module? _module = subjectModulesInfo.modules!.firstWhereOrNull((k) =>
          k.type == 'growthData' &&
          k.growthData?.classId != null &&
          k.growthData?.courseSegmentCode != null);
      return _module;
    } else {
      return null;
    }
  }

  /// 成长曲线重试
  void onGrowthRetry(
    int subjectType,
    SubjectTypeModulesInfo? subjectTypeModulesInfo,
  ) async {
    try {
      Module? module = getSubjectModule(subjectTypeModulesInfo, subjectType);
      if (module != null) {
        final newState = state.copyWith()
          ..isLoading = true
          ..isRetry = false
          ..periodDataList = []
          ..periodLearningGrowthList = []
          ..curModule = module;
        emit(newState);
        await getGrowthReportStatisticKeyTemp(
          subjectType,
          module.growthData!.courseSegmentCode!,
        );
        await getClassSchedule(subjectType, module.growthData!.classId!);
        await getGrowth(subjectType, module);
      }
    } catch (e) {
      final newState = state.copyWith()
        ..isLoading = false
        ..isRetry = true;
      emit(newState);
    }
  }

  /// 获取阅读科目下分类信息
  Future<void> getGrowthReportStatisticKeyTemp(
    int selectSubjectType,
    int courseSegmentCode,
  ) async {
    try {
      // 如果指标有数据就不需要多次请求女娲接口
      if (state.statisticInfoList?.isNotEmpty == true) {
        final _getGrowthReportListData = getCumulativePerformanceData(
          selectSubjectType,
          courseSegmentCode,
          statisticInfoList: state.statisticInfoList,
        );
        StatisticInfoItem? _currStatisticInfoItem;
        if (_getGrowthReportListData?.isNotEmpty == true) {
          _currStatisticInfoItem = _getGrowthReportListData?[0];
        }
        final newState = state.copyWith()
          ..currStatisticInfoItem = _currStatisticInfoItem;
        emit(newState);
      } else {
        var deviceUniqueIdentifier =
            BaseConfig.share.deviceInfo?.deviceUniqueIdentifier ?? "";
        final encryptedStr = getDelivererEncrypt();
        var data = await personalHomeApi!.getSingleAdContent(
          'GrowthReportStatisticKey',
          encryptedStr,
          deviceUniqueIdentifier,
        );
        if (data['configValue'] != null) {
          IConfigValue configValue = IConfigValue.fromJson(data['configValue']);
          final _getGrowthReportListData = getCumulativePerformanceData(
            selectSubjectType,
            courseSegmentCode,
            statisticInfoList: configValue.statisticInfoList,
          );
          StatisticInfoItem? _currStatisticInfoItem;
          if (_getGrowthReportListData?.isNotEmpty == true) {
            _currStatisticInfoItem = _getGrowthReportListData?[0];
          }
          final newState = state.copyWith()
            ..statisticInfoList = configValue.statisticInfoList
            ..currStatisticInfoItem = _currStatisticInfoItem;
          emit(newState);
        }
      }
    } catch (e) {
      final newState = state.copyWith()
        ..isLoading = false
        ..isRetry = true;
      emit(newState);
      l.e('个人中心成长曲线', "获取接口失败:$e");
    }
  }

  /// 获取指标数据
  List<StatisticInfoItem>? getCumulativePerformanceData(
    int selectSubjectType,
    int courseSegmentCode, {
    bool isPerformance = false, // 益智提示数据需要的指标
    List<StatisticInfoItem>? statisticInfoList,
  }) {
    List<StatisticInfoItem>? _statisticInfoList =
        statisticInfoList ?? state.statisticInfoList;
    List<StatisticInfoItem>? subjectStatisticList = [];
    if (_statisticInfoList?.isNotEmpty == true) {
      if (isPerformance) {
        subjectStatisticList = _statisticInfoList?.where((element) {
          return element.statisticType == 5 &&
              element.courseSegmentCodeList!
                  .contains(courseSegmentCode.toString());
        }).toList();
      } else {
        subjectStatisticList = _statisticInfoList?.where((element) {
          return element.statisticType != 5 &&
              element.courseSegmentCodeList!
                  .contains(courseSegmentCode.toString());
        }).toList();
      }
    }
    return subjectStatisticList;
  }

  /// 获取科目时间
  /// growthDataClassId 成长曲线的课程id
  Future<void> getClassSchedule(
    int selectSubjectType,
    int growthDataClassId,
  ) async {
    try {
      final _viewType = selectSubjectType == 1 ? 1 : 0;
      final response = await personalHomeApi!
          .getScheduleViews(growthDataClassId, _viewType, partnerId);
      if (response.periodLearningGrowthList == null ||
          response.periodLearningGrowthList?.isEmpty == true) {
        final newState = state.copyWith()
          ..isGrowthEmpty = true
          ..periodLearningGrowthList = [];
        emit(newState);
        return;
      }
      var _currentTimeIndex = 0;
      if (response.periodLearningGrowthList?.isNotEmpty == true) {
        _currentTimeIndex = response.periodLearningGrowthList!.length - 1;
      }

      final newState = state.copyWith()
        ..periodLearningGrowthList = response.periodLearningGrowthList
        ..currentTimeIndex = _currentTimeIndex
        ..isGrowthEmpty = false;
      emit(newState);
    } catch (e) {
      final newState = state.copyWith()
        ..isLoading = false
        ..isRetry = true
        ..isGrowthEmpty = false
        ..periodLearningGrowthList = [];
      emit(newState);
      l.e('获取班级曲线时间报错', e);
    }
  }

  /// 获取曲线数据
  Future<void> getGrowthReportListData(
    int selectSubjectType,
    StatisticInfoItem? statisticInfo,
    int growthDataClassId, {
    bool isPuzzle = false, // 是否是益智
    int? currentTimeIndex,
  }) async {
    try {
      if (state.periodLearningGrowthList == null ||
          state.periodLearningGrowthList?.isEmpty == true ||
          statisticInfo == null) {
        final newState = state.copyWith()
          ..isLoading = false
          ..isRetry = state.isRetry
          ..periodDataList = [];
        emit(newState);
        return;
      }
      final _index = currentTimeIndex ?? state.currentTimeIndex;
      final _currentTime = state.periodLearningGrowthList?[_index ?? 0];
      final response = await personalHomeApi!.getGrowthReportData(
          growthDataClassId,
          statisticInfo.statisticKey ?? '',
          statisticInfo.statisticType ?? 0,
          _currentTime?.startTime ?? 0,
          _currentTime?.endTime ?? 0,
          partnerId);

      if (response.periodDataList == null ||
          response.periodDataList?.isEmpty == true) {
        final newState = state.copyWith()
          ..isGrowthEmpty = true
          ..periodDataList = []
          ..isLoading = false;
        emit(newState);
        return;
      }

      if (isPuzzle) {
        final newState = state.copyWith()
          ..isRetry = false
          ..puzzleTipsData = response.periodDataList?.isNotEmpty == true
              ? response.periodDataList
              : []
          ..isGrowthEmpty = false;
        emit(newState);
      } else {
        final newState = state.copyWith()
          ..periodDataList = response.periodDataList
          ..isLoading = false
          ..isRetry = false
          ..isGrowthEmpty = false;
        emit(newState);
      }
    } catch (e) {
      final newState = state.copyWith()
        ..isLoading = false
        ..isRetry = true
        ..periodDataList = [];
      emit(newState);
      l.e('成长曲线报错', e);
    }
  }

  /// 曲线配置请求
  getGrowth(
    int selectSubjectType,
    Module module, {
    int? currentTimeIndex,
  }) async {
    try {
      final isPuzzle = selectSubjectType == 1;
      if (isPuzzle) {
        final _getPuzzleData = getCumulativePerformanceData(
          selectSubjectType,
          module.growthData!.courseSegmentCode!,
          isPerformance: true,
        );
        if (_getPuzzleData?.isNotEmpty == true) {
          await getGrowthReportListData(
            selectSubjectType,
            _getPuzzleData![0],
            module.growthData!.classId!,
            isPuzzle: true,
            currentTimeIndex: currentTimeIndex,
          );
        }
      }
      if (state.currStatisticInfoItem == null) {
        final newState = state.copyWith()
          ..isLoading = false
          ..isGrowthEmpty = true;
        emit(newState);
      } else {
        await getGrowthReportListData(
          selectSubjectType,
          state.currStatisticInfoItem!,
          module.growthData!.classId!,
          currentTimeIndex: currentTimeIndex,
        );
      }
    } catch (e) {
      final newState = state.copyWith()
        ..isLoading = false
        ..isRetry = true;
      emit(newState);
      l.e('请求成长曲线', e);
    }
  }

  /// 设置当前时间
  setTimeIndex(int index) {
    if (state.isLoading == true) {
      return;
    }
    final newState = state.copyWith()..currentTimeIndex = index;
    getGrowth(
      state.selectSubjectType ?? 0,
      state.curModule!,
      currentTimeIndex: index,
    );
    emit(newState);
  }

  /// 阅读切换分类
  handleChangeType(StatisticInfoItem item) {
    if (state.isLoading == true) {
      return;
    }
    final newState = state.copyWith()..currStatisticInfoItem = item;
    emit(newState);
    getGrowthReportListData(
      state.selectSubjectType ?? 2,
      item,
      state.curModule!.growthData!.classId!,
    );
  }

  //点赞
  void postLikeRecord() async {
    if (partnerId == null) {
      return;
    }

    final personalInfo = state.personalInfo;
    final kudos = personalInfo?.like ?? 1;
    final canLike = personalInfo?.canLike ?? 0;
    final newKudos = canLike == 1 ? kudos + 1 : kudos - 1;
    final action = canLike == 1 ? '1' : '0';
    final newCanlike = canLike == 1 ? 0 : 1;
    final newPersonalInfo = personalInfo?.copyWith(
      like: newKudos,
      canLike: newCanlike,
    );

    final newState = state.copyWith()..personalInfo = newPersonalInfo;
    safeEmit(newState);
    await learningIncentivesApi.postLikeRecord(partnerId!, action);
  }

  void postUpNewRecord({bool needCallback = true}) async {
    final personalInfo = state.personalInfo;
    if (personalInfo?.newDressUp != 1) {
      return;
    }
    final newPersonalInfo = personalInfo?.copyWith(newDressUp: 0); //手动修改状态
    final newState = state.copyWith()..personalInfo = newPersonalInfo;
    safeEmit(newState);

    if (needCallback) {
      final useInfoReponse = await jojoNativeBridge.getUserInfo();
      final uid = useInfoReponse.data?.uid ?? '';
      personalHomeApi!.reportUpnew({
        "bizIdList": ["79"],
        "type": 79, //固定(79)
        "userId": uid,
        "classId": 0
      });
    }
  }

  void postRedDotCallBack(EntranceInfo info) async {
    if (info.callBackParam != null) {
      final useInfoReponse = await jojoNativeBridge.getUserInfo();
      final uid = useInfoReponse.data?.uid ?? '';
      final type = info.callBackParam;
      personalHomeApi?.reportUpnew({
        "bizIdList": [type.toString()],
        "type": type,
        "userId": uid,
        "classId": 0
      });
    }

    final personalInfo = state.personalInfo;
    if (personalInfo != null) {
      final updatedEntranceList = personalInfo.entranceList
          ?.map((e) => e.type == info.type ? e.copyWith(redPoint: 0) : e)
          .toList();

      final updatedPersonalInfo =
          personalInfo.copyWith(entranceList: updatedEntranceList);
      final newState = state.copyWith()..personalInfo = updatedPersonalInfo;
      safeEmit(newState);
    }
  }

  //获取点赞图标
  String getKudosIcon() {
    String src = AssetsImg.LEARNING_INCENTIVES_ICON_KOUDS; //默认是红花
    final canLike = state.personalInfo?.canLike;
    if (partnerId != null && canLike == 1) {
      src = AssetsImg.LEARNING_INCENTIVES_ICON_NO_KOUDS;
    }
    return src;
  }

  bool isVisitorAttitude() {
    return partnerId != null;
  }

  Future<void> addPartner() async {
    final newState = await _addPartner(partnerId);
    safeEmit(newState);
  }

  Future<PersonalHomePageState> _addPartner(int? partnerId) async {
    if (partnerId == null) {
      return state;
    }
    try {
      await findStudyPartnerApi!.addPartner(partnerId);
      PersonalInfo? personalInfo =
          state.personalInfo?.copyWith(partnerStatus: 2);
      final newState = state.copyWith()..personalInfo = personalInfo;
      return newState;
    } catch (e) {
      l.e('FindPartner', '添加学伴失败：${e.toString()}');
      // 返回当前状态，不做任何修改
      return state;
    }
  }

  int getRealNum(EntranceInfo info) {
    final lastNum = info.lastNum ?? 0;
    final currentNum = info.num ?? 0;
    if (isVisitorAttitude()) {
      return currentNum;
    }
    if (lastNum == currentNum) {
      return currentNum;
    }
    if (info.type != "flower") {
      return currentNum;
    }
    return lastNum > currentNum ? currentNum : lastNum;
  }

// 时间格式化方法
  String formatTimeDifference(
      int endTimeMillis, String hour, String min, String day) {
    // 获取当前时间
    DateTime now = DateTime.now();
    DateTime endTime = DateTime.fromMillisecondsSinceEpoch(endTimeMillis);
    // 计算时间差
    Duration difference = endTime.difference(now);

    // 如果时间差为负，说明结束时间已过去
    if (difference.isNegative) {
      return '';
    }

    // 计算天、小时、分钟
    int days = difference.inDays;
    int hours = difference.inHours % 24;
    int minutes = difference.inMinutes % 60;

    // 小于1小时，格式化为“xx分”
    if (difference.inHours < 1) {
      return '$minutes$min';
    }

    // 小于1天，格式化为“x时x分”
    if (days < 1) {
      return '$hours$hour$minutes$min';
    }

    // 大于或等于1天，格式化为“x天x时”
    return '$days$day${hours == 0 ? "" : "$hours$hour"}';
  }

  /// 排序队员
  List<TeamList?> sortTeamData(Module module) {
    if (module.teamList == null || module.teamList?.isEmpty == true) return [null, null, null];
    final teamList = module.teamList!;
    // 找到 isSelf == 1 的对象
    var selfIndex = teamList.indexWhere((element) => element.isSelf == 1);

    // 初始化结果数组，固定长度为3，初始值都为 null
    List<TeamList?> result = [null, null, null];

    // 如果没有找到 isSelf == 1 的对象，直接返回 [null, null, null]
    if (selfIndex == -1) {
      return result;
    }

    final selfMember = teamList[selfIndex];
    result[1] = selfMember;

    // 如果 teamList 长度大于1，尝试填充左边
    if (teamList.length > 1) {
      // 找到第一个非 isSelf == 1 的对象

      var otherMember =
          teamList.firstWhereOrNull((element) => element.isSelf != 1);
      if (otherMember != null) {
        result[0] = otherMember; // 放入左边
      }
      if (teamList.length > 2) {
        // 尝试填充右边
        otherMember =
            teamList.lastWhereOrNull((element) => element.isSelf != 1);
        if (otherMember != null) {
          result[2] = otherMember; // 放入右边
        }
      }
    }
    return result;
  }

  /// 是否需要显示队友
  double isNeedShowTeammate(Module module, TeamList? data) {
    var isSelf = data != null && data.isSelf == 1;

    if (isSelf) {
      return 1;
    }

    if (module.status == 0 || module.status == 3) {
      return 0;
    }

    return 1;
  }
}
