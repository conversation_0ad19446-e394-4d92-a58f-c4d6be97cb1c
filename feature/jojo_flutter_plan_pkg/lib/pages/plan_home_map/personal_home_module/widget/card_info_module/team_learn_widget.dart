import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/subject_type_modules_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/state.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/utils/widget_util.dart';

import '../../../../../static/svg.dart';

class TeamTitleWidget extends HookWidget {
  final Module module;

  const TeamTitleWidget({Key? key, required this.module}) : super(key: key);

  /// 未解锁
  Widget _buildLockState(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ContinuousDaysFire(
          continuousDays: module.days ?? 0,
          size: 18.rdp,
          hideBg: false,
        ),
        Text(
          S.of(context).me20DayVictory,
          style: context.textstyles.smallestText.pf.copyWith(
              color: context.appColors.jColorYellow5,
              fontWeight: FontWeight.w600),
        ),
        Text(
          " ${S.of(context).meSetSail}",
          style: context.textstyles.smallestText.pf.copyWith(
              color: context.appColors.jColorYellow5,
              fontWeight: FontWeight.w400),
        )
      ],
    );
  }

  Widget _buildStartedState(BuildContext context,
      {required String icon, required String text, String? hint}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgAssetWeb(
          assetName: icon,
          package: Config.package,
          width: 18.rdp,
          height: 18.rdp,
        ),
        Text(
          text,
          style: context.textstyles.smallestText.pf.copyWith(
              color: context.appColors.jColorYellow5,
              fontWeight: FontWeight.w600),
        ),
        Visibility(
            visible: hint != null,
            child: Text(
              hint ?? "",
              style: context.textstyles.smallestText.pf.copyWith(
                  color: context.appColors.jColorYellow5,
                  fontWeight: FontWeight.w400),
            ))
      ],
    );
  }

  Widget _buildSubTitle(BuildContext context) {
    final cubit = context.readOrNull<PersonalHomePageCtrl>();
    final status = module.status ?? 0;

    // 未解锁
    if (status == 0) {
      return _buildLockState(context);
    }

    // 活动结束 // 组队失败
    if (status == 3) {
      return _buildStartedState(context,
          icon: AssetsImg.PLAN_IMAGE_PLAN_BG_TEAM_LEARN,
          text: S.of(context).activityEnd);
    }

    final hour = S.of(context).hour;
    final min = S.of(context).min;
    final day = S.of(context).day;
    final timeStr = cubit?.formatTimeDifference(
        module.endTime ?? DateTime.now().millisecondsSinceEpoch,
        hour,
        min,
        day);

    // 组队中
    if (status == 1) {
      return _buildStartedState(context,
          icon: AssetsSvg.CONTINUOUS_LEARNING_TIME,
          text: timeStr ?? "",
          hint: " ${S.of(context).teamingEnds}${S.of(context).teaming}");
    }

    // 活动中/进行中
    return _buildStartedState(context,
        icon: AssetsSvg.CONTINUOUS_LEARNING_TIME,
        text: timeStr ?? "",
        hint: " ${S.of(context).teamingEnds}");
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ListTile(
        contentPadding: const EdgeInsets.only(left: 0),
        title: SizedBox(
          child: Text(
            module.name ?? "",
            style: context.textstyles.headingLargeEmphasis.pf.copyWith(
                color: context.appColors.jColorBlue6,
                fontWeight: FontWeight.w600),
          ),
        ),
        subtitle: SizedBox(
          child: _buildSubTitle(context),
        ),
      ),
    );
  }
}

/// 图标区域
class TeamLearnWidget extends HookWidget {
  final Module module;
  final PersonalHomePageState state;

  const TeamLearnWidget({Key? key, required this.module, required this.state})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final list =
        context.readOrNull<PersonalHomePageCtrl>()?.sortTeamData(module);
    return Transform.translate(
      offset: Offset(10.rdp, 0),
      child: Row(
        children: [
          _buildTeamMember(context, list![0]),
          Transform.translate(
            offset: Offset(29.rdp, 0),
            child: _buildTeamMember(context, list[2]),
          ),
          Transform.translate(
            offset: Offset(-47.rdp, 0),
            child: _buildTeamMember(context, list[1]),
          )
        ],
      ),
    );
  }

  Widget _buildTeamMember(BuildContext context, TeamList? data) {
    var isSelf = data != null && data.isSelf == 1;
    var contentWH = isSelf ? 49.rdp : 37.rdp;
    var wh = isSelf ? 48.rdp : 36.rdp;
    return Opacity(
      opacity: context
              .readOrNull<PersonalHomePageCtrl>()
              ?.isNeedShowTeammate(module, data) ??
          0,
      child: Container(
        width: contentWH,
        height: contentWH,
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
            color: context.appColors.jColorGray2,
            shape: BoxShape.circle,
            border: Border.all(
              color: context.appColors.jColorGray3,
              width: 1.rdp,
            )),
        child: data == null
            ? Center(
                child: SvgAssetWeb(
                  assetName: AssetsSvg.TEAM_ADD,
                  package: Config.package,
                  width: 14.rdp,
                  height: 14.rdp,
                ),
              )
            : ImageNetworkCached(
                width: wh,
                height: wh,
                imageUrl: data.photo ?? "",
                fit: BoxFit.contain,
                alignment: Alignment.center,
              ),
      ),
    );
  }
}
