import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/card_info_module/study_partner.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/card_info_module/team_learn_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/cognitive_growth.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/commcon_style/growth_container.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/error_retry_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/loading_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/reading_growth_container.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/learn_data_widget.dart';
import '../../../../common/host_env/host_env.dart';
import '../../../../static/img.dart';
import '../model/subject_type_modules_info_data.dart';
import '../state.dart';
import 'card_info_module/achievements_widget.dart';
import 'card_info_module/card_widget.dart';
import 'card_info_module/continuous_learn_widget.dart';
import 'card_info_module/douxingmijing_widget.dart';
import 'card_info_module/gallery_widget.dart';
import 'card_info_module/multi_learn_widget.dart';
import 'card_info_module/sticker_widget.dart';
import 'custom_module_container.dart';

class CustomTabbarViewWidget extends StatefulHookWidget {
  final PersonalHomePageState state;
  final TabController tabController;

  const CustomTabbarViewWidget(
      {Key? key, required this.state, required this.tabController})
      : super(key: key);

  @override
  CustomTabbarViewWidgetState createState() => CustomTabbarViewWidgetState();
}

class CustomTabbarViewWidgetState extends State<CustomTabbarViewWidget> {
  PersonalHomePageCtrl? get pageController =>
      mounted ? context.read<PersonalHomePageCtrl>() : null;

  bool isVisitorAttitude() {
    return pageController?.isVisitorAttitude() ?? false;
  }

  Color getCurrentSubjectColor(BuildContext context, String subjectColor) {
    final hexColor = HexColor(subjectColor);
    return context.appColors.colorVariant6(hexColor).withOpacity(0.7);
  }

  @override
  Widget build(BuildContext context) {
    int pageNum = widget.state.hadSubjectList?.length ?? 0;
    // var mockData = Module.mockData();
    return TabBarView(
        controller: widget.tabController,
        children: List.generate(pageNum, (indexTab) {
          List<SubjectTypeModulesInfo>? subjectTypeModulesInfoList =
              widget.state.subjectTypeModulesInfoList ?? [];
          final subjectList = widget.state.hadSubjectList?[indexTab];
          int subjectType = subjectList?.subjectType ?? 0;
          String subjectColor = subjectList?.subjectColor ?? "#976645";
          final learnTextColor = getCurrentSubjectColor(context, subjectColor);
          // 单独取出 subjetType 相等的 SubjectTypeModulesInfo
          SubjectTypeModulesInfo? subjectTypeModulesInfo =
              subjectTypeModulesInfoList.firstWhere(
                  (info) => info.subjectType == subjectType,
                  orElse: () => const SubjectTypeModulesInfo());
          List<Module> moduleList = subjectTypeModulesInfo.modules ?? [];

          Module? _statisticsModule = moduleList
              .firstWhereOrNull((module) => module.type == 'learningMetrics');

          // modules需要过滤掉成长曲线模块
          moduleList = moduleList
              .where((module) =>
                  module.type != 'growthData' &&
                  module.type != 'learningMetrics')
              .toList();
          int listLength = moduleList.length;
          bool isNotEmpty = false;
          Module? _module;

          if (subjectType == 1 ||
              subjectType == 2 ||
              subjectType == 6 ||
              subjectType == 3) {
            _module = context.read<PersonalHomePageCtrl>().getSubjectModule(
                  subjectTypeModulesInfo,
                  subjectType,
                );
            isNotEmpty = _module?.growthData?.classId != null;
          }

          return ListView(
              padding:
                  EdgeInsets.symmetric(vertical: 14.rdp, horizontal: 20.rdp),
              children: [
                ...List.generate(listLength, (index) {
                  Module module = moduleList[index];
                  String type = module.type ?? "";
                  if (type == "studyPartners") {
                    //学伴特殊处理
                    List<Partner> partners = module.partners ?? [];
                    return _buildSudyPartner(
                        partners, module, index, pageNum, context);
                  }
                  String backgroundImage = '';
                  if (type == "continuousLearn") {
                    backgroundImage = continuousCard[subjectType] ?? '';
                  } else {
                    backgroundImage = moduleCard[type] ?? '';
                  }
                  if (backgroundImage.isEmpty) {
                    backgroundImage = AssetsImg
                        .PERSONAL_HOME_MODULE_PERSONAL_CONTINUOUS_MODULE;
                  }

                  final isAchievements = type == "achievements";
                  final achievenNum = module.num ?? 0;
                  final achievenColor = HexColor(subjectColor);
                  return Column(
                    children: [
                      GestureDetector(
                          onTap: () {
                            _tapBeryPoint(module, widget.state,
                                isVisitorAttitude: isVisitorAttitude());
                          },
                          child: CustomModuleContainer(
                              backgroundImage: backgroundImage,
                              height: 82.rdp,
                              rightWidget:
                                  getModuleWidget(module, widget.state),
                              titleWidget: getModuleTitleWidget(module),
                              text: module.name ?? "",
                              isAchievements: isAchievements,
                              achievementNum: isAchievements ? achievenNum : 0,
                              achievementNumColor: achievenColor,
                              textColor: learnTextColor)),
                      Container(height: 10.rdp),
                    ],
                  );
                }),
                _GrowthAndStatisticsWidget(
                    subjectType, isNotEmpty, _module, _statisticsModule),
                SizedBox(
                    height: pageController?.isVisitorAttitude() == true
                        ? 84.rdp
                        : 12.rdp), // 底部间距
              ]);
        }));
  }

  Widget _GrowthAndStatisticsWidget(int subjectType, bool isNotEmpty,
      Module? growthModule, Module? statisticsModule) {
    if ((subjectType != widget.state.selectSubjectType ||
        widget.state.isLoading == true)) {
      return const LoadingWidget();
    }
    final haveGrowth = (subjectType == 1 ||
            subjectType == 2 ||
            subjectType == 6 ||
            subjectType == 3) &&
        isNotEmpty;
    if (statisticsModule == null && !haveGrowth) {
      return const SizedBox.shrink();
    }
    return Container(
        padding: EdgeInsets.symmetric(
          vertical: 20.rdp,
          horizontal: 14.rdp,
        ),
        decoration: BoxDecoration(
            color: Colors.white,
            border:
                Border.all(width: 1.rdp, color: context.appColors.jColorGray2),
            borderRadius: BorderRadius.all(Radius.circular(24.rdp)),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.03), blurRadius: 20.rdp),
            ]),
        child: Column(children: [
          if (haveGrowth)
            _buildGrowth(
              context: context,
              subjectType: subjectType,
              isNotEmpty: isNotEmpty,
              state: widget.state,
              module: growthModule,
            ),
          if (statisticsModule != null)
            LearnDataWidget(
              module: statisticsModule,
              showTitle: !haveGrowth,
            ), // 没有成长曲线才展示里面的‘成长记录’标题
        ]));
  }

  Widget _buildSudyPartner(List<Partner> partners, Module module, int index,
      int pageNum, BuildContext context) {
    if (partners.isEmpty) {
      return Container();
    }
    return VisibilityObserve(
        child: Column(
          children: [
            GestureDetector(
              onTap: () {
                _tapBeryPoint(module, widget.state,
                    isVisitorAttitude: isVisitorAttitude());
              },
              child: StudyPartner(
                partnerList: partners,
              ),
            ),
            SizedBox(
                height: index < (pageNum + 1)
                    ? 10.rdp
                    : MediaQuery.of(context).viewPadding.bottom),
          ],
        ),
        onShow: () {
          l.i("sensorsTrack", '个人主页_学伴入口曝光');
          RunEnv.sensorsTrack(
              '\$AppViewScreen', {"\$screen_name": "个人主页_学伴入口曝光"});
        });
  }
}

Widget _buildGrowth({
  required BuildContext context,
  required int subjectType,
  required bool isNotEmpty,
  required PersonalHomePageState state,
  Module? module,
  SubjectTypeModulesInfo? subjectTypeModulesInfo,
}) {
  if (isNotEmpty == false) return const SizedBox();
  return VisibilityObserve(
    onShow: () {
      RunEnv.sensorsTrack('\$AppViewScreen', {
        '\$screen_name': '成长曲线曝光',
        'custom_state': state.defaultSelectedTabName, // 科目列表页面曝光没有科目属性
      });
    },
    child: Column(
      children: [
        if (subjectType == 1 && state.isLoading == false)
          state.isRetry == true
              ? ErrorRetryWidget(
                  subjectTypeModulesInfo: subjectTypeModulesInfo,
                )
              : CognitiveGrowth(
                  state: state,
                ),
        if ((subjectType == 2 || subjectType == 3 || subjectType == 6) &&
            state.isLoading == false)
          state.isRetry == true
              ? ErrorRetryWidget(
                  subjectTypeModulesInfo: subjectTypeModulesInfo,
                )
              : Growth(
                  state: state,
                  module: module,
                ),
        // :ReadingGrowth(
        //     state: state,
        //     module: module,
        //   ),
      ],
    ),
  );
}

const moduleCard = {
  "continuousLearn":
      AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_CONTINUOUS_MODULE, //连续学
  "multiLearn":
      AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_MUTI_STUDY_MODULE_PNG, //多人学
  "achievements":
      AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_ACHIEVEMENT_MODULE, //成就
  "douxingmijing":
      AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_PUZZLE_MODULE_PNG, //豆星秘境
  "gallery": AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_GALLERY_MODULE_PNG, //画廊
  "sticker": AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_EXCLUSIVE_MODULE, //贴纸册
  "card": AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_EXCLUSIVE_MODULE, //卡册
  "teamLearn": AssetsImg.PLAN_IMAGE_PLAN_BG_TEAM_LEARN, // 多人学
};

const continuousCard = {
  1: AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_SIWEI_MODULE, //思维
  2: AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_CONTINUOUS_MODULE, //阅读
  3: AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_PUZZLE_MODULE_PNG, //小作家
  4: AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_GALLERY_MODULE_PNG, //美育
  6: AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_EXCLUSIVE_MODULE, //英语
  7: AssetsImg.PERSONAL_HOME_MODULE_PERSONAL_ZHUANTI_MODULE, //专题
};

final moduleTextColor = {
  "continuousLearn": HexColor("#976645"), //连续学
  "multiLearn": HexColor("#8D7100"), //多人学
  "achievements": HexColor("#947A54"), //成就
  "douxingmijing": HexColor("#40A12D"), //豆星秘境
  "gallery": HexColor("#9D68DD"), //画廊
  "sticker": HexColor("#F77896"), //贴纸册
  "card": HexColor("#F77896"), //卡册
};

getModuleTitleWidget(Module module){

  switch (module.type){
    case "teamLearn":
      return TeamTitleWidget(module: module);
    default:
      return null;
  }
}

getModuleWidget(Module module, PersonalHomePageState state) {
  Widget widget;
  switch (module.type) {
    case "continuousLearn":
      widget = ContinuousLearnWidget(
        module: module,
        state: state,
      );
      break;
    case "multiLearn":
      widget = MultiLearnWidget(
        module: module,
        state: state,
      );
      break;
    case "achievements":
      widget = AchievementsWidget(
        module: module,
        state: state,
      );
      break;
    case "douxingmijing":
      widget = DouxingmijingWidget(
        module: module,
        state: state,
      );
      break;
    case "gallery":
      widget = GalleryWidget(
        module: module,
        state: state,
      );
      break;
    case "sticker":
      widget = StickerWidget(
        module: module,
        state: state,
      );
      break;
    case "card":
      widget = CardWidget(
        module: module,
        state: state,
      );
      break;
    case "teamLearn": // 多人学
      widget = TeamLearnWidget(
        module: module,
        state: state,
      );
      break;
    default:
      widget = Container();
  }
  return widget;
}

_tapBeryPoint(Module module, PersonalHomePageState state,
    {bool isVisitorAttitude = false}) {
  switch (module.type) {
    case "continuousLearn":
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人详情板块点击_连续学",
        'material_id': state.defaultSelectedTabName
      });
      if (isVisitorAttitude == false) {
        RunEnv.jumpLink(module.jumpRoute ?? "");
      }
      break;
    case "multiLearn":
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人详情板块点击_多人学",
        'material_id': state.defaultSelectedTabName
      });
      RunEnv.jumpLink(module.jumpRoute ?? "");
      break;
    case "achievements":
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人详情板块点击_成就奖章",
        'material_id': state.defaultSelectedTabName
      });
      RunEnv.jumpLink(module.jumpRoute ?? "");
      break;
    case "douxingmijing":
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人详情板块点击_学科特有激励",
        'material_id': state.defaultSelectedTabName,
        'custom_state': '3-益智豆星'
      });
      print('${module.jumpRoute}');
      RunEnv.jumpLink(module.jumpRoute ?? "");
      break;
    case "gallery":
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人详情板块点击_学科特有激励",
        'material_id': state.defaultSelectedTabName,
        'custom_state': '4-美育画廊'
      });
      RunEnv.jumpLink(module.jumpRoute ?? "");
      break;
    case "sticker":
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人详情板块点击_学科特有激励",
        'material_id': state.defaultSelectedTabName,
        'custom_state': '1-英语贴纸'
      });
      RunEnv.jumpLink(module.jumpRoute ?? "");
      break;
    case "card":
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人详情板块点击_学科特有激励",
        'material_id': state.defaultSelectedTabName,
        'custom_state': '2-英语卡册'
      });
      RunEnv.jumpLink(module.jumpRoute ?? "");
      break;
    case "studyPartners": //学伴圈子
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人主页_学伴入口点击",
        'material_id': state.defaultSelectedTabName,
      });
      RunEnv.jumpLink(module.jumpRoute ?? "");
      break;
    case "teamLearn": // 多人学
      if(module.status == 3){
        JoJoToast.showText(S.current.activityEndToast);
      }else{
        RunEnv.jumpLink(module.jumpRoute ?? "");
      }
      break;
    default:
  }
}
