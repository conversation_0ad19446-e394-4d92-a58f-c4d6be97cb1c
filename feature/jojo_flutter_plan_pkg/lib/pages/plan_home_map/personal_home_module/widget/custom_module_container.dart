import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';

import '../../../../common/host_env/host_env.dart';

class CustomModuleContainer extends StatefulHookWidget {
  final double height;
  final String backgroundImage;
  final Color textColor;
  final String text;
  final Widget rightWidget;
  final Widget? titleWidget;
  final bool isAchievements;
  final int achievementNum;
  final Color achievementNumColor;

  const CustomModuleContainer({
    Key? key,
    required this.height,
    required this.backgroundImage,
    required this.textColor,
    required this.text,
    required this.rightWidget,
    this.titleWidget,
    this.isAchievements = false,
    this.achievementNum = 0,
    this.achievementNumColor = Colors.white
  }) : super(key: key);

  @override
  CustomModuleContainerState createState() => CustomModuleContainerState();
}

class CustomModuleContainerState extends State<CustomModuleContainer> {
  Widget _buildTitleWidget(BuildContext context) {
    if (widget.isAchievements) {
      return RichText(
          text: TextSpan(
        children: [
          TextSpan(
              text: S.of(context).lightUp,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 20.rdp,
                color: widget.textColor,
                overflow: TextOverflow.ellipsis,
              )),
          TextSpan(
              text: '${widget.achievementNum}',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 20.rdp,
                color: widget.achievementNumColor,
                overflow: TextOverflow.ellipsis,
              )),
          TextSpan(
              text: '${S.of(context).num}${S.of(context).achievement}',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 20.rdp,
                color: widget.textColor,
                overflow: TextOverflow.ellipsis,
              ))
        ],
      ));
    }
    return Text(
      widget.text,
      style: TextStyle(
        fontWeight: FontWeight.w600,
        fontSize: 20.rdp,
        color: widget.textColor,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      child: Stack(
        children: [
          Positioned.fill(
            child: ImageAssetWeb(
              height: widget.height,
              width: double.infinity,
              assetName: widget.backgroundImage,
              fit: BoxFit.fill,
              package: RunEnv.package,
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            child: Row(
              children: [
                Expanded(
                  flex: 6,
                  child: Container(
                    padding: EdgeInsets.only(left: 20.rdp),
                    height: widget.height,
                    alignment: Alignment.centerLeft,
                    child: widget.titleWidget ?? _buildTitleWidget(context),
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: widget.rightWidget,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
