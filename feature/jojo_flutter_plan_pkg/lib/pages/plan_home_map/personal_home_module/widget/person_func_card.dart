import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

class PersonFuncCard extends StatefulWidget {
  final BoxDecoration? decoration;
  final double width;
  final double height;
  final String? imageUrl;
  final String tipText;
  final String? guideText;
  final VoidCallback? onTap;
  final bool canShowDot;

  // 新的数据参数
  final int lastNum;
  final int currentNum;

  // 动画控制参数
  final bool enableNumberAnimation; // 是否启用数字切换动画，默认true
  final bool enableIconAnimation; // 是否启用Icon动画，默认true

  const PersonFuncCard({
    super.key,
    required this.width,
    required this.height,
    this.tipText = '',
    this.guideText,
    this.decoration,
    this.imageUrl,
    this.canShowDot = false,
    this.onTap,
    // 新参数
    required this.lastNum,
    required this.currentNum,
    this.enableNumberAnimation = true,
    this.enableIconAnimation = true,
  });

  @override
  State<PersonFuncCard> createState() => _PersonFuncCardState();
}

class _PersonFuncCardState extends State<PersonFuncCard>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _textController;

  late Animation<double> _bounceUpAnimation;
  late Animation<double> _bounceDownAnimation;
  late Animation<double> _textFadeInAnimation;
  late Animation<double> _textFadeOutAnimation;

  bool _isAnimating = false;
  bool _animationCompleted = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();

    // 初始化时检查是否需要触发动画
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndTriggerAnimation();
    });
  }

  @override
  void didUpdateWidget(PersonFuncCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当lastNum或currentNum发生变化时，检查是否需要触发动画
    if (oldWidget.lastNum != widget.lastNum ||
        oldWidget.currentNum != widget.currentNum) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _checkAndTriggerAnimation();
      });
    }
  }

  // 检查并触发动画的统一方法
  void _checkAndTriggerAnimation() {
    if (widget.enableNumberAnimation && widget.currentNum > widget.lastNum) {
      _triggerAnimation();
    }
  }

  // 计算显示的数值 - 修改这里的逻辑
  String get _displayText {
    // 如果正在动画且还未完成，显示lastNum
    if (_isAnimating && !_animationCompleted) {
      return widget.lastNum.toString();
    }
    // 否则显示currentNum
    return widget.currentNum.toString();
  }

  // 计算动画增量文本
  String get _animationText {
    if (widget.currentNum > widget.lastNum) {
      int increment = widget.currentNum - widget.lastNum;
      return '+$increment';
    }
    return '';
  }

  // 是否应该显示动画
  bool get _shouldShowAnimation {
    return widget.enableNumberAnimation && widget.currentNum > widget.lastNum;
  }

  void _initAnimations() {
    // 弹跳动画控制器
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1050),
      vsync: this,
    );

    // 文字动画控制器
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1600),
      vsync: this,
    );

    // 弹起动画：延迟600ms，Y+5，150ms，Ease in
    _bounceUpAnimation = Tween<double>(
      begin: 0.0,
      end: -5.0.rdp,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: const Interval(0.57, 0.71, curve: Curves.easeIn),
    ));

    // 落下动画：Y+5，300ms，Ease out back
    _bounceDownAnimation = Tween<double>(
      begin: -5.0.rdp,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: const Interval(0.71, 1.0, curve: Curves.easeOutBack),
    ));

    // 文字淡入：延迟300ms，透明度0→1，300ms，Linear
    _textFadeInAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.1875, 0.375, curve: Curves.linear),
    ));

    // 文字淡出：延迟1000ms，透明度1→0，Linear
    _textFadeOutAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.625, 1.0, curve: Curves.linear),
    ));

    // 监听动画状态
    _bounceController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
        });
      }
    });

    _textController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _animationCompleted = true;
          // 动画完成后，触发UI更新以显示currentNum
        });
      }
    });
  }

  // 触发动画的方法
  void _triggerAnimation() {
    if (!_isAnimating) {
      setState(() {
        _isAnimating = true;
        _animationCompleted = false;
      });

      // 重置并启动文字动画
      _textController.reset();
      _textController.forward();

      // 如果启用Icon动画，同时启动Icon动画
      if (widget.enableIconAnimation) {
        _bounceController.reset();
        _bounceController.forward();
      }
    }
  }

  Widget _buildRightWidget(BuildContext context) {
    if (widget.guideText != '') {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
              widget.tipText,
              style: TextStyle(
                overflow: TextOverflow.ellipsis,
                color: context.appColors.jColorGray5,
                fontSize: 14.rdp,
                height: 1.5,
                fontWeight: FontWeight.w400,
              ),
            ),
          const SizedBox(
            width: 6,
          ),
          Transform.translate(
            offset: Offset(0, -0.rdp),
            child: Row(
              children: [
                Text(
                  _displayText, // 这里会根据动画状态显示不同的数字
                  style: context.textstyles.bodyText.pf.copyWith(
                      fontSize: 16.rdp,
                      fontWeight: FontWeight.w500,
                      height: 1.5,
                      color: context.appColors.jColorGray5),
                ),
                // 只有启用数字动画且满足条件时才显示动画文字
                if (_shouldShowAnimation &&
                    _isAnimating &&
                    !_animationCompleted)
                  AnimatedBuilder(
                    animation: _textController,
                    builder: (context, child) {
                      double opacity = 0.0;
                      if (_textController.value <= 0.375) {
                        opacity = _textFadeInAnimation.value;
                      } else if (_textController.value >= 0.625) {
                        opacity = _textFadeOutAnimation.value;
                      } else {
                        opacity = 1.0;
                      }
                      return Opacity(
                        opacity: opacity,
                        child: Text(
                          _animationText,
                          style: context.textstyles.bodyText.pf.copyWith(
                            fontSize: 16.rdp,
                            fontWeight: FontWeight.w500,
                            height: 1.5,
                            color: context.appColors.jColorRed4,
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          )
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.tipText,
            style: context.textstyles.smallestText.pf
                .copyWith(color: context.appColors.jColorGray5, height: 1.5),
          ),
          Transform.translate(
            offset: Offset(0, -4.rdp),
            child: Row(
              children: [
                Text(
                  _displayText, // 这里会根据动画状态显示不同的数字
                  style: context.textstyles.bodyText.pf.copyWith(
                      fontSize: 16.rdp,
                      fontWeight: FontWeight.w500,
                      height: 1.5,
                      color: context.appColors.jColorGray6),
                ),
                // 只有启用数字动画且满足条件时才显示动画文字
                if (_shouldShowAnimation &&
                    _isAnimating &&
                    !_animationCompleted)
                  AnimatedBuilder(
                    animation: _textController,
                    builder: (context, child) {
                      double opacity = 0.0;
                      if (_textController.value <= 0.375) {
                        opacity = _textFadeInAnimation.value;
                      } else if (_textController.value >= 0.625) {
                        opacity = _textFadeOutAnimation.value;
                      } else {
                        opacity = 1.0;
                      }
                      return Opacity(
                        opacity: opacity,
                        child: Text(
                          _animationText,
                          style: context.textstyles.bodyText.pf.copyWith(
                            fontSize: 16.rdp,
                            fontWeight: FontWeight.w500,
                            height: 1.5,
                            color: context.appColors.jColorRed4,
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          )
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ClickWidget(
      type: ClickType.throttleWithTimeout,
      onTap: () {
        widget.onTap?.call();
      },
      child: Container(
        width: widget.width,
        height: widget.height,
        decoration: widget.decoration,
        padding: EdgeInsets.all(12.rdp),
        clipBehavior: Clip.none,
        alignment: Alignment.centerLeft,
        child: SizedBox(
          width: double.infinity,
          child: Stack(clipBehavior: Clip.none, children: [
            Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                // Icon动画（如果启用）
                widget.enableIconAnimation
                    ? AnimatedBuilder(
                        animation: Listenable.merge([_bounceController]),
                        builder: (context, child) {
                          double offsetY = 0.0;
                          if (_bounceController.value <= 0.71) {
                            offsetY = _bounceUpAnimation.value;
                          } else {
                            offsetY = _bounceDownAnimation.value;
                          }
                          return Transform.translate(
                            offset: Offset(0, offsetY),
                            child: ImageNetworkCached(
                              imageUrl: widget.imageUrl ?? '',
                              width: 32.rdp,
                              height: 32.rdp,
                            ),
                          );
                        },
                      )
                    : ImageNetworkCached(
                        imageUrl: widget.imageUrl ?? '',
                        width: 32.rdp,
                        height: 32.rdp,
                      ),
                Positioned(left: 40.rdp, child: _buildRightWidget(context)),
                if (widget.guideText != '') ...{
                  Positioned(
                    right: (0 - widget.height) - 212.rdp,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Opacity(
                          opacity: 0.7, // 设置透明度（0.0完全透明，1.0不透明）
                          child: Text(
                            widget.guideText ?? "",
                            maxLines: 2,
                            style: TextStyle(
                              overflow: TextOverflow.ellipsis,
                              color: context.appColors.jColorGray5,
                              fontSize: 14.rdp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        Opacity(
                          opacity: 0.7, // 设置透明度（0.0完全透明，1.0不透明）
                          child: ImageAssetWeb(
                            assetName: AssetsImg.PERSONAL_CARD_ARROW,
                            width: 14.rdp,
                            height: 14.rdp,
                            package: Config.package,
                          ),
                        )
                      ],
                    ),
                  )
                },
              ],
            ),
            if (widget.canShowDot)
              Positioned(
                right: -2.rdp,
                top: -6.rdp,
                child: Container(
                  height: 12.rdp,
                  width: 12.rdp,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                  child: Center(
                    child: Container(
                      height: 8.rdp,
                      width: 8.rdp,
                      decoration: BoxDecoration(
                        color: context.appColors.jColorOrange4,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
              )
          ]),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _bounceController.dispose();
    _textController.dispose();
    super.dispose();
  }
}
