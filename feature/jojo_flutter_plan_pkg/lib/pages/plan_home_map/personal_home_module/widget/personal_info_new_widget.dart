import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/appbar_left.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_half.dart';
import 'package:jojo_flutter_base/widgets/dialog/base_dialog_module_helper.dart';
import 'package:jojo_flutter_base/widgets/parent_verification_dialog.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/utils/ext.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/personal_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/person_func_card.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_train_enjoyplan/utils/statu_bar_utils.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

import '../../../../static/img.dart';
import '../state.dart';

class PersonalInfoNewWidget extends StatefulWidget {
  final PersonalHomePageState state;
  const PersonalInfoNewWidget({super.key, required this.state});

  @override
  State<PersonalInfoNewWidget> createState() => _PersonalInfoNewWidgetState();
}

class _PersonalInfoNewWidgetState extends State<PersonalInfoNewWidget> {
  bool isIosIpadMiniWindow = false;
  String? localDressImg = '';
  final flower = "flower";
  final dress = "dress";
  final partner = "partner";

  bool isVisitorAttitude() {
    final isVisitorAttitude = pageController?.isVisitorAttitude() ?? false;
    return isVisitorAttitude;
  }

  //是否为国内用户
  bool isUserInLand() {
    return pageController?.info?.regionCode == "CN";
  }

  @override
  void initState() {
    if (RunEnv.isIOS && JoJoRouter.isWindow) {
      isIosIpadMiniWindow = true;
    }
    super.initState();
  }

  String getNikcName() {
    String nickname = widget.state.personalInfo?.nickname ?? '';
    return nickname;
  }

  void handleEntanceAction(EntranceInfo info) {
    entanceInfoReportTracking(info);
    if (info.redPoint == 1) {
      pageController?.postRedDotCallBack(info);
    }
    final isNewDressUp =
        (pageController?.state.personalInfo?.newDressUp ?? 0) != 0;
    if (info.type == "flower") {
      showFlowerDialog(info);
      return;
    }
    if (info.jumpRoute.isNullOrEmpty()) {
      return;
    }
    if (info.type == "dress" && isNewDressUp) {
      pageController?.postUpNewRecord(needCallback: false);
    }
    RunEnv.jumpLink(info.jumpRoute ?? '');
  }

  void entanceInfoReportTracking(EntranceInfo info) {
    if (info.type == "flower") {
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人主页_花花",
        'custommaterial_id_state': widget.state.defaultSelectedTabName,
      });
    }
    if (info.type == "dress") {
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人主页_ 衣柜",
        'custommaterial_id_state': widget.state.defaultSelectedTabName,
      });
    }
    if (info.type == "partner") {
      RunEnv.sensorsTrack('\$AppClick', {
        '\$element_name': "个人主页_学伴",
        'custommaterial_id_state': widget.state.defaultSelectedTabName,
      });
    }
  }

  void showFlowerDialog(EntranceInfo info) {
    ModuleHalfDialogData data = ModuleHalfDialogData(
        topImageWidget: ImageNetworkCached(
          imageUrl: info.icon ?? '',
          width: 70.rdp,
          height: 70.rdp,
        ),
        content: info.desc,
        sureText: S.of(context).konw,
        outImage: false,
        sureCallback: () {
          RunEnv.sensorsTrack('\$AppClick', {
            '\$element_name': "个人主页_花花提示弹窗_知道了",
            'custommaterial_id_state': widget.state.defaultSelectedTabName,
          });
          ModuleDialogHelper.getInstance().dismissWithTag();
        });
    ModuleDialogHelper.getInstance().getModuleHalfDialog(data).show();
  }

  Widget _buildListFuncCardList() {
    final entranceList = pageController?.state.personalInfo?.entranceList ?? [];
    return Container(
      width: 375.rdp,
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: entranceList.map((e) {
            return _buildFuncCard(e, entranceList.length);
          }).toList()),
    );
  }

  double _getCardWidth(int cardNum) {
    switch (cardNum) {
      case 1:
        return 335.rdp;
      case 2:
        return 163.rdp;
      default:
        return 104.rdp;
    }
  }

  Widget _buildFuncCard(EntranceInfo info, cardNum) {
    final isPartner = isVisitorAttitude();
    final lastNum = info.lastNum ?? 0;
    final num = info.num ?? 0;
    final addNum = num - lastNum;
    var cardWidth = _getCardWidth(cardNum);
    return PersonFuncCard(
      width: cardWidth,
      height: 66.rdp,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.rdp),
          border: Border.all(
              color: isPartner ? Colors.white : context.appColors.jColorGray2,
              width: 1.rdp),
          boxShadow: isPartner
              ? null
              : [
                  BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 20.rdp),
                ]),
      imageUrl: info.icon,
      tipText: info.name ?? "",
      lastNum: info.lastNum ?? 0,
      currentNum: info.num ?? 0,
      enableIconAnimation: !isPartner && info.type != dress,
      enableNumberAnimation: !isPartner && info.type != dress,
      guideText: cardNum == 1 ? info.jumpDesc : "", //只有一个卡片时，才展示这个引导文案
      canShowDot: info.redPoint == 1,
      onTap: () {
        if (isVisitorAttitude()) {
          return;
        }
        handleEntanceAction(info);
      },
    );
  }

  Widget _buildMsgBtn(BuildContext context) {
    final msgInfo = pageController?.state.personalInfo?.messageInfo;
    final msgCount = msgInfo?.unreadNum ?? 0;
    final jumpRoute = msgInfo?.jumpRoute ?? '';
    return GestureDetector(
        onTap: () {
          if (jumpRoute.isNotEmpty) {
            RunEnv.jumpLink(jumpRoute);
            RunEnv.sensorsTrack('\$AppClick', {
              '\$element_name': "个人主页_气泡",
              'custommaterial_id_state': widget.state.defaultSelectedTabName,
            });
          }
        },
        child: Container(
          height: 44.rdp,
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 18.rdp),
          decoration: BoxDecoration(
            color:
                msgCount > 0 ? context.appColors.jColorYellow4 : Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(25.rdp)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _getMessageIcon(msgCount),
              if (msgCount > 0)
                Padding(
                  padding: EdgeInsets.only(left: 8.rdp),
                  child: Text(
                    "${msgCount > 99 ? '99+' : msgCount}",
                    style: context.textstyles.bodyTextEmphasis.pf.copyWith(
                        fontSize: 16.rdp,
                        fontWeight: FontWeight.w500,
                        color: context.appColors.jColorGray6),
                  ),
                )
            ],
          ),
        ));
  }

  Widget _getMessageIcon(int msgCount) {
    
    if (msgCount > 0) {
      return ImageAssetWeb(
        assetName: AssetsImg.MESSAGE_REMINDER,
        width: 24.rdp,
        package: Config.package,
      );
    } else {
      return SvgAssetWeb(
        assetName: AssetsSvg.LEARNING_INCENTIVES_MSG_ICON,
        width: 24.rdp,
        height: 24.rdp,
        package: RunEnv.package,
      );
    }
  }

  Widget _buildUerInfoWidget() {
    String localPersonalImage = widget.state.localPersonalImagePath ?? '';
    String imgUrl = widget.state.personalInfo?.dressImg ?? '';
    String? imgPath = Uri.parse(imgUrl).path;
    String? localPath = Uri.parse(localDressImg ?? '').path;
    if (localPath != imgPath) {
      localDressImg = imgUrl;
    }
    final nikNameSize = (widget.state.personalInfo?.nickname?.length ?? 0) < 6
        ? 24.rdp
        : 20.rdp;
    return Stack(
      children: [
        Positioned(
          left: 20.rdp,
          top: 79.rdp,
          child: GestureDetector(
            onTap: () {
              if (isVisitorAttitude()) {
                return;
              }
              pageController?.postUpNewRecord();
              RunEnv.sensorsTrack('\$AppClick', {
                '\$element_name': "个人主页_编辑装扮按钮",
                'custommaterial_id_state': widget.state.defaultSelectedTabName,
              });
              RunEnv.jumpLink(
                  "tinman-router://cn.tinman.jojoread/flutter/space/mydress");
            },
            child: localPersonalImage.isNotEmpty && !isVisitorAttitude()
                ? Image.file(File(localPersonalImage),
                    fit: BoxFit.cover, height: 180.rdp,
                    errorBuilder: (context, error, stackTrace) {
                    return ImageNetworkCached(
                      imageUrl: localDressImg ?? '',
                      fit: BoxFit.cover,
                      height: 180.rdp,
                      width: 150.rdp,
                      hideDefaultHolder: true,
                      hideErrorHolder: true,
                    );
                  })
                : ImageNetworkCached(
                    imageUrl: localDressImg ?? '',
                    fit: BoxFit.contain,
                    height: 180.rdp,
                    width: 150.rdp,
                  ),
          ),
        ),
        Positioned(
          left: 183.rdp,
          top: 0.rdp,
          bottom: 0.rdp,
          child: GestureDetector(
            onTap: () {
              if (isIosIpadMiniWindow || isVisitorAttitude()) {
                return;
              }
              RunEnv.sensorsTrack('\$AppClick', {
                '\$element_name': "个人主页编辑昵称",
              });
              ParentVerificationDialog(
                success: () => {
                  showParentVerifyDialogInIosAuditMode(() {
                    var stringUrl =
                        "tinman-router://cn.tinman.jojoread/flutter/setting/babyInfoEdit?windowType=normal";
                    RunEnv.jumpLink(stringUrl);
                  }, ignoreRouterUrl: "")
                },
              ).show();
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(widget.state.personalInfo?.nickname ?? '',
                        style: context.textstyles.bodyTextEmphasis.pf.copyWith(
                            fontSize: nikNameSize,
                            fontWeight: FontWeight.w500,
                            height: 1.5)),
                    SizedBox(width: isIosIpadMiniWindow ? 0 : 6.rdp),
                    Visibility(
                        visible: !isIosIpadMiniWindow && !isVisitorAttitude(),
                        child: ImageAssetWeb(
                          width: 12.rdp,
                          height: 15.rdp,
                          package: Config.package,
                          assetName: AssetsImg
                              .PERSONAL_HOME_MODULE_INCENTIVE_PERSION_EDIT,
                        ))
                  ],
                ),
                SizedBox(
                  height: 4.rdp,
                ),
                Text(
                  S
                      .of(context)
                      .studyDays(widget.state.personalInfo?.studyDays ?? 0),
                  style: context.textstyles.bodyText.pf.copyWith(
                      color: context.appColors.jColorGray5,
                      fontSize: 14.rdp,
                      fontWeight: FontWeight.w400,
                      height: 1.5),
                )
              ],
            ),
          ),
        ),
        Positioned(bottom: 0.rdp, child: _buildListFuncCardList())
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 353.rdp,
      child: Stack(
        alignment: Alignment.topLeft,
        children: [
          ImageAssetWeb(
            assetName: AssetsImg.PERSONAL_HOME_MODULE_BG_PERSON_HEAD,
            height: 268.rdp,
            width: 375.rdp,
            package: RunEnv.package,
          ),
          JoJoAppBar(
            backgroundColor: Colors.transparent,
            actions: [
              if (!isVisitorAttitude() && isUserInLand())
                Container(
                  alignment: Alignment.center,
                  child: _buildMsgBtn(context),
                )
            ],
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(flex: 1, child: _buildUerInfoWidget()),
            ],
          )
        ],
      ),
    );
  }

  PersonalHomePageCtrl? get pageController =>
      mounted ? context.read<PersonalHomePageCtrl>() : null;
}
