import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/model/subject_type_modules_info_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/commcon_style/growth_chart.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/growth_date_bar.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/loading_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/reading_class_bar.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/service/subjct_theme_service.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class Growth extends StatefulWidget {
  final PersonalHomePageState state;
  final Module? module;

  SideTitles left_Y_Titles = SideTitles(); //左边Y轴标题
  List<String> bottom_X_Titles = []; //下边X轴标题

  List<int?> growthPoints = []; //曲线数据
  int currentIndex = 0;
  double roundBase = 0; //Y轴刻度
  String unit = ''; //单位
  double max_Y_value = 0; // Y 轴最大值
  double max_X_value = 0; // X 轴最大值

  Growth({
    Key? key,
    required this.state,
    this.module,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _GrowthLineChartState();
  }

  void buildData() {
    final chartData = state.periodDataList;
    growthPoints = chartData?.map((i) => i.periodPerformance).toList() ?? [];
    build_X_Titles();
    //计算Y轴最大显示数据
    max_Y_value = calculateNiceMax();
    //计算Y轴间隔
    roundBase = calculateNiceInterval(max_Y_value);
    max_X_value = chartData!.length.toDouble(); // 正确写法
    currentIndex = chartData.indexWhere((element) => element.current == true);
    unit = state.currStatisticInfoItem?.statisticUnit ?? "";
  }

  void build_X_Titles() {
    final chartData = state.periodDataList;
    // 新增差异化处理
    if (chartData == null || chartData.isEmpty) {
      bottom_X_Titles = [];
      return;
    }
   
    //阅读，英语是按照主题，1\n主题，2\n主题...需换行
    if (state.selectSubjectType == 6 || state.selectSubjectType == 2) {
      //获取X轴展示数据
      bottom_X_Titles =
          chartData?.map((i) => '${i.periodOrder}\n${i.periodName}').toList() ??
              [];
    } else if (state.selectSubjectType == 3) {
      //小作家的是按照月，1月，2月...不换行,1月和第一个需要展示年
      bottom_X_Titles = List.generate(chartData.length, (index) {
      final periodName = chartData[index].periodName ?? "";
      return convertToMonthText(periodName,index);
    });
    }
  }

  String convertToMonthText(String inputStr,int index) {
    if (inputStr.length < 4) return '';
    final monthStr = inputStr.substring(inputStr.length - 2);
    final yearStr = inputStr.substring(inputStr.length - 4,inputStr.length - 2);
    final int month = int.parse(monthStr);
    if (month == 1 || index == 0) {
      return "$month月\n$yearStr年";
    } else {
      return "$month月";
    }
  }

  /// 计算Y轴显示最大数据
  double calculateNiceMax() {
    final growthPoints = state.periodDataList
            ?.map((i) => i.periodPerformance) // 提取原始数据
            .whereType<int>() // 过滤非空且类型为int的值
            .toList() ??
        [];
    int maxGrowthPoint = growthPoints.isNotEmpty ? growthPoints.reduce(max) : 0;
    if (maxGrowthPoint <= 0) return 5;
    final exponent = (log(maxGrowthPoint) / ln10).floor(); // 取数量级
    final base = pow(10, exponent);
    final niceSteps = [1, 2, 5, 10];
    for (final step in niceSteps) {
      final candidate = base * step;
      if (candidate >= maxGrowthPoint) {
        return candidate.toDouble();
      }
    }
    // 如果没有匹配上，说明 maxValue 比 base*10 还大，那就直接返回 base * 10
    return (base * 10).toDouble();
  }

  /// 计算Y轴显示刻度
  double calculateNiceInterval(double maxValue, {int targetGridLineCount = 5}) {
    if (maxValue <= 0) return 1;
    final roughInterval = maxValue / targetGridLineCount;
    // 计算 10 的幂
    final exponent = (log(roughInterval) / ln10).floor();
    final base = pow(10, exponent);
    // 获取最接近 roughInterval 的“好看”的间隔
    final niceIntervals = [1, 2, 5, 10];
    double bestInterval = base.toDouble();
    for (var factor in niceIntervals) {
      final candidate = base * factor;
      if (roughInterval <= candidate) {
        bestInterval = candidate.toDouble();
        break;
      }
    }
    return bestInterval;
  }
}

class _GrowthLineChartState extends State<Growth> {
  int? selectedIndex; // 记录选中的点索引

  @override
  void initState() {
    super.initState();
    widget.buildData();
  }

  @override
  void didUpdateWidget(covariant Growth oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.growthPoints != oldWidget.growthPoints ||
        widget.currentIndex != oldWidget.currentIndex) {
      setState(() {
        widget.buildData();
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ctr = context.read<PersonalHomePageCtrl>();

    final _statisticInfoList = ctr.getCumulativePerformanceData(
      widget.state.selectSubjectType ?? 2,
      widget.module!.growthData!.courseSegmentCode!,
    );

    final SubjectThemeConfig themeConfig =
        SubjectThemeService.getThemeConfig(widget.state.selectSubjectType ?? 2);

    return Column(
      children: [
        GrowthDateBar(
          periodLearningGrowthList: widget.state.periodLearningGrowthList,
          currentTimeIndex: widget.state.currentTimeIndex,
        ),
        if (_statisticInfoList?.isNotEmpty == true &&
            _statisticInfoList!.length > 1)
          ReadingClassBar(
            statisticInfoList: _statisticInfoList, subjectName: widget.state.defaultSelectedTabName ?? "",
          ),
        widget.state.isLoading == true
            ? const LoadingWidget()
            : widget.state.periodDataList?.isEmpty == true
                ? SizedBox(
                    width: screen.screenWidth,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ImageAssetWeb(
                          assetName: AssetsImg.TASK_CENTER_EMPTY2,
                          width: 140.rdp,
                          height: 140.rdp,
                          fit: BoxFit.cover,
                          package: RunEnv.package,
                        ),
                        SizedBox(height: 5.rdp),
                        Text(
                          '暂时没有数据哦～',
                          style: TextStyle(
                            color: HexColor('#B2B2B2'),
                            fontSize: 16.rdp,
                          ),
                        )
                      ],
                    ),
                  )
                : GrowthLineChart(
                    bottom_X_Titles: widget.bottom_X_Titles,
                    growthPoints: widget.growthPoints,
                    max_X_value: widget.max_X_value,
                    max_Y_value: widget.max_Y_value,
                    roundBase: widget.roundBase,
                    unit: widget.unit,
                    currentIndex: widget.currentIndex,
                    themeConfig: themeConfig,
                  ),
      ],
    );
  }
}
