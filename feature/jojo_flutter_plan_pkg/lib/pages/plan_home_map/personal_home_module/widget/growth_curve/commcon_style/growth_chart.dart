import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/cognitive_growth_line.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/widget/growth_curve/custom_fl_dot_painter.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/service/subjct_theme_service.dart';

class GrowthLineChart extends StatefulWidget {
  double max_Y_value; // Y 轴最大值
  double max_X_value; // X 轴最大值
  List<String> bottom_X_Titles; //下边X轴标题
  List<int?> growthPoints; //曲线数据
  int currentIndex; //当前选中数据
  double roundBase; //Y轴刻度
  String unit; //单位
  SubjectThemeConfig themeConfig; //学科色
  GrowthLineChart({
    Key? key,
    required this.max_Y_value,
    required this.max_X_value,
    required this.bottom_X_Titles,
    required this.growthPoints,
    required this.currentIndex,
    required this.roundBase,
    required this.unit,
    required this.themeConfig,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _GrowthLineChartState();
  }
}

class _GrowthLineChartState extends State<GrowthLineChart> {
  int? selectedIndex; // 记录选中的点索引

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.currentIndex;
  }

  @override
  void didUpdateWidget(covariant GrowthLineChart oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Column(
          children: [
            Stack(
              children: [
                SizedBox(
                  width: 335.rdp,
                  height: 199.rdp,
                  child: LineChart(
                    key: const Key('2'),
                    swapAnimationDuration:
                        const Duration(milliseconds: 500), // 动画效果持续500毫秒
                    LineChartData(
                      minY: 0,
                      maxY: widget.max_Y_value,
                      minX: 0,
                      maxX: widget.max_X_value + 0.5, // 让X轴比最后一个点大一点，给箭头留空间
                      gridData: FlGridData(
                        show: true,
                        drawVerticalLine: false,
                        drawHorizontalLine: true,
                        horizontalInterval:
                            widget.roundBase, // 每 100 一个刻度（可根据数据调整）
                        getDrawingHorizontalLine: (value) => FlLine(
                          color: HexColor('#E0DFDF'),
                          strokeWidth: 1.rdp,
                          dashArray: [1, 1], // Y 轴横线虚线
                        ),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: Border(
                          left: BorderSide(
                            color: HexColor('#E3E3E3'),
                            width: 1.rdp,
                          ), // 只保留左侧
                          bottom: BorderSide(
                            color: HexColor('#E3E3E3'),
                            width: 1.rdp,
                          ), // 只保留底
                        ),
                      ),

                      lineBarsData: [
                        ..._getVerticalDashedLines(
                          widget.growthPoints,
                          selectedIndex,
                        ), // 添加所有拐点到 X 轴的虚线
                        LineChartBarData(
                          spots: widget.growthPoints
                              .asMap()
                              .entries
                              .map((e) => e.value != null
                                  ? FlSpot(
                                      e.key.toDouble(), e.value!.toDouble())
                                  : FlSpot.nullSpot)
                              .toList(),
                          isCurved: false,
                          color: HexColor(widget.themeConfig.colorLevel4),
                          barWidth: 4.rdp,
                          belowBarData: BarAreaData(
                            show: true,
                            gradient: LinearGradient(
                              colors: [
                                HexColor(widget.themeConfig.colorLevel2),
                                HexColor(widget.themeConfig.colorLevel2)
                                    .withOpacity(0.0), // 透明色，制造渐变效果
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                          ),
                          dotData: FlDotData(
                            show: true,
                            getDotPainter: (spot, percent, barData, index) {
                              // 获取拐点数据
                              if (index == selectedIndex) {
                                return ReadingCustomFlDotPainter(
                                  text:
                                      '${(spot.y).toInt()}${widget.unit}', color: widget.themeConfig.colorLevel4, // 这里可以动态传入不同文本
                                );
                              } else {
                                return FlDotCirclePainter(
                                  radius: 3.rdp, // 圆点大小
                                  color: Colors.white, // 圆点颜色
                                  strokeWidth: 1.rdp, // 圆点边框
                                  strokeColor: HexColor(widget.themeConfig.colorLevel4), // 圆点边框颜色
                                );
                              }
                            },
                          ),
                        ),
                      ],
                      titlesData: FlTitlesData(
                              bottomTitles: AxisTitles(
                                sideTitles: _bottomTitles(widget.bottom_X_Titles),
                              ),
                              leftTitles: AxisTitles(
                                sideTitles: _leftTitles(widget.roundBase),
                              ),
                              rightTitles: AxisTitles(),
                              topTitles: AxisTitles(),
                            ),
                      lineTouchData: LineTouchData(
                        enabled: true,
                        handleBuiltInTouches: false, // 禁用默认 tooltip
                        touchCallback:
                            (FlTouchEvent event, LineTouchResponse? response) {
                          if (event is FlTapUpEvent &&
                              response != null &&
                              response.lineBarSpots != null) {
                            final FlSpot tappedSpot =
                                response.lineBarSpots!.first;
                            setState(() {
                              selectedIndex = tappedSpot.x.toInt();
                            });
                          }
                        },
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 21.rdp, // 调整箭头位置
                  top: 0, // 贴近 Y 轴顶部
                  child: CustomPaint(
                    size: Size(5.rdp, 5.rdp),
                    painter: OpenArrowPainter(
                      xPosition: 20.rdp,
                      yPosition: 0,
                      arrowSize: 5.rdp,
                      direction: AxisDirection.up, // 指定箭头方向向上
                    ),
                  ),
                ),
                Positioned(
                  right: 0, // 调整箭头位置
                  bottom: 25.rdp, // 贴近 X 轴
                  child: CustomPaint(
                    size: Size(5.rdp, 5.rdp),
                    painter: OpenArrowPainter(
                      xPosition: 5.rdp,
                      yPosition: 0,
                      arrowSize: 5.rdp,
                      direction: AxisDirection.right, // 指定箭头方向向右
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.rdp),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 7.5.rdp,
                  height: 7.5.rdp,
                  decoration: BoxDecoration(
                    color: HexColor(widget.themeConfig.colorLevel4),
                    borderRadius: BorderRadius.all(Radius.circular(7.5.rdp)),
                  ),
                ),
                SizedBox(width: 5.rdp),
                Text(
                  '宝贝成长曲线',
                  style: TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 11.rdp,
                    color: HexColor('#ACB2BB'),
                    height: 1.5,
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.rdp),
          ],
        )
      ],
    );
  }

  /// 左侧 Y 轴标题
  SideTitles _leftTitles(roundBase) {
    return SideTitles(
      showTitles: true,
      interval: roundBase,
      reservedSize: 40.rdp,
      getTitlesWidget: (value, meta) {
        return Text(
          value.toInt().toString(),
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 10.rdp,
            fontWeight: FontWeight.w400,
            color: HexColor('#B2B2B2'),
          ),
        );
      },
    );
  }

  /// 生成所有拐点到 X
  List<LineChartBarData> _getVerticalDashedLines(
    List<int?> growthPoints,
    int? selectedIndex,
  ) {
    return growthPoints.asMap().entries.map((entry) {
      final int index = entry.key;
      final int? y = entry.value;
      return selectedIndex == index && y != null
          ? LineChartBarData(
              spots: [
                FlSpot(index.toDouble(), y.toDouble()), // 拐点起点
                FlSpot(index.toDouble(), 0), // X 轴终点
              ],
              isCurved: false,
              color:HexColor(widget.themeConfig.colorLevel4), // 颜色
              barWidth: 1, // 线宽
              isStrokeCapRound: false,
              dashArray: [5, 5], // 5px 实线 + 5px 空隙，形成虚线
              dotData: FlDotData(show: false), // 不显示点
              belowBarData: BarAreaData(show: false),
            )
          : LineChartBarData();
    }).toList();
  }

    /// 底部 X 轴标题
  SideTitles _bottomTitles(List<String> periodOrder) {
    return SideTitles(
      showTitles: true,
      interval: 1,
      getTitlesWidget: (value, meta) {
        final index = value.round();
        if (index >= 0 && index < periodOrder.length) {
          return Padding(
            padding: EdgeInsets.only(top: 2.rdp),
            child: Text(
              periodOrder[index],
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 10.rdp,
                fontWeight: FontWeight.w400,
                color: selectedIndex == index
                    ? HexColor(widget.themeConfig.colorLevel5)
                    : HexColor('#B2B2B2'),
              ),
            ),
          );
        }
        return const Text('');
      },
      reservedSize: 29.rdp,
    );
  }
}
