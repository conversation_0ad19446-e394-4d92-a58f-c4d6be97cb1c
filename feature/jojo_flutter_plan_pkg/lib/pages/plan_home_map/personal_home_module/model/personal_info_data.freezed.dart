// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'personal_info_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PersonalInfo _$PersonalInfoFromJson(Map<String, dynamic> json) {
  return _PersonalInfo.fromJson(json);
}

/// @nodoc
mixin _$PersonalInfo {
  String? get nickname => throw _privateConstructorUsedError; //昵称
  int? get onlyTrainingCamp =>
      throw _privateConstructorUsedError; //是否仅训练营(1是0否)
  int? get studyDays => throw _privateConstructorUsedError; //学习天数
  String? get dressImg => throw _privateConstructorUsedError; //装扮图
  int? get like => throw _privateConstructorUsedError; //点赞数
  String? get likeDescription => throw _privateConstructorUsedError; //点赞描述
  String? get dressUpIcon => throw _privateConstructorUsedError; //装扮图标
  int? get newDressUp => throw _privateConstructorUsedError; //是否新装扮(1是0否)
  String? get newDressUpIcon => throw _privateConstructorUsedError; //上新图标
  int get canLike => throw _privateConstructorUsedError; //是否可以点赞(1否0可)
  List<EntranceInfo>? get entranceList => throw _privateConstructorUsedError;
  MessageInfo? get messageInfo => throw _privateConstructorUsedError;
  int? get partnerStatus => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PersonalInfoCopyWith<PersonalInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PersonalInfoCopyWith<$Res> {
  factory $PersonalInfoCopyWith(
          PersonalInfo value, $Res Function(PersonalInfo) then) =
      _$PersonalInfoCopyWithImpl<$Res, PersonalInfo>;
  @useResult
  $Res call(
      {String? nickname,
      int? onlyTrainingCamp,
      int? studyDays,
      String? dressImg,
      int? like,
      String? likeDescription,
      String? dressUpIcon,
      int? newDressUp,
      String? newDressUpIcon,
      int canLike,
      List<EntranceInfo>? entranceList,
      MessageInfo? messageInfo,
      int? partnerStatus});

  $MessageInfoCopyWith<$Res>? get messageInfo;
}

/// @nodoc
class _$PersonalInfoCopyWithImpl<$Res, $Val extends PersonalInfo>
    implements $PersonalInfoCopyWith<$Res> {
  _$PersonalInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? onlyTrainingCamp = freezed,
    Object? studyDays = freezed,
    Object? dressImg = freezed,
    Object? like = freezed,
    Object? likeDescription = freezed,
    Object? dressUpIcon = freezed,
    Object? newDressUp = freezed,
    Object? newDressUpIcon = freezed,
    Object? canLike = null,
    Object? entranceList = freezed,
    Object? messageInfo = freezed,
    Object? partnerStatus = freezed,
  }) {
    return _then(_value.copyWith(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      onlyTrainingCamp: freezed == onlyTrainingCamp
          ? _value.onlyTrainingCamp
          : onlyTrainingCamp // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      dressImg: freezed == dressImg
          ? _value.dressImg
          : dressImg // ignore: cast_nullable_to_non_nullable
              as String?,
      like: freezed == like
          ? _value.like
          : like // ignore: cast_nullable_to_non_nullable
              as int?,
      likeDescription: freezed == likeDescription
          ? _value.likeDescription
          : likeDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      dressUpIcon: freezed == dressUpIcon
          ? _value.dressUpIcon
          : dressUpIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      newDressUp: freezed == newDressUp
          ? _value.newDressUp
          : newDressUp // ignore: cast_nullable_to_non_nullable
              as int?,
      newDressUpIcon: freezed == newDressUpIcon
          ? _value.newDressUpIcon
          : newDressUpIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      canLike: null == canLike
          ? _value.canLike
          : canLike // ignore: cast_nullable_to_non_nullable
              as int,
      entranceList: freezed == entranceList
          ? _value.entranceList
          : entranceList // ignore: cast_nullable_to_non_nullable
              as List<EntranceInfo>?,
      messageInfo: freezed == messageInfo
          ? _value.messageInfo
          : messageInfo // ignore: cast_nullable_to_non_nullable
              as MessageInfo?,
      partnerStatus: freezed == partnerStatus
          ? _value.partnerStatus
          : partnerStatus // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MessageInfoCopyWith<$Res>? get messageInfo {
    if (_value.messageInfo == null) {
      return null;
    }

    return $MessageInfoCopyWith<$Res>(_value.messageInfo!, (value) {
      return _then(_value.copyWith(messageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_PersonalInfoCopyWith<$Res>
    implements $PersonalInfoCopyWith<$Res> {
  factory _$$_PersonalInfoCopyWith(
          _$_PersonalInfo value, $Res Function(_$_PersonalInfo) then) =
      __$$_PersonalInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickname,
      int? onlyTrainingCamp,
      int? studyDays,
      String? dressImg,
      int? like,
      String? likeDescription,
      String? dressUpIcon,
      int? newDressUp,
      String? newDressUpIcon,
      int canLike,
      List<EntranceInfo>? entranceList,
      MessageInfo? messageInfo,
      int? partnerStatus});

  @override
  $MessageInfoCopyWith<$Res>? get messageInfo;
}

/// @nodoc
class __$$_PersonalInfoCopyWithImpl<$Res>
    extends _$PersonalInfoCopyWithImpl<$Res, _$_PersonalInfo>
    implements _$$_PersonalInfoCopyWith<$Res> {
  __$$_PersonalInfoCopyWithImpl(
      _$_PersonalInfo _value, $Res Function(_$_PersonalInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? onlyTrainingCamp = freezed,
    Object? studyDays = freezed,
    Object? dressImg = freezed,
    Object? like = freezed,
    Object? likeDescription = freezed,
    Object? dressUpIcon = freezed,
    Object? newDressUp = freezed,
    Object? newDressUpIcon = freezed,
    Object? canLike = null,
    Object? entranceList = freezed,
    Object? messageInfo = freezed,
    Object? partnerStatus = freezed,
  }) {
    return _then(_$_PersonalInfo(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      onlyTrainingCamp: freezed == onlyTrainingCamp
          ? _value.onlyTrainingCamp
          : onlyTrainingCamp // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      dressImg: freezed == dressImg
          ? _value.dressImg
          : dressImg // ignore: cast_nullable_to_non_nullable
              as String?,
      like: freezed == like
          ? _value.like
          : like // ignore: cast_nullable_to_non_nullable
              as int?,
      likeDescription: freezed == likeDescription
          ? _value.likeDescription
          : likeDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      dressUpIcon: freezed == dressUpIcon
          ? _value.dressUpIcon
          : dressUpIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      newDressUp: freezed == newDressUp
          ? _value.newDressUp
          : newDressUp // ignore: cast_nullable_to_non_nullable
              as int?,
      newDressUpIcon: freezed == newDressUpIcon
          ? _value.newDressUpIcon
          : newDressUpIcon // ignore: cast_nullable_to_non_nullable
              as String?,
      canLike: null == canLike
          ? _value.canLike
          : canLike // ignore: cast_nullable_to_non_nullable
              as int,
      entranceList: freezed == entranceList
          ? _value._entranceList
          : entranceList // ignore: cast_nullable_to_non_nullable
              as List<EntranceInfo>?,
      messageInfo: freezed == messageInfo
          ? _value.messageInfo
          : messageInfo // ignore: cast_nullable_to_non_nullable
              as MessageInfo?,
      partnerStatus: freezed == partnerStatus
          ? _value.partnerStatus
          : partnerStatus // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PersonalInfo implements _PersonalInfo {
  const _$_PersonalInfo(
      {this.nickname,
      this.onlyTrainingCamp,
      this.studyDays,
      this.dressImg,
      this.like = 1,
      this.likeDescription = '',
      this.dressUpIcon = '',
      this.newDressUp = 0,
      this.newDressUpIcon,
      this.canLike = 0,
      final List<EntranceInfo>? entranceList,
      this.messageInfo,
      this.partnerStatus = -1})
      : _entranceList = entranceList;

  factory _$_PersonalInfo.fromJson(Map<String, dynamic> json) =>
      _$$_PersonalInfoFromJson(json);

  @override
  final String? nickname;
//昵称
  @override
  final int? onlyTrainingCamp;
//是否仅训练营(1是0否)
  @override
  final int? studyDays;
//学习天数
  @override
  final String? dressImg;
//装扮图
  @override
  @JsonKey()
  final int? like;
//点赞数
  @override
  @JsonKey()
  final String? likeDescription;
//点赞描述
  @override
  @JsonKey()
  final String? dressUpIcon;
//装扮图标
  @override
  @JsonKey()
  final int? newDressUp;
//是否新装扮(1是0否)
  @override
  final String? newDressUpIcon;
//上新图标
  @override
  @JsonKey()
  final int canLike;
//是否可以点赞(1否0可)
  final List<EntranceInfo>? _entranceList;
//是否可以点赞(1否0可)
  @override
  List<EntranceInfo>? get entranceList {
    final value = _entranceList;
    if (value == null) return null;
    if (_entranceList is EqualUnmodifiableListView) return _entranceList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final MessageInfo? messageInfo;
  @override
  @JsonKey()
  final int? partnerStatus;

  @override
  String toString() {
    return 'PersonalInfo(nickname: $nickname, onlyTrainingCamp: $onlyTrainingCamp, studyDays: $studyDays, dressImg: $dressImg, like: $like, likeDescription: $likeDescription, dressUpIcon: $dressUpIcon, newDressUp: $newDressUp, newDressUpIcon: $newDressUpIcon, canLike: $canLike, entranceList: $entranceList, messageInfo: $messageInfo, partnerStatus: $partnerStatus)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PersonalInfo &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.onlyTrainingCamp, onlyTrainingCamp) ||
                other.onlyTrainingCamp == onlyTrainingCamp) &&
            (identical(other.studyDays, studyDays) ||
                other.studyDays == studyDays) &&
            (identical(other.dressImg, dressImg) ||
                other.dressImg == dressImg) &&
            (identical(other.like, like) || other.like == like) &&
            (identical(other.likeDescription, likeDescription) ||
                other.likeDescription == likeDescription) &&
            (identical(other.dressUpIcon, dressUpIcon) ||
                other.dressUpIcon == dressUpIcon) &&
            (identical(other.newDressUp, newDressUp) ||
                other.newDressUp == newDressUp) &&
            (identical(other.newDressUpIcon, newDressUpIcon) ||
                other.newDressUpIcon == newDressUpIcon) &&
            (identical(other.canLike, canLike) || other.canLike == canLike) &&
            const DeepCollectionEquality()
                .equals(other._entranceList, _entranceList) &&
            (identical(other.messageInfo, messageInfo) ||
                other.messageInfo == messageInfo) &&
            (identical(other.partnerStatus, partnerStatus) ||
                other.partnerStatus == partnerStatus));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      nickname,
      onlyTrainingCamp,
      studyDays,
      dressImg,
      like,
      likeDescription,
      dressUpIcon,
      newDressUp,
      newDressUpIcon,
      canLike,
      const DeepCollectionEquality().hash(_entranceList),
      messageInfo,
      partnerStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PersonalInfoCopyWith<_$_PersonalInfo> get copyWith =>
      __$$_PersonalInfoCopyWithImpl<_$_PersonalInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PersonalInfoToJson(
      this,
    );
  }
}

abstract class _PersonalInfo implements PersonalInfo {
  const factory _PersonalInfo(
      {final String? nickname,
      final int? onlyTrainingCamp,
      final int? studyDays,
      final String? dressImg,
      final int? like,
      final String? likeDescription,
      final String? dressUpIcon,
      final int? newDressUp,
      final String? newDressUpIcon,
      final int canLike,
      final List<EntranceInfo>? entranceList,
      final MessageInfo? messageInfo,
      final int? partnerStatus}) = _$_PersonalInfo;

  factory _PersonalInfo.fromJson(Map<String, dynamic> json) =
      _$_PersonalInfo.fromJson;

  @override
  String? get nickname;
  @override //昵称
  int? get onlyTrainingCamp;
  @override //是否仅训练营(1是0否)
  int? get studyDays;
  @override //学习天数
  String? get dressImg;
  @override //装扮图
  int? get like;
  @override //点赞数
  String? get likeDescription;
  @override //点赞描述
  String? get dressUpIcon;
  @override //装扮图标
  int? get newDressUp;
  @override //是否新装扮(1是0否)
  String? get newDressUpIcon;
  @override //上新图标
  int get canLike;
  @override //是否可以点赞(1否0可)
  List<EntranceInfo>? get entranceList;
  @override
  MessageInfo? get messageInfo;
  @override
  int? get partnerStatus;
  @override
  @JsonKey(ignore: true)
  _$$_PersonalInfoCopyWith<_$_PersonalInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

MessageInfo _$MessageInfoFromJson(Map<String, dynamic> json) {
  return _MessageInfo.fromJson(json);
}

/// @nodoc
mixin _$MessageInfo {
  int? get unreadNum => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MessageInfoCopyWith<MessageInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MessageInfoCopyWith<$Res> {
  factory $MessageInfoCopyWith(
          MessageInfo value, $Res Function(MessageInfo) then) =
      _$MessageInfoCopyWithImpl<$Res, MessageInfo>;
  @useResult
  $Res call({int? unreadNum, String? jumpRoute, String? icon});
}

/// @nodoc
class _$MessageInfoCopyWithImpl<$Res, $Val extends MessageInfo>
    implements $MessageInfoCopyWith<$Res> {
  _$MessageInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unreadNum = freezed,
    Object? jumpRoute = freezed,
    Object? icon = freezed,
  }) {
    return _then(_value.copyWith(
      unreadNum: freezed == unreadNum
          ? _value.unreadNum
          : unreadNum // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MessageInfoCopyWith<$Res>
    implements $MessageInfoCopyWith<$Res> {
  factory _$$_MessageInfoCopyWith(
          _$_MessageInfo value, $Res Function(_$_MessageInfo) then) =
      __$$_MessageInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? unreadNum, String? jumpRoute, String? icon});
}

/// @nodoc
class __$$_MessageInfoCopyWithImpl<$Res>
    extends _$MessageInfoCopyWithImpl<$Res, _$_MessageInfo>
    implements _$$_MessageInfoCopyWith<$Res> {
  __$$_MessageInfoCopyWithImpl(
      _$_MessageInfo _value, $Res Function(_$_MessageInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? unreadNum = freezed,
    Object? jumpRoute = freezed,
    Object? icon = freezed,
  }) {
    return _then(_$_MessageInfo(
      unreadNum: freezed == unreadNum
          ? _value.unreadNum
          : unreadNum // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MessageInfo implements _MessageInfo {
  const _$_MessageInfo(
      {this.unreadNum = 0, this.jumpRoute = "", this.icon = ""});

  factory _$_MessageInfo.fromJson(Map<String, dynamic> json) =>
      _$$_MessageInfoFromJson(json);

  @override
  @JsonKey()
  final int? unreadNum;
  @override
  @JsonKey()
  final String? jumpRoute;
  @override
  @JsonKey()
  final String? icon;

  @override
  String toString() {
    return 'MessageInfo(unreadNum: $unreadNum, jumpRoute: $jumpRoute, icon: $icon)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MessageInfo &&
            (identical(other.unreadNum, unreadNum) ||
                other.unreadNum == unreadNum) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, unreadNum, jumpRoute, icon);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MessageInfoCopyWith<_$_MessageInfo> get copyWith =>
      __$$_MessageInfoCopyWithImpl<_$_MessageInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MessageInfoToJson(
      this,
    );
  }
}

abstract class _MessageInfo implements MessageInfo {
  const factory _MessageInfo(
      {final int? unreadNum,
      final String? jumpRoute,
      final String? icon}) = _$_MessageInfo;

  factory _MessageInfo.fromJson(Map<String, dynamic> json) =
      _$_MessageInfo.fromJson;

  @override
  int? get unreadNum;
  @override
  String? get jumpRoute;
  @override
  String? get icon;
  @override
  @JsonKey(ignore: true)
  _$$_MessageInfoCopyWith<_$_MessageInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

EntranceInfo _$EntranceInfoFromJson(Map<String, dynamic> json) {
  return _EntranceInfo.fromJson(json);
}

/// @nodoc
mixin _$EntranceInfo {
  String? get type => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  int? get num => throw _privateConstructorUsedError;
  int? get lastNum => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;
  String? get jumpDesc => throw _privateConstructorUsedError;
  int? get redPoint => throw _privateConstructorUsedError;
  int? get callBackParam => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EntranceInfoCopyWith<EntranceInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EntranceInfoCopyWith<$Res> {
  factory $EntranceInfoCopyWith(
          EntranceInfo value, $Res Function(EntranceInfo) then) =
      _$EntranceInfoCopyWithImpl<$Res, EntranceInfo>;
  @useResult
  $Res call(
      {String? type,
      String? icon,
      String? name,
      String? desc,
      int? num,
      int? lastNum,
      String? jumpRoute,
      String? jumpDesc,
      int? redPoint,
      int? callBackParam});
}

/// @nodoc
class _$EntranceInfoCopyWithImpl<$Res, $Val extends EntranceInfo>
    implements $EntranceInfoCopyWith<$Res> {
  _$EntranceInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? icon = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? num = freezed,
    Object? lastNum = freezed,
    Object? jumpRoute = freezed,
    Object? jumpDesc = freezed,
    Object? redPoint = freezed,
    Object? callBackParam = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      num: freezed == num
          ? _value.num
          : num // ignore: cast_nullable_to_non_nullable
              as int?,
      lastNum: freezed == lastNum
          ? _value.lastNum
          : lastNum // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpDesc: freezed == jumpDesc
          ? _value.jumpDesc
          : jumpDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      redPoint: freezed == redPoint
          ? _value.redPoint
          : redPoint // ignore: cast_nullable_to_non_nullable
              as int?,
      callBackParam: freezed == callBackParam
          ? _value.callBackParam
          : callBackParam // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_EntranceInfoCopyWith<$Res>
    implements $EntranceInfoCopyWith<$Res> {
  factory _$$_EntranceInfoCopyWith(
          _$_EntranceInfo value, $Res Function(_$_EntranceInfo) then) =
      __$$_EntranceInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? type,
      String? icon,
      String? name,
      String? desc,
      int? num,
      int? lastNum,
      String? jumpRoute,
      String? jumpDesc,
      int? redPoint,
      int? callBackParam});
}

/// @nodoc
class __$$_EntranceInfoCopyWithImpl<$Res>
    extends _$EntranceInfoCopyWithImpl<$Res, _$_EntranceInfo>
    implements _$$_EntranceInfoCopyWith<$Res> {
  __$$_EntranceInfoCopyWithImpl(
      _$_EntranceInfo _value, $Res Function(_$_EntranceInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? icon = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? num = freezed,
    Object? lastNum = freezed,
    Object? jumpRoute = freezed,
    Object? jumpDesc = freezed,
    Object? redPoint = freezed,
    Object? callBackParam = freezed,
  }) {
    return _then(_$_EntranceInfo(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      num: freezed == num
          ? _value.num
          : num // ignore: cast_nullable_to_non_nullable
              as int?,
      lastNum: freezed == lastNum
          ? _value.lastNum
          : lastNum // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      jumpDesc: freezed == jumpDesc
          ? _value.jumpDesc
          : jumpDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      redPoint: freezed == redPoint
          ? _value.redPoint
          : redPoint // ignore: cast_nullable_to_non_nullable
              as int?,
      callBackParam: freezed == callBackParam
          ? _value.callBackParam
          : callBackParam // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_EntranceInfo implements _EntranceInfo {
  const _$_EntranceInfo(
      {this.type = "",
      this.icon = "",
      this.name = "",
      this.desc = "",
      this.num = 0,
      this.lastNum = 0,
      this.jumpRoute = "",
      this.jumpDesc = "",
      this.redPoint = 0,
      this.callBackParam = 0});

  factory _$_EntranceInfo.fromJson(Map<String, dynamic> json) =>
      _$$_EntranceInfoFromJson(json);

  @override
  @JsonKey()
  final String? type;
  @override
  @JsonKey()
  final String? icon;
  @override
  @JsonKey()
  final String? name;
  @override
  @JsonKey()
  final String? desc;
  @override
  @JsonKey()
  final int? num;
  @override
  @JsonKey()
  final int? lastNum;
  @override
  @JsonKey()
  final String? jumpRoute;
  @override
  @JsonKey()
  final String? jumpDesc;
  @override
  @JsonKey()
  final int? redPoint;
  @override
  @JsonKey()
  final int? callBackParam;

  @override
  String toString() {
    return 'EntranceInfo(type: $type, icon: $icon, name: $name, desc: $desc, num: $num, lastNum: $lastNum, jumpRoute: $jumpRoute, jumpDesc: $jumpDesc, redPoint: $redPoint, callBackParam: $callBackParam)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_EntranceInfo &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.num, num) || other.num == num) &&
            (identical(other.lastNum, lastNum) || other.lastNum == lastNum) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute) &&
            (identical(other.jumpDesc, jumpDesc) ||
                other.jumpDesc == jumpDesc) &&
            (identical(other.redPoint, redPoint) ||
                other.redPoint == redPoint) &&
            (identical(other.callBackParam, callBackParam) ||
                other.callBackParam == callBackParam));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, icon, name, desc, num,
      lastNum, jumpRoute, jumpDesc, redPoint, callBackParam);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_EntranceInfoCopyWith<_$_EntranceInfo> get copyWith =>
      __$$_EntranceInfoCopyWithImpl<_$_EntranceInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_EntranceInfoToJson(
      this,
    );
  }
}

abstract class _EntranceInfo implements EntranceInfo {
  const factory _EntranceInfo(
      {final String? type,
      final String? icon,
      final String? name,
      final String? desc,
      final int? num,
      final int? lastNum,
      final String? jumpRoute,
      final String? jumpDesc,
      final int? redPoint,
      final int? callBackParam}) = _$_EntranceInfo;

  factory _EntranceInfo.fromJson(Map<String, dynamic> json) =
      _$_EntranceInfo.fromJson;

  @override
  String? get type;
  @override
  String? get icon;
  @override
  String? get name;
  @override
  String? get desc;
  @override
  int? get num;
  @override
  int? get lastNum;
  @override
  String? get jumpRoute;
  @override
  String? get jumpDesc;
  @override
  int? get redPoint;
  @override
  int? get callBackParam;
  @override
  @JsonKey(ignore: true)
  _$$_EntranceInfoCopyWith<_$_EntranceInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
