import 'dart:ffi';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'subject_type_modules_info_data.freezed.dart';
part 'subject_type_modules_info_data.g.dart';
@freezed
class SubjectTypeModulesInfo with _$SubjectTypeModulesInfo {
  const factory SubjectTypeModulesInfo({
    int? subjectType,
    String? subjectName,
    List<Module>? modules
  }) = _SubjectTypeModulesInfo;

  factory SubjectTypeModulesInfo.fromJson(Map<String, dynamic> json) =>
      _$SubjectTypeModulesInfoFromJson(json);
}

@freezed
class Module with _$Module {
  const factory Module({
    String?
        type, //模块类型continuousLearn：连续学  multiLearn：多人学 achievements：成就  douxingmijing：豆星秘境  gallery：画廊  sticker：贴纸册  card：卡册  growthData：成长曲线模块 learningMetrics:学习数据
    String? name, //模块名称
    int? bestDays, //历史最佳
    String? icon, //连续学图标
    int? days, //连续学天数
    int? num, //勋章数
    String? jumpRoute, //跳转路由
    List<TeamList>? teamList, //多人小队头像
    List<Medel>? medals, //勋章列表
    List<Partner>? partners, //学伴小队
    String? img,
    GrowthData? growthData,
    List<LearnDataStatisticsData>? data,
    int? status, //状态(0未解锁,1组队中,2进行中,3已结束)
    String? statusDesc,
    int? startTime,
    int? endTime,
}) = _Module;

  factory Module.fromJson(Map<String, dynamic> json) => _$ModuleFromJson(json);

  static Module mockData(){

    final data = {
      "type":"learningMetrics",
      "name":"学习指标",
      "data":[
        // {
        //   "type": 1,
        //   "name": "最新答题正确率",
        //   "value": 98,
        //   "tagImg": "https://jojopublicfat.jojoread.com/edu/admin/teacher/811293423655878665.png"  // 较上次提升2%
        // },
        // {
        //   "type": 2,
        //   "name": "累计学习时长",
        //   "value": 2500,     // 单位：小时
        // },
        {
          "type": 3,
          "name": "最新完课等级",
          "value": 1,
          "tagImg": "https://jojopublicfat.jojoread.com/edu/admin/teacher/811293423655878665.png"
          
        },
        // {
        //   "type": 4,
        //   "name": "累计上传作品数",
        //   "value": 2500,     // 单位：小时
        // }
        ]
      
    };
  return _$ModuleFromJson(data);
  }
}

@freezed
class LearnDataStatisticsData with _$LearnDataStatisticsData {

  const factory LearnDataStatisticsData({
    int?    type, // 数据类型
    String? name, // 数据名称
    int? value, // 数值
    String? tagImg, // 连续学图标
  }) = _LearnDataStatisticsData;

  factory LearnDataStatisticsData.fromJson(Map<String, dynamic> json) => _$LearnDataStatisticsDataFromJson(json);
}



extension LearnDataStatisticsExtension on LearnDataStatisticsData {

  bool haveTagImg(){
    return tagImg != null && tagImg!.isNotEmpty;
  }

  int getValueHour(){
    if(value != null){
      return value! ~/ 60;
    }
    return 0;
  }

  int getResidueMinute(){
    if(value != null){
      return value! % 60;
    }
    return 0;
  }

  String getValueLevel(){
    if(value == 1){
      return 'S';
    }else if(value == 2){
      return 'A';
    }else if(value == 3){
      return 'B';
    }else if(value == 4){
      return 'C';
    }else if(value == 5){
      return 'D';
    }
    return '';
  }
}

@freezed
class Partner with _$Partner {
  const factory Partner({
    @Default("") String? nickName,
    @Default(0) int? medal,
    @Default(0) int? continuousDays,
    @Default(0) int? studyDays,
    @Default(1) int? like, //点赞数默认是 1
    @Default("") String? likeDescription,
    @Default("") String? img,
    @Default("") String? url
  }) = _Partner;
  factory Partner.fromJson(Map<String, dynamic> json) => _$PartnerFromJson(json);
}

@freezed
class Medel with _$Medel {
  const factory Medel({
    String? name,
    String? img,
  }) = _Medel;

  factory Medel.fromJson(Map<String, dynamic> json) => _$MedelFromJson(json);
}

@freezed
class TeamList with _$TeamList {
  const factory TeamList({
    String? photo,
    int? isSelf,
  }) = _TeamList;

  factory TeamList.fromJson(Map<String, dynamic> json) =>
      _$TeamListFromJson(json);
}

@freezed
class GrowthData with _$GrowthData {
  const factory GrowthData({
    int? classId,
    int? courseSegmentCode,
  }) = _GrowthData;

  factory GrowthData.fromJson(Map<String, dynamic> json) =>
      _$GrowthDataFromJson(json);
}
