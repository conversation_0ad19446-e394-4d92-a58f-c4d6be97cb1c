// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subject_type_modules_info_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

SubjectTypeModulesInfo _$SubjectTypeModulesInfoFromJson(
    Map<String, dynamic> json) {
  return _SubjectTypeModulesInfo.fromJson(json);
}

/// @nodoc
mixin _$SubjectTypeModulesInfo {
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  List<Module>? get modules => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubjectTypeModulesInfoCopyWith<SubjectTypeModulesInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubjectTypeModulesInfoCopyWith<$Res> {
  factory $SubjectTypeModulesInfoCopyWith(SubjectTypeModulesInfo value,
          $Res Function(SubjectTypeModulesInfo) then) =
      _$SubjectTypeModulesInfoCopyWithImpl<$Res, SubjectTypeModulesInfo>;
  @useResult
  $Res call({int? subjectType, String? subjectName, List<Module>? modules});
}

/// @nodoc
class _$SubjectTypeModulesInfoCopyWithImpl<$Res,
        $Val extends SubjectTypeModulesInfo>
    implements $SubjectTypeModulesInfoCopyWith<$Res> {
  _$SubjectTypeModulesInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? modules = freezed,
  }) {
    return _then(_value.copyWith(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      modules: freezed == modules
          ? _value.modules
          : modules // ignore: cast_nullable_to_non_nullable
              as List<Module>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_SubjectTypeModulesInfoCopyWith<$Res>
    implements $SubjectTypeModulesInfoCopyWith<$Res> {
  factory _$$_SubjectTypeModulesInfoCopyWith(_$_SubjectTypeModulesInfo value,
          $Res Function(_$_SubjectTypeModulesInfo) then) =
      __$$_SubjectTypeModulesInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? subjectType, String? subjectName, List<Module>? modules});
}

/// @nodoc
class __$$_SubjectTypeModulesInfoCopyWithImpl<$Res>
    extends _$SubjectTypeModulesInfoCopyWithImpl<$Res,
        _$_SubjectTypeModulesInfo>
    implements _$$_SubjectTypeModulesInfoCopyWith<$Res> {
  __$$_SubjectTypeModulesInfoCopyWithImpl(_$_SubjectTypeModulesInfo _value,
      $Res Function(_$_SubjectTypeModulesInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? modules = freezed,
  }) {
    return _then(_$_SubjectTypeModulesInfo(
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      modules: freezed == modules
          ? _value._modules
          : modules // ignore: cast_nullable_to_non_nullable
              as List<Module>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_SubjectTypeModulesInfo implements _SubjectTypeModulesInfo {
  const _$_SubjectTypeModulesInfo(
      {this.subjectType, this.subjectName, final List<Module>? modules})
      : _modules = modules;

  factory _$_SubjectTypeModulesInfo.fromJson(Map<String, dynamic> json) =>
      _$$_SubjectTypeModulesInfoFromJson(json);

  @override
  final int? subjectType;
  @override
  final String? subjectName;
  final List<Module>? _modules;
  @override
  List<Module>? get modules {
    final value = _modules;
    if (value == null) return null;
    if (_modules is EqualUnmodifiableListView) return _modules;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'SubjectTypeModulesInfo(subjectType: $subjectType, subjectName: $subjectName, modules: $modules)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_SubjectTypeModulesInfo &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            const DeepCollectionEquality().equals(other._modules, _modules));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subjectType, subjectName,
      const DeepCollectionEquality().hash(_modules));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_SubjectTypeModulesInfoCopyWith<_$_SubjectTypeModulesInfo> get copyWith =>
      __$$_SubjectTypeModulesInfoCopyWithImpl<_$_SubjectTypeModulesInfo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_SubjectTypeModulesInfoToJson(
      this,
    );
  }
}

abstract class _SubjectTypeModulesInfo implements SubjectTypeModulesInfo {
  const factory _SubjectTypeModulesInfo(
      {final int? subjectType,
      final String? subjectName,
      final List<Module>? modules}) = _$_SubjectTypeModulesInfo;

  factory _SubjectTypeModulesInfo.fromJson(Map<String, dynamic> json) =
      _$_SubjectTypeModulesInfo.fromJson;

  @override
  int? get subjectType;
  @override
  String? get subjectName;
  @override
  List<Module>? get modules;
  @override
  @JsonKey(ignore: true)
  _$$_SubjectTypeModulesInfoCopyWith<_$_SubjectTypeModulesInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

Module _$ModuleFromJson(Map<String, dynamic> json) {
  return _Module.fromJson(json);
}

/// @nodoc
mixin _$Module {
  String? get type =>
      throw _privateConstructorUsedError; //模块类型continuousLearn：连续学  multiLearn：多人学 achievements：成就  douxingmijing：豆星秘境  gallery：画廊  sticker：贴纸册  card：卡册  growthData：成长曲线模块 learningMetrics:学习数据
  String? get name => throw _privateConstructorUsedError; //模块名称
  int? get bestDays => throw _privateConstructorUsedError; //历史最佳
  String? get icon => throw _privateConstructorUsedError; //连续学图标
  int? get days => throw _privateConstructorUsedError; //连续学天数
  int? get num => throw _privateConstructorUsedError; //勋章数
  String? get jumpRoute => throw _privateConstructorUsedError; //跳转路由
  List<TeamList>? get teamList => throw _privateConstructorUsedError; //多人小队头像
  List<Medel>? get medals => throw _privateConstructorUsedError; //勋章列表
  List<Partner>? get partners => throw _privateConstructorUsedError; //学伴小队
  String? get img => throw _privateConstructorUsedError;
  GrowthData? get growthData => throw _privateConstructorUsedError;
  List<LearnDataStatisticsData>? get data => throw _privateConstructorUsedError;
  int? get status =>
      throw _privateConstructorUsedError; //状态(0未解锁,1组队中,2进行中,3已结束)
  String? get statusDesc => throw _privateConstructorUsedError;
  int? get startTime => throw _privateConstructorUsedError;
  int? get endTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ModuleCopyWith<Module> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ModuleCopyWith<$Res> {
  factory $ModuleCopyWith(Module value, $Res Function(Module) then) =
      _$ModuleCopyWithImpl<$Res, Module>;
  @useResult
  $Res call(
      {String? type,
      String? name,
      int? bestDays,
      String? icon,
      int? days,
      int? num,
      String? jumpRoute,
      List<TeamList>? teamList,
      List<Medel>? medals,
      List<Partner>? partners,
      String? img,
      GrowthData? growthData,
      List<LearnDataStatisticsData>? data,
      int? status,
      String? statusDesc,
      int? startTime,
      int? endTime});

  $GrowthDataCopyWith<$Res>? get growthData;
}

/// @nodoc
class _$ModuleCopyWithImpl<$Res, $Val extends Module>
    implements $ModuleCopyWith<$Res> {
  _$ModuleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? bestDays = freezed,
    Object? icon = freezed,
    Object? days = freezed,
    Object? num = freezed,
    Object? jumpRoute = freezed,
    Object? teamList = freezed,
    Object? medals = freezed,
    Object? partners = freezed,
    Object? img = freezed,
    Object? growthData = freezed,
    Object? data = freezed,
    Object? status = freezed,
    Object? statusDesc = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      bestDays: freezed == bestDays
          ? _value.bestDays
          : bestDays // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      days: freezed == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int?,
      num: freezed == num
          ? _value.num
          : num // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      teamList: freezed == teamList
          ? _value.teamList
          : teamList // ignore: cast_nullable_to_non_nullable
              as List<TeamList>?,
      medals: freezed == medals
          ? _value.medals
          : medals // ignore: cast_nullable_to_non_nullable
              as List<Medel>?,
      partners: freezed == partners
          ? _value.partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<Partner>?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      growthData: freezed == growthData
          ? _value.growthData
          : growthData // ignore: cast_nullable_to_non_nullable
              as GrowthData?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<LearnDataStatisticsData>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      statusDesc: freezed == statusDesc
          ? _value.statusDesc
          : statusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GrowthDataCopyWith<$Res>? get growthData {
    if (_value.growthData == null) {
      return null;
    }

    return $GrowthDataCopyWith<$Res>(_value.growthData!, (value) {
      return _then(_value.copyWith(growthData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ModuleCopyWith<$Res> implements $ModuleCopyWith<$Res> {
  factory _$$_ModuleCopyWith(_$_Module value, $Res Function(_$_Module) then) =
      __$$_ModuleCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? type,
      String? name,
      int? bestDays,
      String? icon,
      int? days,
      int? num,
      String? jumpRoute,
      List<TeamList>? teamList,
      List<Medel>? medals,
      List<Partner>? partners,
      String? img,
      GrowthData? growthData,
      List<LearnDataStatisticsData>? data,
      int? status,
      String? statusDesc,
      int? startTime,
      int? endTime});

  @override
  $GrowthDataCopyWith<$Res>? get growthData;
}

/// @nodoc
class __$$_ModuleCopyWithImpl<$Res>
    extends _$ModuleCopyWithImpl<$Res, _$_Module>
    implements _$$_ModuleCopyWith<$Res> {
  __$$_ModuleCopyWithImpl(_$_Module _value, $Res Function(_$_Module) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? bestDays = freezed,
    Object? icon = freezed,
    Object? days = freezed,
    Object? num = freezed,
    Object? jumpRoute = freezed,
    Object? teamList = freezed,
    Object? medals = freezed,
    Object? partners = freezed,
    Object? img = freezed,
    Object? growthData = freezed,
    Object? data = freezed,
    Object? status = freezed,
    Object? statusDesc = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
  }) {
    return _then(_$_Module(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      bestDays: freezed == bestDays
          ? _value.bestDays
          : bestDays // ignore: cast_nullable_to_non_nullable
              as int?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      days: freezed == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int?,
      num: freezed == num
          ? _value.num
          : num // ignore: cast_nullable_to_non_nullable
              as int?,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      teamList: freezed == teamList
          ? _value._teamList
          : teamList // ignore: cast_nullable_to_non_nullable
              as List<TeamList>?,
      medals: freezed == medals
          ? _value._medals
          : medals // ignore: cast_nullable_to_non_nullable
              as List<Medel>?,
      partners: freezed == partners
          ? _value._partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<Partner>?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      growthData: freezed == growthData
          ? _value.growthData
          : growthData // ignore: cast_nullable_to_non_nullable
              as GrowthData?,
      data: freezed == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<LearnDataStatisticsData>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      statusDesc: freezed == statusDesc
          ? _value.statusDesc
          : statusDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Module implements _Module {
  const _$_Module(
      {this.type,
      this.name,
      this.bestDays,
      this.icon,
      this.days,
      this.num,
      this.jumpRoute,
      final List<TeamList>? teamList,
      final List<Medel>? medals,
      final List<Partner>? partners,
      this.img,
      this.growthData,
      final List<LearnDataStatisticsData>? data,
      this.status,
      this.statusDesc,
      this.startTime,
      this.endTime})
      : _teamList = teamList,
        _medals = medals,
        _partners = partners,
        _data = data;

  factory _$_Module.fromJson(Map<String, dynamic> json) =>
      _$$_ModuleFromJson(json);

  @override
  final String? type;
//模块类型continuousLearn：连续学  multiLearn：多人学 achievements：成就  douxingmijing：豆星秘境  gallery：画廊  sticker：贴纸册  card：卡册  growthData：成长曲线模块 learningMetrics:学习数据
  @override
  final String? name;
//模块名称
  @override
  final int? bestDays;
//历史最佳
  @override
  final String? icon;
//连续学图标
  @override
  final int? days;
//连续学天数
  @override
  final int? num;
//勋章数
  @override
  final String? jumpRoute;
//跳转路由
  final List<TeamList>? _teamList;
//跳转路由
  @override
  List<TeamList>? get teamList {
    final value = _teamList;
    if (value == null) return null;
    if (_teamList is EqualUnmodifiableListView) return _teamList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//多人小队头像
  final List<Medel>? _medals;
//多人小队头像
  @override
  List<Medel>? get medals {
    final value = _medals;
    if (value == null) return null;
    if (_medals is EqualUnmodifiableListView) return _medals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//勋章列表
  final List<Partner>? _partners;
//勋章列表
  @override
  List<Partner>? get partners {
    final value = _partners;
    if (value == null) return null;
    if (_partners is EqualUnmodifiableListView) return _partners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

//学伴小队
  @override
  final String? img;
  @override
  final GrowthData? growthData;
  final List<LearnDataStatisticsData>? _data;
  @override
  List<LearnDataStatisticsData>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? status;
//状态(0未解锁,1组队中,2进行中,3已结束)
  @override
  final String? statusDesc;
  @override
  final int? startTime;
  @override
  final int? endTime;

  @override
  String toString() {
    return 'Module(type: $type, name: $name, bestDays: $bestDays, icon: $icon, days: $days, num: $num, jumpRoute: $jumpRoute, teamList: $teamList, medals: $medals, partners: $partners, img: $img, growthData: $growthData, data: $data, status: $status, statusDesc: $statusDesc, startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Module &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.bestDays, bestDays) ||
                other.bestDays == bestDays) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.days, days) || other.days == days) &&
            (identical(other.num, num) || other.num == num) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute) &&
            const DeepCollectionEquality().equals(other._teamList, _teamList) &&
            const DeepCollectionEquality().equals(other._medals, _medals) &&
            const DeepCollectionEquality().equals(other._partners, _partners) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.growthData, growthData) ||
                other.growthData == growthData) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.statusDesc, statusDesc) ||
                other.statusDesc == statusDesc) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      name,
      bestDays,
      icon,
      days,
      num,
      jumpRoute,
      const DeepCollectionEquality().hash(_teamList),
      const DeepCollectionEquality().hash(_medals),
      const DeepCollectionEquality().hash(_partners),
      img,
      growthData,
      const DeepCollectionEquality().hash(_data),
      status,
      statusDesc,
      startTime,
      endTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ModuleCopyWith<_$_Module> get copyWith =>
      __$$_ModuleCopyWithImpl<_$_Module>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ModuleToJson(
      this,
    );
  }
}

abstract class _Module implements Module {
  const factory _Module(
      {final String? type,
      final String? name,
      final int? bestDays,
      final String? icon,
      final int? days,
      final int? num,
      final String? jumpRoute,
      final List<TeamList>? teamList,
      final List<Medel>? medals,
      final List<Partner>? partners,
      final String? img,
      final GrowthData? growthData,
      final List<LearnDataStatisticsData>? data,
      final int? status,
      final String? statusDesc,
      final int? startTime,
      final int? endTime}) = _$_Module;

  factory _Module.fromJson(Map<String, dynamic> json) = _$_Module.fromJson;

  @override
  String? get type;
  @override //模块类型continuousLearn：连续学  multiLearn：多人学 achievements：成就  douxingmijing：豆星秘境  gallery：画廊  sticker：贴纸册  card：卡册  growthData：成长曲线模块 learningMetrics:学习数据
  String? get name;
  @override //模块名称
  int? get bestDays;
  @override //历史最佳
  String? get icon;
  @override //连续学图标
  int? get days;
  @override //连续学天数
  int? get num;
  @override //勋章数
  String? get jumpRoute;
  @override //跳转路由
  List<TeamList>? get teamList;
  @override //多人小队头像
  List<Medel>? get medals;
  @override //勋章列表
  List<Partner>? get partners;
  @override //学伴小队
  String? get img;
  @override
  GrowthData? get growthData;
  @override
  List<LearnDataStatisticsData>? get data;
  @override
  int? get status;
  @override //状态(0未解锁,1组队中,2进行中,3已结束)
  String? get statusDesc;
  @override
  int? get startTime;
  @override
  int? get endTime;
  @override
  @JsonKey(ignore: true)
  _$$_ModuleCopyWith<_$_Module> get copyWith =>
      throw _privateConstructorUsedError;
}

LearnDataStatisticsData _$LearnDataStatisticsDataFromJson(
    Map<String, dynamic> json) {
  return _LearnDataStatisticsData.fromJson(json);
}

/// @nodoc
mixin _$LearnDataStatisticsData {
  int? get type => throw _privateConstructorUsedError; // 数据类型
  String? get name => throw _privateConstructorUsedError; // 数据名称
  int? get value => throw _privateConstructorUsedError; // 数值
  String? get tagImg => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LearnDataStatisticsDataCopyWith<LearnDataStatisticsData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LearnDataStatisticsDataCopyWith<$Res> {
  factory $LearnDataStatisticsDataCopyWith(LearnDataStatisticsData value,
          $Res Function(LearnDataStatisticsData) then) =
      _$LearnDataStatisticsDataCopyWithImpl<$Res, LearnDataStatisticsData>;
  @useResult
  $Res call({int? type, String? name, int? value, String? tagImg});
}

/// @nodoc
class _$LearnDataStatisticsDataCopyWithImpl<$Res,
        $Val extends LearnDataStatisticsData>
    implements $LearnDataStatisticsDataCopyWith<$Res> {
  _$LearnDataStatisticsDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? value = freezed,
    Object? tagImg = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int?,
      tagImg: freezed == tagImg
          ? _value.tagImg
          : tagImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_LearnDataStatisticsDataCopyWith<$Res>
    implements $LearnDataStatisticsDataCopyWith<$Res> {
  factory _$$_LearnDataStatisticsDataCopyWith(_$_LearnDataStatisticsData value,
          $Res Function(_$_LearnDataStatisticsData) then) =
      __$$_LearnDataStatisticsDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? type, String? name, int? value, String? tagImg});
}

/// @nodoc
class __$$_LearnDataStatisticsDataCopyWithImpl<$Res>
    extends _$LearnDataStatisticsDataCopyWithImpl<$Res,
        _$_LearnDataStatisticsData>
    implements _$$_LearnDataStatisticsDataCopyWith<$Res> {
  __$$_LearnDataStatisticsDataCopyWithImpl(_$_LearnDataStatisticsData _value,
      $Res Function(_$_LearnDataStatisticsData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? value = freezed,
    Object? tagImg = freezed,
  }) {
    return _then(_$_LearnDataStatisticsData(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int?,
      tagImg: freezed == tagImg
          ? _value.tagImg
          : tagImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_LearnDataStatisticsData implements _LearnDataStatisticsData {
  const _$_LearnDataStatisticsData(
      {this.type, this.name, this.value, this.tagImg});

  factory _$_LearnDataStatisticsData.fromJson(Map<String, dynamic> json) =>
      _$$_LearnDataStatisticsDataFromJson(json);

  @override
  final int? type;
// 数据类型
  @override
  final String? name;
// 数据名称
  @override
  final int? value;
// 数值
  @override
  final String? tagImg;

  @override
  String toString() {
    return 'LearnDataStatisticsData(type: $type, name: $name, value: $value, tagImg: $tagImg)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_LearnDataStatisticsData &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.tagImg, tagImg) || other.tagImg == tagImg));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, name, value, tagImg);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LearnDataStatisticsDataCopyWith<_$_LearnDataStatisticsData>
      get copyWith =>
          __$$_LearnDataStatisticsDataCopyWithImpl<_$_LearnDataStatisticsData>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_LearnDataStatisticsDataToJson(
      this,
    );
  }
}

abstract class _LearnDataStatisticsData implements LearnDataStatisticsData {
  const factory _LearnDataStatisticsData(
      {final int? type,
      final String? name,
      final int? value,
      final String? tagImg}) = _$_LearnDataStatisticsData;

  factory _LearnDataStatisticsData.fromJson(Map<String, dynamic> json) =
      _$_LearnDataStatisticsData.fromJson;

  @override
  int? get type;
  @override // 数据类型
  String? get name;
  @override // 数据名称
  int? get value;
  @override // 数值
  String? get tagImg;
  @override
  @JsonKey(ignore: true)
  _$$_LearnDataStatisticsDataCopyWith<_$_LearnDataStatisticsData>
      get copyWith => throw _privateConstructorUsedError;
}

Partner _$PartnerFromJson(Map<String, dynamic> json) {
  return _Partner.fromJson(json);
}

/// @nodoc
mixin _$Partner {
  String? get nickName => throw _privateConstructorUsedError;
  int? get medal => throw _privateConstructorUsedError;
  int? get continuousDays => throw _privateConstructorUsedError;
  int? get studyDays => throw _privateConstructorUsedError;
  int? get like => throw _privateConstructorUsedError; //点赞数默认是 1
  String? get likeDescription => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PartnerCopyWith<Partner> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PartnerCopyWith<$Res> {
  factory $PartnerCopyWith(Partner value, $Res Function(Partner) then) =
      _$PartnerCopyWithImpl<$Res, Partner>;
  @useResult
  $Res call(
      {String? nickName,
      int? medal,
      int? continuousDays,
      int? studyDays,
      int? like,
      String? likeDescription,
      String? img,
      String? url});
}

/// @nodoc
class _$PartnerCopyWithImpl<$Res, $Val extends Partner>
    implements $PartnerCopyWith<$Res> {
  _$PartnerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? medal = freezed,
    Object? continuousDays = freezed,
    Object? studyDays = freezed,
    Object? like = freezed,
    Object? likeDescription = freezed,
    Object? img = freezed,
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      medal: freezed == medal
          ? _value.medal
          : medal // ignore: cast_nullable_to_non_nullable
              as int?,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      like: freezed == like
          ? _value.like
          : like // ignore: cast_nullable_to_non_nullable
              as int?,
      likeDescription: freezed == likeDescription
          ? _value.likeDescription
          : likeDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PartnerCopyWith<$Res> implements $PartnerCopyWith<$Res> {
  factory _$$_PartnerCopyWith(
          _$_Partner value, $Res Function(_$_Partner) then) =
      __$$_PartnerCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickName,
      int? medal,
      int? continuousDays,
      int? studyDays,
      int? like,
      String? likeDescription,
      String? img,
      String? url});
}

/// @nodoc
class __$$_PartnerCopyWithImpl<$Res>
    extends _$PartnerCopyWithImpl<$Res, _$_Partner>
    implements _$$_PartnerCopyWith<$Res> {
  __$$_PartnerCopyWithImpl(_$_Partner _value, $Res Function(_$_Partner) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickName = freezed,
    Object? medal = freezed,
    Object? continuousDays = freezed,
    Object? studyDays = freezed,
    Object? like = freezed,
    Object? likeDescription = freezed,
    Object? img = freezed,
    Object? url = freezed,
  }) {
    return _then(_$_Partner(
      nickName: freezed == nickName
          ? _value.nickName
          : nickName // ignore: cast_nullable_to_non_nullable
              as String?,
      medal: freezed == medal
          ? _value.medal
          : medal // ignore: cast_nullable_to_non_nullable
              as int?,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      studyDays: freezed == studyDays
          ? _value.studyDays
          : studyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      like: freezed == like
          ? _value.like
          : like // ignore: cast_nullable_to_non_nullable
              as int?,
      likeDescription: freezed == likeDescription
          ? _value.likeDescription
          : likeDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Partner implements _Partner {
  const _$_Partner(
      {this.nickName = "",
      this.medal = 0,
      this.continuousDays = 0,
      this.studyDays = 0,
      this.like = 1,
      this.likeDescription = "",
      this.img = "",
      this.url = ""});

  factory _$_Partner.fromJson(Map<String, dynamic> json) =>
      _$$_PartnerFromJson(json);

  @override
  @JsonKey()
  final String? nickName;
  @override
  @JsonKey()
  final int? medal;
  @override
  @JsonKey()
  final int? continuousDays;
  @override
  @JsonKey()
  final int? studyDays;
  @override
  @JsonKey()
  final int? like;
//点赞数默认是 1
  @override
  @JsonKey()
  final String? likeDescription;
  @override
  @JsonKey()
  final String? img;
  @override
  @JsonKey()
  final String? url;

  @override
  String toString() {
    return 'Partner(nickName: $nickName, medal: $medal, continuousDays: $continuousDays, studyDays: $studyDays, like: $like, likeDescription: $likeDescription, img: $img, url: $url)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Partner &&
            (identical(other.nickName, nickName) ||
                other.nickName == nickName) &&
            (identical(other.medal, medal) || other.medal == medal) &&
            (identical(other.continuousDays, continuousDays) ||
                other.continuousDays == continuousDays) &&
            (identical(other.studyDays, studyDays) ||
                other.studyDays == studyDays) &&
            (identical(other.like, like) || other.like == like) &&
            (identical(other.likeDescription, likeDescription) ||
                other.likeDescription == likeDescription) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, nickName, medal, continuousDays,
      studyDays, like, likeDescription, img, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PartnerCopyWith<_$_Partner> get copyWith =>
      __$$_PartnerCopyWithImpl<_$_Partner>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PartnerToJson(
      this,
    );
  }
}

abstract class _Partner implements Partner {
  const factory _Partner(
      {final String? nickName,
      final int? medal,
      final int? continuousDays,
      final int? studyDays,
      final int? like,
      final String? likeDescription,
      final String? img,
      final String? url}) = _$_Partner;

  factory _Partner.fromJson(Map<String, dynamic> json) = _$_Partner.fromJson;

  @override
  String? get nickName;
  @override
  int? get medal;
  @override
  int? get continuousDays;
  @override
  int? get studyDays;
  @override
  int? get like;
  @override //点赞数默认是 1
  String? get likeDescription;
  @override
  String? get img;
  @override
  String? get url;
  @override
  @JsonKey(ignore: true)
  _$$_PartnerCopyWith<_$_Partner> get copyWith =>
      throw _privateConstructorUsedError;
}

Medel _$MedelFromJson(Map<String, dynamic> json) {
  return _Medel.fromJson(json);
}

/// @nodoc
mixin _$Medel {
  String? get name => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MedelCopyWith<Medel> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedelCopyWith<$Res> {
  factory $MedelCopyWith(Medel value, $Res Function(Medel) then) =
      _$MedelCopyWithImpl<$Res, Medel>;
  @useResult
  $Res call({String? name, String? img});
}

/// @nodoc
class _$MedelCopyWithImpl<$Res, $Val extends Medel>
    implements $MedelCopyWith<$Res> {
  _$MedelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? img = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MedelCopyWith<$Res> implements $MedelCopyWith<$Res> {
  factory _$$_MedelCopyWith(_$_Medel value, $Res Function(_$_Medel) then) =
      __$$_MedelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name, String? img});
}

/// @nodoc
class __$$_MedelCopyWithImpl<$Res> extends _$MedelCopyWithImpl<$Res, _$_Medel>
    implements _$$_MedelCopyWith<$Res> {
  __$$_MedelCopyWithImpl(_$_Medel _value, $Res Function(_$_Medel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? img = freezed,
  }) {
    return _then(_$_Medel(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_Medel implements _Medel {
  const _$_Medel({this.name, this.img});

  factory _$_Medel.fromJson(Map<String, dynamic> json) =>
      _$$_MedelFromJson(json);

  @override
  final String? name;
  @override
  final String? img;

  @override
  String toString() {
    return 'Medel(name: $name, img: $img)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Medel &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.img, img) || other.img == img));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name, img);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MedelCopyWith<_$_Medel> get copyWith =>
      __$$_MedelCopyWithImpl<_$_Medel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MedelToJson(
      this,
    );
  }
}

abstract class _Medel implements Medel {
  const factory _Medel({final String? name, final String? img}) = _$_Medel;

  factory _Medel.fromJson(Map<String, dynamic> json) = _$_Medel.fromJson;

  @override
  String? get name;
  @override
  String? get img;
  @override
  @JsonKey(ignore: true)
  _$$_MedelCopyWith<_$_Medel> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamList _$TeamListFromJson(Map<String, dynamic> json) {
  return _TeamList.fromJson(json);
}

/// @nodoc
mixin _$TeamList {
  String? get photo => throw _privateConstructorUsedError;
  int? get isSelf => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamListCopyWith<TeamList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamListCopyWith<$Res> {
  factory $TeamListCopyWith(TeamList value, $Res Function(TeamList) then) =
      _$TeamListCopyWithImpl<$Res, TeamList>;
  @useResult
  $Res call({String? photo, int? isSelf});
}

/// @nodoc
class _$TeamListCopyWithImpl<$Res, $Val extends TeamList>
    implements $TeamListCopyWith<$Res> {
  _$TeamListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? photo = freezed,
    Object? isSelf = freezed,
  }) {
    return _then(_value.copyWith(
      photo: freezed == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as String?,
      isSelf: freezed == isSelf
          ? _value.isSelf
          : isSelf // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamListCopyWith<$Res> implements $TeamListCopyWith<$Res> {
  factory _$$_TeamListCopyWith(
          _$_TeamList value, $Res Function(_$_TeamList) then) =
      __$$_TeamListCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? photo, int? isSelf});
}

/// @nodoc
class __$$_TeamListCopyWithImpl<$Res>
    extends _$TeamListCopyWithImpl<$Res, _$_TeamList>
    implements _$$_TeamListCopyWith<$Res> {
  __$$_TeamListCopyWithImpl(
      _$_TeamList _value, $Res Function(_$_TeamList) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? photo = freezed,
    Object? isSelf = freezed,
  }) {
    return _then(_$_TeamList(
      photo: freezed == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as String?,
      isSelf: freezed == isSelf
          ? _value.isSelf
          : isSelf // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamList implements _TeamList {
  const _$_TeamList({this.photo, this.isSelf});

  factory _$_TeamList.fromJson(Map<String, dynamic> json) =>
      _$$_TeamListFromJson(json);

  @override
  final String? photo;
  @override
  final int? isSelf;

  @override
  String toString() {
    return 'TeamList(photo: $photo, isSelf: $isSelf)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamList &&
            (identical(other.photo, photo) || other.photo == photo) &&
            (identical(other.isSelf, isSelf) || other.isSelf == isSelf));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, photo, isSelf);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamListCopyWith<_$_TeamList> get copyWith =>
      __$$_TeamListCopyWithImpl<_$_TeamList>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamListToJson(
      this,
    );
  }
}

abstract class _TeamList implements TeamList {
  const factory _TeamList({final String? photo, final int? isSelf}) =
      _$_TeamList;

  factory _TeamList.fromJson(Map<String, dynamic> json) = _$_TeamList.fromJson;

  @override
  String? get photo;
  @override
  int? get isSelf;
  @override
  @JsonKey(ignore: true)
  _$$_TeamListCopyWith<_$_TeamList> get copyWith =>
      throw _privateConstructorUsedError;
}

GrowthData _$GrowthDataFromJson(Map<String, dynamic> json) {
  return _GrowthData.fromJson(json);
}

/// @nodoc
mixin _$GrowthData {
  int? get classId => throw _privateConstructorUsedError;
  int? get courseSegmentCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GrowthDataCopyWith<GrowthData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GrowthDataCopyWith<$Res> {
  factory $GrowthDataCopyWith(
          GrowthData value, $Res Function(GrowthData) then) =
      _$GrowthDataCopyWithImpl<$Res, GrowthData>;
  @useResult
  $Res call({int? classId, int? courseSegmentCode});
}

/// @nodoc
class _$GrowthDataCopyWithImpl<$Res, $Val extends GrowthData>
    implements $GrowthDataCopyWith<$Res> {
  _$GrowthDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? courseSegmentCode = freezed,
  }) {
    return _then(_value.copyWith(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_GrowthDataCopyWith<$Res>
    implements $GrowthDataCopyWith<$Res> {
  factory _$$_GrowthDataCopyWith(
          _$_GrowthData value, $Res Function(_$_GrowthData) then) =
      __$$_GrowthDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? classId, int? courseSegmentCode});
}

/// @nodoc
class __$$_GrowthDataCopyWithImpl<$Res>
    extends _$GrowthDataCopyWithImpl<$Res, _$_GrowthData>
    implements _$$_GrowthDataCopyWith<$Res> {
  __$$_GrowthDataCopyWithImpl(
      _$_GrowthData _value, $Res Function(_$_GrowthData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? classId = freezed,
    Object? courseSegmentCode = freezed,
  }) {
    return _then(_$_GrowthData(
      classId: freezed == classId
          ? _value.classId
          : classId // ignore: cast_nullable_to_non_nullable
              as int?,
      courseSegmentCode: freezed == courseSegmentCode
          ? _value.courseSegmentCode
          : courseSegmentCode // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_GrowthData implements _GrowthData {
  const _$_GrowthData({this.classId, this.courseSegmentCode});

  factory _$_GrowthData.fromJson(Map<String, dynamic> json) =>
      _$$_GrowthDataFromJson(json);

  @override
  final int? classId;
  @override
  final int? courseSegmentCode;

  @override
  String toString() {
    return 'GrowthData(classId: $classId, courseSegmentCode: $courseSegmentCode)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_GrowthData &&
            (identical(other.classId, classId) || other.classId == classId) &&
            (identical(other.courseSegmentCode, courseSegmentCode) ||
                other.courseSegmentCode == courseSegmentCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, classId, courseSegmentCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_GrowthDataCopyWith<_$_GrowthData> get copyWith =>
      __$$_GrowthDataCopyWithImpl<_$_GrowthData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_GrowthDataToJson(
      this,
    );
  }
}

abstract class _GrowthData implements GrowthData {
  const factory _GrowthData(
      {final int? classId, final int? courseSegmentCode}) = _$_GrowthData;

  factory _GrowthData.fromJson(Map<String, dynamic> json) =
      _$_GrowthData.fromJson;

  @override
  int? get classId;
  @override
  int? get courseSegmentCode;
  @override
  @JsonKey(ignore: true)
  _$$_GrowthDataCopyWith<_$_GrowthData> get copyWith =>
      throw _privateConstructorUsedError;
}
