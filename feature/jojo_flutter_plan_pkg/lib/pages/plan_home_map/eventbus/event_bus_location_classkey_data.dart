import 'package:jojo_flutter_plan_pkg/pages/plan_home_lesson/model/course_promote_finish.dart';

class EventBusLocationClassKeyData {
  final String classKey;
  EventBusLocationClassKeyData(this.classKey);
}

/// 首页列表生命周期通知
class EventBusPromoteFinishLifecycleData {
  final LifecycleType lifecycleType;
  EventBusPromoteFinishLifecycleData(this.lifecycleType);
}

/// 促完课动画执行弹窗通知
class EventBusPromoteFinishAnimationDialogData {
  final AnimationType animationType;
  EventBusPromoteFinishAnimationDialogData(this.animationType);
}

/// 促完课动画执行弹窗执行的通知
class EventBusPromoteDialogShowData {
  final AnimationDialogType dialogType;
  final double topSpace;
  final double leftSpace;
  final double progressItemWidth;
  final int courseCompleteCount;  // 处理飞星资源没下载好,动画无法执行,完课数不能更新的问题
  EventBusPromoteDialogShowData(this.dialogType,this.topSpace,this.progressItemWidth,this.leftSpace, this.courseCompleteCount);
}

/// 促完课引入弹窗点击跳转落地页通知
class EventBusPromoteFinishGuideDialogData {
  EventBusPromoteFinishGuideDialogData();
}
