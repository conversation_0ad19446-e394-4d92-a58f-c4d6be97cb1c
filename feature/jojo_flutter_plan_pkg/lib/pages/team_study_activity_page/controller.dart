import 'dart:ffi';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_state.dart';
import 'package:jojo_flutter_plan_pkg/service/team_study_activity_api.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'state.dart';

class TeamStudyActivityController extends Cubit<TeamStudyActivityState> {
  TeamStudyActivityController({TeamStudyActivityApi? api})
      : api = api ?? teamStudyApi, // 在初始化列表中处理
        super(TeamStudyActivityState(status: PageStatus.loading));

  final TeamStudyActivityApi api;

  static String TeamStudyActivityGuideKey = 'TeamStudyActivityGuideKey';

  // 获取活动信息
  fetchActivityInfo() async {
    final newState = state.copyWith();
    try {
      // 1 获取当前引导状态 TODO: csl测试代码
      const type = TeamStudyActivityGuideType.shipEnter; // await getGuideType();
      newState.setGuideType(type);

      // 2 获取活动信息
      TeamStudyActivityModel activityModel =
          await api.getTeamStudyActivityInfo(null, 'team'); // 不填默认阅读
      
      // 3 下载资源
      await downloadResource(activityModel);

      // 4 解析资源配置

      // 5 更新数据
      newState.activityModel = activityModel;
      newState.status = PageStatus.success;
    } catch (e) {
      
      newState.status = PageStatus.error;
    } finally {

      // 6 刷新UI
      emit(newState);
    }
  }

  // 是否能显示能量
  bool canShowEnergyAndUserInfo() {
    if(state.activityModel?.status == 1 && state.guideType == TeamStudyActivityGuideType.energyShow){
      return true;
    }
    if(state.activityModel?.status == 2){
      return true;
    }
    return false;
  }

  // 下载资源
  Future<void> downloadResource(TeamStudyActivityModel activityModel)async{

    return;
  }


  // spine动画播放结束
  spinePlayCompleted() {
    if (state.guideType == TeamStudyActivityGuideType.shipStop ||
        state.guideType == TeamStudyActivityGuideType.shipIdle ||
        state.guideType == TeamStudyActivityGuideType.shipForward ||
        state.guideType == TeamStudyActivityGuideType.shipStopAgain ||
        state.guideType == TeamStudyActivityGuideType.shipIdleAgain) {
      stepCompleted();
    }
  }

  // 当前步骤结束
  stepCompleted() {
    final index = state.guideType!.index + 1;
    final newGuideType = TeamStudyActivityGuideType.values.length > index
        ? TeamStudyActivityGuideType.values[index]
        : TeamStudyActivityGuideType.explore;
    setGuideType(newGuideType);
  }

  // 领取活动奖励
  recvActivityReward() {
    if (state.activityModel == null) {
      return;
    }
    if (state.activityModel!.didGetReward()) {
      return;
    }
    int teamId = state.activityModel!.teamId ?? 0;
    int memberId = state.activityModel!.getSelfId();
    try {
      api.getMapRewards(teamId, 2, memberId, 0);
    } catch (e) {
      l.i('TeamStudyTag', '领取活动宝箱失败:$e');
    }
  }

  // 获取初始引导状态
  getBeginGuideType() async {
    final guideType = await getLocalGuideType();
  }

  // 设置引导状态
  setGuideType(TeamStudyActivityGuideType guideType) {
    // 开宝箱
    if (guideType == TeamStudyActivityGuideType.openTreasure) {
      // 领取奖励
      recvActivityReward();
      // 打开领取页面
      // if(state.activityModel != null && state.activityModel!.didGetReward() == false){
      //   final reward = state.activityModel!.joinActivityRewardList!.first;
      //   final data = TreasureChestData(name:reward.name ?? '', num: reward.num ?? 1,desc: reward.desc ?? '',icon: reward.img ?? '');
      //   TreasureChestPage.show(data: data,onClose: () {
      //     stepCompleted();
      //   });
      // }

      final data =
          TreasureChestData(name: '测试奖励', num: 1, desc: '测试描述', icon: '');
      TreasureChestPage.show(
          data: data,
          onClose: () {
            stepCompleted();
          });
    }

    final newState = state.copyWith();
    newState.setGuideType(guideType);
    emit(newState);

    // 存本地
    saveGuideTypeToLocal(guideType);
  }

  /// 设置本地存储的引导状态
  saveGuideTypeToLocal(TeamStudyActivityGuideType guideType) async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setInt(TeamStudyActivityGuideKey, guideType.index);
  }

  // 获取本地存储的引导状态
  Future<TeamStudyActivityGuideType> getLocalGuideType() async {
    final prefs = await SharedPreferences.getInstance();
    int guideTypeIndex = prefs.getInt(TeamStudyActivityGuideKey) ?? 0;
    return TeamStudyActivityGuideType.values[guideTypeIndex];
  }
}
