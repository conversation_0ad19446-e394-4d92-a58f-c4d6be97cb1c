import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/team_study_activity_const.dart';

class EnergyAddView extends StatefulWidget {
  final int energyCount;

  const EnergyAddView({
    Key? key,
    required this.energyCount,
  }) : super(key: key);

  @override
  State<EnergyAddView> createState() => _EnergyAddViewState();
}

class _EnergyAddViewState extends State<EnergyAddView>
    with TickerProviderStateMixin {
  late AnimationController _moveController;
  late Animation<Offset> _moveAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化移动动画控制器
    _moveController = AnimationController(
      duration: const Duration(milliseconds: TeamStudyConst.energyCountTime),
      vsync: this,
    );

    // 创建向上移动40像素的动画
    _moveAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset(0, -0.1.rdp),
    ).animate(CurvedAnimation(
      parent: _moveController,
      curve: Curves.easeOut,
    ));

    // 初始化透明度动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: TeamStudyConst.energyCountTime),
      vsync: this,
    );

    // 创建透明度动画
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(_fadeController);

    // 启动移动动画
    Future.delayed(const Duration(milliseconds: 100), () {
      _moveController.forward().then((_) {
        // 移动动画完成后，延迟800ms后启动淡出动画
        Future.delayed(
            const Duration(milliseconds: TeamStudyConst.energyNumDelayTime),
            () {
          if (mounted) {
            _fadeController.forward();
          }
        });
      });
    });
  }

  @override
  void dispose() {
    _moveController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  // @override
  // Widget build(BuildContext context) {
  //   return Positioned.fill(left: 130.rdp, top: 30.rdp, child: _energyWiget());
  // }

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
        left: 140.rdp,
        top: 30.rdp,
        child: FadeTransition(
          opacity: _fadeAnimation,
          child:
              SlideTransition(position: _moveAnimation, child: _energyWiget()),
        ));
  }

  Widget _energyWiget() {
    return Text('+${widget.energyCount}',
        style: TextStyle(
          color: Colors.white,
          fontSize: 32.rdp,
          fontWeight: FontWeight.w400,
          package: 'jojo_flutter_base',
          fontFamily: 'MohrRounded_Bold',
        ));
  }
}
