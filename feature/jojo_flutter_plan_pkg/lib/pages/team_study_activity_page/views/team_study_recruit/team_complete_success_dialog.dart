import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';

import '../../../../static/img.dart';

class TeamCompleteSuccessDialog extends StatefulWidget {
  final String? localImagePath; // 从外部传入的本地图片路径
  final VoidCallback? onStartJourney; // 出发按钮回调

  const TeamCompleteSuccessDialog({
    super.key,
    this.localImagePath,
    this.onStartJourney,
  });

  @override
  State<TeamCompleteSuccessDialog> createState() => _TeamCompleteSuccessDialogState();

  /// 显示队伍集结成功弹窗的静态方法
  static void show({
    String? localImagePath,
    VoidCallback? onStartJourney,
  }) {
    SmartDialog.show(
      builder: (context) => TeamCompleteSuccessDialog(
        localImagePath: localImagePath,
        onStartJourney: onStartJourney,
      ),
      alignment: Alignment.center,
      maskColor: Colors.transparent,
      clickMaskDismiss: false,
      backDismiss: false,
      usePenetrate: false,
    );
  }
}

class _TeamCompleteSuccessDialogState extends State<TeamCompleteSuccessDialog>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimations();
  }

  void _initAnimations() {
    // 滑入动画控制器
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 淡入动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // 从下往上滑入
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // 淡入动画
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
  }

  void _startAnimations() {
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _fadeController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFFF8E1), // 浅黄色
              Color(0xFFF3E5AB), // 金黄色
            ],
          ),
        ),
        child: SafeArea(
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  _buildHeader(),
                  Expanded(child: _buildMapContent()),
                  _buildBottomButton(),
                  SizedBox(height: 20.rdp),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 30.rdp),
      child: Column(
        children: [
          // 成功标题背景
          Container(
            padding: EdgeInsets.symmetric(horizontal: 24.rdp, vertical: 12.rdp),
            decoration: BoxDecoration(
              color: const Color(0xFFFF6B35), // 橙红色背景
              borderRadius: BorderRadius.circular(20.rdp),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8.rdp,
                  offset: Offset(0, 4.rdp),
                ),
              ],
            ),
            child: Text(
              '队伍集结成功！',
              style: TextStyle(
                fontSize: 20.rdp,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          SizedBox(height: 12.rdp),
          // 副标题
          Text(
            '一起探索知识的海洋吧！',
            style: TextStyle(
              fontSize: 16.rdp,
              color: const Color(0xFF8B4513), // 棕色
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapContent() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.rdp),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.rdp),
        border: Border.all(
          color: const Color(0xFF9C27B0), // 紫色边框
          width: 3.rdp,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 12.rdp,
            offset: Offset(0, 6.rdp),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(13.rdp),
        child: _buildMapImage(),
      ),
    );
  }

  Widget _buildMapImage() {
    if (widget.localImagePath != null && widget.localImagePath!.isNotEmpty) {
      // 显示本地下载的图片
      final file = File(widget.localImagePath!);
      if (file.existsSync()) {
        return Image.file(
          file,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
        );
      }
    }

    // 显示默认占位图
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF87CEEB), // 天蓝色背景
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.map,
            size: 80.rdp,
            color: Colors.white.withOpacity(0.7),
          ),
          SizedBox(height: 16.rdp),
          Text(
            '海洋地图加载中...',
            style: TextStyle(
              fontSize: 16.rdp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 40.rdp),
      child: GestureDetector(
        onTap: () {
          SmartDialog.dismiss();
          widget.onStartJourney?.call();
        },
        child: Container(
          width: double.infinity,
          height: 50.rdp,
          decoration: BoxDecoration(
            color: const Color(0xFFFFD700), // 金黄色
            borderRadius: BorderRadius.circular(25.rdp),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8.rdp,
                offset: Offset(0, 4.rdp),
              ),
            ],
          ),
          child: Center(
            child: Text(
              '出发',
              style: TextStyle(
                fontSize: 18.rdp,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF8B4513), // 棕色文字
              ),
            ),
          ),
        ),
      ),
    );
  }
}
