import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/spine_views/spine_animation_view.dart';

import 'package:spine_flutter/spine_flutter.dart';

class BoatBehindSpineAnimationView extends StatefulWidget {
  final SpineAnimationConfig animationConfig;
  const BoatBehindSpineAnimationView(
      {super.key, required this.animationConfig});

  @override
  State<BoatBehindSpineAnimationView> createState() =>
      _BoatBehindSpineAnimationViewState();
}

class _BoatBehindSpineAnimationViewState
    extends State<BoatBehindSpineAnimationView> {
  late JoJoSpineAnimationController _spineController;
  late Ticker _ticker;
  bool didLoad = false;

  final spineKey = UniqueKey();
  Bone? _targetBone;
  Bone? _targetBone2;
  Bone? _targetBone3;
  Bone? _targetBoneTx;
  @override
  void initState() {
    super.initState();
    // 初始化逻辑
    _spineController = JoJoSpineAnimationController();

    _ticker = Ticker((elapsed) {
      _updateBonePostion();
    });
    _ticker.start();
  }

  @override
  void dispose() {
    // 清理资源
    super.dispose();
    _ticker.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String atlasFile = '${widget.animationConfig.animationPath}.atlas.txt';
    String skelFile = '${widget.animationConfig.animationPath}.skel.bytes';
    if(didLoad){
      _play();
    }
    return JoJoSpineAnimationWidget(
      atlasFile,
      skelFile,
      LoadMode.assets,
      _spineController,
      useRootBoneAlign: true,
      package: Config.package,
      fit: BoxFit.none,
      key: spineKey,
      onInitialized: (controller) {
        _targetBone ??= controller.skeleton.findBone("js1"); // 替换为骨骼名称
        _targetBone2 ??= controller.skeleton.findBone("js2"); // 替换为骨骼名称
        _targetBone3 ??= controller.skeleton.findBone("js3"); // 替换为骨骼名称
        _targetBoneTx ??= controller.skeleton.findBone("tx"); // 替换为骨骼名称

        controller.skeleton.setScaleX(widget.animationConfig.scale);
        controller.skeleton.setScaleY(widget.animationConfig.scale);
        didLoad = true;
        try {
          _play();
        } catch (e) {
          l.w('SpineAnimation', "播放失败");
        }
      },
    );
  }

  _play() {
    _spineController.playAnimationWithCallback(
      JoJoSpineAnimation(
          animaitonName: widget.animationConfig.animationName,
          trackIndex: 0,
          loop: widget.animationConfig.isLoop,
          delay: 0,

          /// dieLoop循环太快了设置回调可能引起 didUpdateWidget 改变 _currentSpineNames，
          /// 马上触发 _initNextStatus切到下一个动画节点，当前的场景dieLoop无需回调
          listener: _animationEvent),
      listener: (type, entry, event) {
        _listenerAnimationEvent(type, entry, event);
      },
    );
  }

  _animationEvent(AnimationEventType type) {
    widget.animationConfig.eventCallback.call(type);
  }

  _listenerAnimationEvent(EventType type, TrackEntry entry, Event? event) {}

  _updateBonePostion() {
    Offset p1 = _getLocation(_targetBone);
    
    Offset p2 = _getLocation(_targetBone2);
    Offset p3 = _getLocation(_targetBone3);
    Offset tx = _getLocation(_targetBoneTx);
    // l.i('csl-----','offsetY:${p3.dy}');
    if (p1 != const Offset(0, 0) ||
        p2 != const Offset(0, 0) ||
        p3 != const Offset(0, 0) ||
        tx != const Offset(0, 0)) {
      widget.animationConfig.boneLocationCallback?.call(p1, p2, p3, tx);
    }
  }

  // 获取当前骨骼的位置
  Offset _getLocation(Bone? bone) {
    if (bone == null) {
      return const Offset(0, 0);
    }
    bone.updateWorldTransform();
    double x = bone.getWorldX();
    double y = bone.getWorldY();
    return Offset(x, y);
  }
}
