import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/team_study_activity_const.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/energy_add_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/energy_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/spine_views/boat_behind_spine_animation_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/spine_views/role_spine_animation_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/spine_views/spine_animation_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/user_info_view.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/static/spine.dart';
import 'package:jojo_flutter_plan_pkg/utils/num_to_image.dart';
import 'package:spine_flutter/spine_widget.dart';

class TeamStudyAnimationView extends StatefulWidget {
  final TeamStudyActivityState state;
  final TeamStudyActivityController controller;

  const TeamStudyAnimationView(
      {Key? key, required this.state, required this.controller})
      : super(key: key);

  @override
  State<TeamStudyAnimationView> createState() => _TeamStudyAnimationViewState();
}

class _TeamStudyAnimationViewState extends State<TeamStudyAnimationView>
    with TickerProviderStateMixin {
  // 在这里声明您的状态变量
  late AnimationController _moveController;

  // 船移动动画
  late Animation<Offset> _boatAnimation;

  int energyFlyCount = 0;

  GlobalKey personKey1 = GlobalKey();
  GlobalKey personKey2 = GlobalKey();
  GlobalKey personKey3 = GlobalKey();
  GlobalKey bgBehindKey = GlobalKey();
  GlobalKey bgFrontKey = GlobalKey();
  GlobalKey boatBehindKey = GlobalKey();
  GlobalKey boatFrontKey = GlobalKey();

  SpineWidgetController? _role1Controller;
  SpineWidgetController? _role2Controller;
  SpineWidgetController? _role3Controller;

  @override
  void initState() {
    super.initState();
    // 初始化动画控制器
    _moveController = AnimationController(
      duration: const Duration(milliseconds: TeamStudyConst.boatIntoTime),
      vsync: this,
    );

    // 创建动画曲线和路径
    _boatAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0), // 从屏幕左侧外部开始
      end: Offset.zero, // 到达当前位置(0, 0)
    ).animate(CurvedAnimation(
      parent: _moveController,
      curve: Curves.easeOutBack, // 使用弹性曲线增加动画效果
    ));

    // 监听动画状态
    _moveController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.controller.stepCompleted();
      }
    });

    // 启动动画
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _moveController.forward();
    });
  }

  @override
  void dispose() {
    // 释放资源
    _moveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _seaLayer(context);
  }

  _seaLayer(BuildContext context) {
    if (!mounted) {
      return const Positioned.fill(child: SizedBox.shrink());
    }
    return Positioned.fill(
        child: OverflowBox(
            maxWidth: 2000,
            child: Column(
              children: [
                _contentWidget(), // 中间内容
                _bottomBgWidget(), // 底部颜色
              ],
            )));
  }

  _contentWidget() {
    return SizedBox(
      width: 500,
      height: 375,
      child: Stack(
        children: [
          // 背景
          _seaBehind(),

          // 船整体
          _boatWhole(),

          // 前景
          _seaFront(),

          // 能量
          if (widget.controller.canShowEnergyAndUserInfo())
            _energysWidget(),
          // 个人信息
          if (widget.controller.canShowEnergyAndUserInfo())
            Positioned.fill(child: _userInfo()),
        ],
      ),
    );
  }

  // 船整体 包括船背景、人物、船前景
  Widget _boatWhole() {
    return Positioned.fill(
      child: SlideTransition(
        position: _boatAnimation,
        child: Stack(
          children: [
            // 船背景
            _boatBehind(),

            // 人物
            ..._personsWidget(),

            // 船前景
            _boatFront(),

            // 能量+1动画
            if (energyFlyCount > 0) EnergyAddView(energyCount: energyFlyCount),
          ],
        ),
      ),
    );
  }

  List<Widget> _personsWidget() {
    List<Widget> persons = [];
    if (widget.state.activityModel == null) {
      persons.add(_person());
      return persons;
    }
    final memberList = widget.state.activityModel!.teamMemberList;
    if (memberList == null) {
      persons.add(_person());
      return persons;
    }
    if (memberList.length == 3) {
      persons.add(_person3());
    }
    if (memberList.length >= 2) {
      persons.add(_person2());
    }
    persons.add(_person());
    return persons;
  }

  Widget _seaBehind() {
    final config = SpineAnimationConfig(
        animationPath: AssetsSpine.SPINE_NPC_BG_BEHIND,
        animationName: widget.state.guideType!.bgAnimationName,
        isLoop: true,
        scale: 500 / 1080.0,
        eventCallback: (type) => {});
    return Positioned.fill(
      child: SpineAnimationView(animationConfig: config),
    );
  }

  Widget _boatBehind() {
    final config = SpineAnimationConfig(
      animationPath: AssetsSpine.SPINE_NPC_BOAT_BEHIND,
      animationName: widget.state.guideType!.boatAnimationName,
      isLoop: true,
      scale: 500 / 1080.0,
      eventCallback: (type) => {_boatAnimationTypeCallback(type)},
      boneLocationCallback: (p1, p2, p3, tx) {
        _updateRolePostion(p1, p2, p3, tx);
      },
    );
    return Positioned.fill(
      child: BoatBehindSpineAnimationView(animationConfig: config),
    );
  }

  _boatAnimationTypeCallback(AnimationEventType type) {
    if (type != AnimationEventType.complete) {
      return;
    }
    widget.controller.spinePlayCompleted();
  }

  Widget _person() {
    final config = SpineAnimationConfig(
        animationPath: AssetsSpine.SPINE_NPC_DANCE,
        animationName: widget.state.guideType!.getRandomRoleAnimationName(),
        isLoop: true,
        scale: 0.2,
        eventCallback: (type) => {});
    return Positioned.fill(
      child: Align(
          alignment: Alignment.center,
          child: SizedBox(
            child: RoleSpineAnimationView(
              animationConfig: config,
              spineControllerCallback: (spineController) =>
                  {_role1Controller = spineController},
            ),
          )),
    );
  }

  Widget _person2() {
    final config = SpineAnimationConfig(
        animationPath: AssetsSpine.SPINE_NPC_DANCE,
        animationName: widget.state.guideType!.getRandomRoleAnimationName(),
        isLoop: true,
        scale: 0.2,
        eventCallback: (type) => {});
    return Positioned.fill(
      child: Align(
          alignment: Alignment.center,
          child: SizedBox(
              child: RoleSpineAnimationView(
            animationConfig: config,
            spineControllerCallback: (spineController) =>
                {_role2Controller = spineController},
          ))),
    );
  }

  Widget _person3() {
    final config = SpineAnimationConfig(
        animationPath: AssetsSpine.SPINE_NPC_DANCE,
        animationName: widget.state.guideType!.getRandomRoleAnimationName(),
        isLoop: true,
        scale: 0.2,
        eventCallback: (type) => {});
    return Positioned.fill(
      child: Align(
          alignment: Alignment.center,
          child: SizedBox(
              child: RoleSpineAnimationView(
            animationConfig: config,
            spineControllerCallback: (spineController) =>
                {_role3Controller = spineController},
          ))),
    );
  }

  Widget _boatFront() {
    final config = SpineAnimationConfig(
        animationPath: AssetsSpine.SPINE_NPC_BOAT_FRONT,
        animationName: widget.state.guideType!.boatAnimationName,
        isLoop: true,
        scale: 500 / 1080.0,
        eventCallback: (type) => {});
    return Positioned.fill(child: SpineAnimationView(animationConfig: config));
  }

  Widget _seaFront() {
    final config = SpineAnimationConfig(
        animationPath: AssetsSpine.SPINE_NPC_BG_FRONT,
        animationName: widget.state.guideType!.bgAnimationName,
        isLoop: true,
        scale: 500 / 1080.0,
        eventCallback: (type) => {});
    return Positioned.fill(child: SpineAnimationView(animationConfig: config));
  }

  _bottomBgWidget() {
    return Expanded(child: Container(color: HexColor('#2977AF')));
  }

  _updateRolePostion(Offset p1, Offset p2, Offset p3, Offset tx) {
    if (_role1Controller != null && mounted) {
      _role1Controller!.skeleton.setPosition(p1.dx, p1.dy + 80);
    }
    if (_role2Controller != null && mounted) {
      _role2Controller!.skeleton.setPosition(p2.dx, p2.dy + 80);
    }
    if (_role3Controller != null && mounted) {
      _role3Controller!.skeleton.setPosition(p3.dx, p3.dy + 80);
    }
  }

  Widget _energysWidget() {
    List<Widget> energys = [];

    if (widget.state.activityModel == null) {
      return const Positioned.fill(child:SizedBox.shrink());
    }
    final memberList = widget.state.activityModel!.teamMemberList;
    if (memberList == null || memberList.isEmpty) {
      return const Positioned.fill(child:SizedBox.shrink());
    }
    // 遍历成员列表，为每个成员创建独立的能量组件
    for (int i = 0; i < memberList.length; i++) {
      final member = memberList[i];
      final energyCount = member.pendingCollectionEnergy ?? 0;
      if(energyCount > 0){
        energys.add(_energyWiget(energyCount,i));
      } 
    }
    return Positioned.fill(child: Stack(children: energys),);
  }

  
  Widget _energyWiget(int energyCount,int index) {
    Alignment alignment = const Alignment(0.0, -0.65);
    if(index == 1){
      alignment = const Alignment(-0.4, -0.65);
    }if(index == 2){
      alignment = const Alignment(0.4, -0.65);
    }
    bool haveGuide = widget.state.guideType == TeamStudyActivityGuideType.energyShow;
    return EnergyView(energyCount: energyCount,alignment:alignment,haveGuide: haveGuide, collectCallback: _collectionEnergy);
  }

  _collectionEnergy(int t, int energyCount) {
    Future.delayed(Duration(milliseconds: t - 200), () {
      setState(() {
        energyFlyCount = energyCount;
      });
      Future.delayed(const Duration(milliseconds: 1200), () {
        setState(() {
          energyFlyCount = 0;
        });
      });
    });

    Future.delayed(Duration(milliseconds: t), () {
      widget.controller.stepCompleted();
    });
  }

  Widget _userInfo() {
    return UserInfoView(
      member: widget.state.activityModel!.teamMemberList!.first,
    );
  }
}
