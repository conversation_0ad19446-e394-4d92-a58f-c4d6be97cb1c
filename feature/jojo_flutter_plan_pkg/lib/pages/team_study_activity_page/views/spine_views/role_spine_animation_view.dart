import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_widget.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/spine_views/spine_animation_view.dart';

import 'package:spine_flutter/spine_flutter.dart';

class RoleSpineAnimationView extends StatefulWidget {
  final SpineAnimationConfig animationConfig;
  final Function(SpineWidgetController spineController) spineControllerCallback;
  const RoleSpineAnimationView({super.key, required this.animationConfig,required this.spineControllerCallback});

  @override
  State<RoleSpineAnimationView> createState() => _RoleSpineAnimationViewState();
}

class _RoleSpineAnimationViewState extends State<RoleSpineAnimationView> {
  late JoJoSpineAnimationController _spineController;
  bool didLoad = false;
  final spineKey = UniqueKey();

  @override
  void initState() {
    super.initState();
    // 初始化逻辑
    _spineController = JoJoSpineAnimationController();

  }

  @override
  void dispose() {
    // 清理资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String atlasFile = '${widget.animationConfig.animationPath}.atlas.txt';
    String skelFile = '${widget.animationConfig.animationPath}.skel.bytes';
    if(didLoad){
      _play();
    }
    return JoJoSpineAnimationWidget(
      atlasFile,
      skelFile,
      LoadMode.assets,
      _spineController,
      useRootBoneAlign: true,
      package: Config.package,
      fit: BoxFit.none,
      key: spineKey,
      onInitialized: (controller) {
        widget.spineControllerCallback(controller);
        controller.skeleton.setScaleX(widget.animationConfig.scale);
        controller.skeleton.setScaleY(widget.animationConfig.scale);
        didLoad = true;
        // controller.skeleton.setPosition(x, y);
        try {
          _play();
        } catch (e) {
          l.w('SpineAnimation', "播放失败");
        }
      },
    );
  }

  _play() {
    _spineController.playAnimationWithCallback(
      JoJoSpineAnimation(
          animaitonName: widget.animationConfig.animationName,
          trackIndex: 0,
          loop: widget.animationConfig.isLoop,
          delay: 0,

          /// dieLoop循环太快了设置回调可能引起 didUpdateWidget 改变 _currentSpineNames，
          /// 马上触发 _initNextStatus切到下一个动画节点，当前的场景dieLoop无需回调
          listener: _animationEvent),
      listener: (type, entry, event) {
        _listenerAnimationEvent(type, entry, event);
      },
    );

    // if (!_spineController.isAnimationPlaying()) {
    //   _spineController.resumeAnimation();
    // }
  }

  _animationEvent(AnimationEventType type) {
    widget.animationConfig.eventCallback.call(type);
  }

  _listenerAnimationEvent(EventType type, TrackEntry entry, Event? event) {

  }

}
