import 'package:freezed_annotation/freezed_annotation.dart';

import '../../team_study/sea_map/model/team_study_sea_map_model.dart';
part 'team_study_activity_model.freezed.dart';
part 'team_study_activity_model.g.dart';

@freezed
class TeamStudyActivityModel with _$TeamStudyActivityModel {
  factory TeamStudyActivityModel({
    int? teamId,
    int? status, //活动状态(0未解锁,1组队中,2进行中,3已结束)
    int? startTime,
    int? endTime,
    int? subjectType,
    String? subjectName, 
    String? resource, 
    String? mapResource, 
    int? current,
    int? total,
    int? isUnReceiveReward,
    int? nodeRewardType,

    List<TeamStudyActivityReward>? joinActivityRewardList,
    List<TeamStudyActivityMember>? teamMemberList,
    List<TeamStudySeaMapPointState>? mapNodeList,

  }) = _TeamStudyActivityModel;
  factory TeamStudyActivityModel.fromJson(Map<String, dynamic> json) =>_$TeamStudyActivityModelFromJson(json);
}

extension TeamStudyActivityModelExtension on TeamStudyActivityModel {
  
  // 是否已经领取活动奖励
  bool didGetReward(){
    if(joinActivityRewardList == null){ // 没有代表已领取
      return true;
    }
    for (var element in joinActivityRewardList!) {
      if(element.isReceive == 1){ // 1代表已领取
        return true;
      }
    }
    return false;
  }
  int getSelfId(){
    return teamMemberList?.firstWhere((element) => element.isSelf == 1).memberId ?? 0;
  }
}

@freezed
class TeamStudyActivityReward with _$TeamStudyActivityReward {
  factory TeamStudyActivityReward({
    int? id,
    int? type,
    int? num,
    String? name, 
    String? desc, 
    String? img, 
    int? isReceive, // 1:是否领取(1是0否)
    List<TeamStudyActivityReward>? joinActivityRewardList,
  }) = _TeamStudyActivityReward;
  factory TeamStudyActivityReward.fromJson(Map<String, dynamic> json) =>_$TeamStudyActivityRewardFromJson(json);
}

@freezed
class TeamStudyActivityMember with _$TeamStudyActivityMember {
  factory TeamStudyActivityMember({
    int? memberId,
    String? photo,
    String? nickname,
    int? dayCount,
    int? isSelf,
    int? pendingCollectionEnergy,
    List<TeamStudyActivityMemberDress>? dressList,
  }) = _TeamStudyActivityMember;
  factory TeamStudyActivityMember.fromJson(Map<String, dynamic> json) =>_$TeamStudyActivityMemberFromJson(json);
}


@freezed
class TeamStudyActivityMemberDress with _$TeamStudyActivityMemberDress {
  factory TeamStudyActivityMemberDress({
    String? resource,
  }) = _TeamStudyActivityMemberDress;

  factory TeamStudyActivityMemberDress.fromJson(Map<String, dynamic> json) =>_$TeamStudyActivityMemberDressFromJson(json);
}



/**
 * 
 * {
  "teamId": 1, //小队id
  "status": 1, //活动状态(0未解锁,1组队中,2进行中,3已结束)
  "startTime": 1755692259358, //开始时间
  "endTime": 1755692259358, //结束时间
  "subjectType": 1,
  "subjectName": "阅读",
  "resource": "", //页面资源
  "mapResource": "", //地图页面资源
  "current": 1, //当前进度
  "total": 30, //总进度
  "isUnReceiveReward": 1, //是否有未领取奖励(1是0否)
  "nodeRewardType": 1, //类型(0无奖励,1宝箱,2照片,3终极宝箱)
  "joinActivityRewardList": [ //参加活动奖励列表
    {
      "id": 1, //主键id
      "type": 1, //奖励类型(1道具)
      "name": "", //奖励名称
      "desc": "", //描述
      "img": "", //奖励图
      "isReceive": 1 //是否领取(1是0否)
    }
  ],
  "teamMemberList":[
    {
      "memberId": 1, //分布式id
      "photo": "", //头像
      "nickname": "昵称",
      "dayCount": 1, //最近连续天数
      "isSelf": 1, //是否本人(1是0否)
      "dressList": [
        {
          "resource": ""
        }
      ],
      "pendingCollectionEnergy": 1, //待领取能量
    }
  ],
  "mapNodeList": [
    {
      "teamId": 1, //小队id
      "nodeId": 1, //节点id
      "number": 1, //节点编号
      "isFinish": 1, //是否完成
      "isReceiveReward": 1, //是否领奖(1是0否),
      "receiveTime": 1, //领取时间
      "nodeRewardType": 1, //类型(0无奖励,1宝箱,2照片,3终极宝箱)
      "rewardList": [
        {
          "id": 1, //主键id
          "type": 1, //奖励类型(1道具)
          "name": "", //奖励名称
          "desc": "", //描述
          "img": "", //奖励图
        }
      ]
    }
  ]
}
 */