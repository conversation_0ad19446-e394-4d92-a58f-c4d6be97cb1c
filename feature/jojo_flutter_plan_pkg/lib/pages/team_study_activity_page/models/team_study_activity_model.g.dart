// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'team_study_activity_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_TeamStudyActivityModel _$$_TeamStudyActivityModelFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudyActivityModel(
      teamId: json['teamId'] as int?,
      status: json['status'] as int?,
      startTime: json['startTime'] as int?,
      endTime: json['endTime'] as int?,
      subjectType: json['subjectType'] as int?,
      subjectName: json['subjectName'] as String?,
      resource: json['resource'] as String?,
      mapResource: json['mapResource'] as String?,
      current: json['current'] as int?,
      total: json['total'] as int?,
      isUnReceiveReward: json['isUnReceiveReward'] as int?,
      nodeRewardType: json['nodeRewardType'] as int?,
      joinActivityRewardList: (json['joinActivityRewardList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudyActivityReward.fromJson(e as Map<String, dynamic>))
          .toList(),
      teamMemberList: (json['teamMemberList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudyActivityMember.fromJson(e as Map<String, dynamic>))
          .toList(),
      mapNodeList: (json['mapNodeList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudySeaMapPointState.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeamStudyActivityModelToJson(
        _$_TeamStudyActivityModel instance) =>
    <String, dynamic>{
      'teamId': instance.teamId,
      'status': instance.status,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'subjectType': instance.subjectType,
      'subjectName': instance.subjectName,
      'resource': instance.resource,
      'mapResource': instance.mapResource,
      'current': instance.current,
      'total': instance.total,
      'isUnReceiveReward': instance.isUnReceiveReward,
      'nodeRewardType': instance.nodeRewardType,
      'joinActivityRewardList': instance.joinActivityRewardList,
      'teamMemberList': instance.teamMemberList,
      'mapNodeList': instance.mapNodeList,
    };

_$_TeamStudyActivityReward _$$_TeamStudyActivityRewardFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudyActivityReward(
      id: json['id'] as int?,
      type: json['type'] as int?,
      num: json['num'] as int?,
      name: json['name'] as String?,
      desc: json['desc'] as String?,
      img: json['img'] as String?,
      isReceive: json['isReceive'] as int?,
      joinActivityRewardList: (json['joinActivityRewardList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudyActivityReward.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeamStudyActivityRewardToJson(
        _$_TeamStudyActivityReward instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'num': instance.num,
      'name': instance.name,
      'desc': instance.desc,
      'img': instance.img,
      'isReceive': instance.isReceive,
      'joinActivityRewardList': instance.joinActivityRewardList,
    };

_$_TeamStudyActivityMember _$$_TeamStudyActivityMemberFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudyActivityMember(
      memberId: json['memberId'] as int?,
      photo: json['photo'] as String?,
      nickname: json['nickname'] as String?,
      dayCount: json['dayCount'] as int?,
      isSelf: json['isSelf'] as int?,
      pendingCollectionEnergy: json['pendingCollectionEnergy'] as int?,
      dressList: (json['dressList'] as List<dynamic>?)
          ?.map((e) =>
              TeamStudyActivityMemberDress.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$_TeamStudyActivityMemberToJson(
        _$_TeamStudyActivityMember instance) =>
    <String, dynamic>{
      'memberId': instance.memberId,
      'photo': instance.photo,
      'nickname': instance.nickname,
      'dayCount': instance.dayCount,
      'isSelf': instance.isSelf,
      'pendingCollectionEnergy': instance.pendingCollectionEnergy,
      'dressList': instance.dressList,
    };

_$_TeamStudyActivityMemberDress _$$_TeamStudyActivityMemberDressFromJson(
        Map<String, dynamic> json) =>
    _$_TeamStudyActivityMemberDress(
      resource: json['resource'] as String?,
    );

Map<String, dynamic> _$$_TeamStudyActivityMemberDressToJson(
        _$_TeamStudyActivityMemberDress instance) =>
    <String, dynamic>{
      'resource': instance.resource,
    };
