// 消息页面
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_base/widgets/visibility_observe.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/state.dart';
import 'package:jojo_flutter_plan_pkg/service/team_study_activity_api.dart';

import 'views/team_study_activity_main_view.dart';

class TeamStudyActivityPage extends BasePage {
  const TeamStudyActivityPage({Key? key}) : super(key: key);

  @override
  State<TeamStudyActivityPage> createState() => _TeamStudyActivityPageState();
}

class _TeamStudyActivityPageState extends BaseState<TeamStudyActivityPage>
    with BasicInitPage {
  late TeamStudyActivityController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TeamStudyActivityController(api: mockTeamStudyApi);
    _controller.fetchActivityInfo();
  }

  @override
  void onResume() {
    super.onResume();
  }

  @override
  Widget body(context) {
    return BlocProvider(
        create: (_) => _controller,
        child: VisibilityObserve(
          onShow: () => {
            // todo: csl 埋点
          },
          child: _buildBody(context),
        ));
  }

  Widget _buildBody(context) {
    return BlocBuilder<TeamStudyActivityController, TeamStudyActivityState>(
        builder: (context, state) {
      return JoJoPageLoadingV25(
          status: state.status ?? PageStatus.loading,
          exception: state.exception,
          hideProgress: true,
          emptyText: '',
          retry: () => _controller.fetchActivityInfo(),
          backWidget: JoJoAppBar(
            title: S.of(context).partnerMessage,
            backgroundColor: Colors.transparent),
          child: Scaffold(
            appBar: JoJoAppBar(
              backgroundColor: HexColor('#8FEDF6'),
              onBack: _onBack,
            ),
            body: TeamStudyActivityMainView(state: state,controller: _controller,),
          ));
    });
  }

  _onBack(backHandler){

    l.i('TeamStudyActivityPage', 'onBack');
    backHandler?.call();
  }
}
