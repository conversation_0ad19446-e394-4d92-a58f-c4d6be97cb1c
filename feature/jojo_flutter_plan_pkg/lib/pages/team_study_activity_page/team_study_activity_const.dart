class TeamStudyConst {
  // 船进入动画时间
  static const boatIntoTime = 3000;

  // 个人信息显示时长
  static const userInfoShowDelayTime = 500;
  static const userInfoFadeInTime = 300;

  // 火苗飞+消失+数字动画总时长
  static const energyAllTime =
      energyFlyTime + energyFadeTime + energyNumDelayTime + energyCountTime;

  // 火苗飞的动画时长
  static const energyFlyTime = 400;
  // 火苗淡出的动画时长
  static const energyFadeTime = 200;

  // 火苗延时时间
  static const energyNumDelayTime = 800;
  // 火苗数字动画时长
  static const energyCountTime = 200;


  // 船出发tips动画时间
  static const boatStartTipsTime = 2600;

  // 手指引导动画 间隔 (多长时间没点就会显示)
  static const fingerGuideTime = 3000;

  // 发现宝箱移动动画
  static const findTreasureBoxTipsTime = 300;
}
