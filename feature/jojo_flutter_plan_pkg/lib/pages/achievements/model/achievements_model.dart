import 'package:freezed_annotation/freezed_annotation.dart';

import 'medal_model.dart';

part 'achievements_model.freezed.dart';
part 'achievements_model.g.dart';

/// 我的成就数据模型
@freezed
class AchievementsModel with _$AchievementsModel {
  factory AchievementsModel({
    /// 是否仅训练营 (1是 0否)
    int? onlyTrainingCamp,

    /// 页面站位图
    String? positionImg,

    /// 公共资源
    String? commonRes,

    /// 页面标题
    String? title,

    /// 科目颜色
    String? subjectColor,

    /// 最近获得的勋章
    List<MedalGroup>? latestMedals,

    /// 科目成就 (阶段勋章)
    List<SegmentMedal>? segmentMedals,
  }) = _AchievementsModel;

  factory AchievementsModel.fromJson(Map<String, dynamic> json) =>
      _$AchievementsModelFromJson(json);
}

/// 最近获得的勋章分组
@freezed
class MedalGroup with _$MedalGroup {
  factory MedalGroup({
    /// 是否可升级 (1是 0否)
    int? upgrade,

    /// 勋章分组 key
    String? groupKey,

    /// 奖章提示
    String? tips,

    /// 该分组下的勋章列表
    List<MedalModel>? groupMedals,
  }) = _MedalGroup;

  factory MedalGroup.fromJson(Map<String, dynamic> json) =>
      _$MedalGroupFromJson(json);
}

/// 科目成就 (即各个阶段的勋章)
@freezed
class SegmentMedal with _$SegmentMedal {
  factory SegmentMedal({
    /// 科目标题
    String? segmentTitle,

    /// 阶段名称
    String? segmentName,

    /// 阶段编码
    int? segmentCode,

    /// 阶段下的分类
    List<MedalCategory>? category,
  }) = _SegmentMedal;

  factory SegmentMedal.fromJson(Map<String, dynamic> json) =>
      _$SegmentMedalFromJson(json);
}

/// 奖章分类
@freezed
class MedalCategory with _$MedalCategory {
  factory MedalCategory({
    /// 分类名称
    String? name,

    /// 分类下的勋章组
    List<MedalGroup>? medals,
  }) = _MedalCategory;

  factory MedalCategory.fromJson(Map<String, dynamic> json) =>
      _$MedalCategoryFromJson(json);
}