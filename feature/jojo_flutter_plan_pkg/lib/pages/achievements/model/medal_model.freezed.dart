// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medal_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

MedalModel _$MedalModelFromJson(Map<String, dynamic> json) {
  return _MedalModel.fromJson(json);
}

/// @nodoc
mixin _$MedalModel {
  /// 勋章ID
  int? get id => throw _privateConstructorUsedError;

  /// 收集系统分布式主键ID
  String? get collectionId => throw _privateConstructorUsedError;

  /// 勋章称号
  String? get title => throw _privateConstructorUsedError;

  /// 勋章资源
  Map<String, String>? get resource => throw _privateConstructorUsedError;

  /// 是否获得 (1是 0否)
  int? get isGet => throw _privateConstructorUsedError;

  /// 获得时间 (时间戳)
  int? get getTime => throw _privateConstructorUsedError;

  /// 是否查看 (1是 0否)
  int? get isView => throw _privateConstructorUsedError;

  /// 勋章关联的任务描述 (主任务)
  String? get remark => throw _privateConstructorUsedError;

  /// 勋章详情路由
  String? get detailRoute => throw _privateConstructorUsedError;

  /// 分享页路由
  String? get shareRoute => throw _privateConstructorUsedError;

  /// 进度信息
  MedalProgress? get progress => throw _privateConstructorUsedError;

  /// 奖章标签
  List<MedalTag>? get tags => throw _privateConstructorUsedError;

  /// 奖章介绍
  String? get description => throw _privateConstructorUsedError;

  /// 奖章介绍音频
  String? get descriptionAudio => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MedalModelCopyWith<MedalModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedalModelCopyWith<$Res> {
  factory $MedalModelCopyWith(
          MedalModel value, $Res Function(MedalModel) then) =
      _$MedalModelCopyWithImpl<$Res, MedalModel>;
  @useResult
  $Res call(
      {int? id,
      String? collectionId,
      String? title,
      Map<String, String>? resource,
      int? isGet,
      int? getTime,
      int? isView,
      String? remark,
      String? detailRoute,
      String? shareRoute,
      MedalProgress? progress,
      List<MedalTag>? tags,
      String? description,
      String? descriptionAudio});

  $MedalProgressCopyWith<$Res>? get progress;
}

/// @nodoc
class _$MedalModelCopyWithImpl<$Res, $Val extends MedalModel>
    implements $MedalModelCopyWith<$Res> {
  _$MedalModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? collectionId = freezed,
    Object? title = freezed,
    Object? resource = freezed,
    Object? isGet = freezed,
    Object? getTime = freezed,
    Object? isView = freezed,
    Object? remark = freezed,
    Object? detailRoute = freezed,
    Object? shareRoute = freezed,
    Object? progress = freezed,
    Object? tags = freezed,
    Object? description = freezed,
    Object? descriptionAudio = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      collectionId: freezed == collectionId
          ? _value.collectionId
          : collectionId // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as Map<String, String>?,
      isGet: freezed == isGet
          ? _value.isGet
          : isGet // ignore: cast_nullable_to_non_nullable
              as int?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      isView: freezed == isView
          ? _value.isView
          : isView // ignore: cast_nullable_to_non_nullable
              as int?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      detailRoute: freezed == detailRoute
          ? _value.detailRoute
          : detailRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      shareRoute: freezed == shareRoute
          ? _value.shareRoute
          : shareRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      progress: freezed == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as MedalProgress?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<MedalTag>?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAudio: freezed == descriptionAudio
          ? _value.descriptionAudio
          : descriptionAudio // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MedalProgressCopyWith<$Res>? get progress {
    if (_value.progress == null) {
      return null;
    }

    return $MedalProgressCopyWith<$Res>(_value.progress!, (value) {
      return _then(_value.copyWith(progress: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MedalModelCopyWith<$Res>
    implements $MedalModelCopyWith<$Res> {
  factory _$$_MedalModelCopyWith(
          _$_MedalModel value, $Res Function(_$_MedalModel) then) =
      __$$_MedalModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? collectionId,
      String? title,
      Map<String, String>? resource,
      int? isGet,
      int? getTime,
      int? isView,
      String? remark,
      String? detailRoute,
      String? shareRoute,
      MedalProgress? progress,
      List<MedalTag>? tags,
      String? description,
      String? descriptionAudio});

  @override
  $MedalProgressCopyWith<$Res>? get progress;
}

/// @nodoc
class __$$_MedalModelCopyWithImpl<$Res>
    extends _$MedalModelCopyWithImpl<$Res, _$_MedalModel>
    implements _$$_MedalModelCopyWith<$Res> {
  __$$_MedalModelCopyWithImpl(
      _$_MedalModel _value, $Res Function(_$_MedalModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? collectionId = freezed,
    Object? title = freezed,
    Object? resource = freezed,
    Object? isGet = freezed,
    Object? getTime = freezed,
    Object? isView = freezed,
    Object? remark = freezed,
    Object? detailRoute = freezed,
    Object? shareRoute = freezed,
    Object? progress = freezed,
    Object? tags = freezed,
    Object? description = freezed,
    Object? descriptionAudio = freezed,
  }) {
    return _then(_$_MedalModel(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      collectionId: freezed == collectionId
          ? _value.collectionId
          : collectionId // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value._resource
          : resource // ignore: cast_nullable_to_non_nullable
              as Map<String, String>?,
      isGet: freezed == isGet
          ? _value.isGet
          : isGet // ignore: cast_nullable_to_non_nullable
              as int?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      isView: freezed == isView
          ? _value.isView
          : isView // ignore: cast_nullable_to_non_nullable
              as int?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      detailRoute: freezed == detailRoute
          ? _value.detailRoute
          : detailRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      shareRoute: freezed == shareRoute
          ? _value.shareRoute
          : shareRoute // ignore: cast_nullable_to_non_nullable
              as String?,
      progress: freezed == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as MedalProgress?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<MedalTag>?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionAudio: freezed == descriptionAudio
          ? _value.descriptionAudio
          : descriptionAudio // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MedalModel implements _MedalModel {
  _$_MedalModel(
      {this.id,
      this.collectionId,
      this.title,
      final Map<String, String>? resource,
      this.isGet,
      this.getTime,
      this.isView,
      this.remark,
      this.detailRoute,
      this.shareRoute,
      this.progress,
      final List<MedalTag>? tags,
      this.description,
      this.descriptionAudio})
      : _resource = resource,
        _tags = tags;

  factory _$_MedalModel.fromJson(Map<String, dynamic> json) =>
      _$$_MedalModelFromJson(json);

  /// 勋章ID
  @override
  final int? id;

  /// 收集系统分布式主键ID
  @override
  final String? collectionId;

  /// 勋章称号
  @override
  final String? title;

  /// 勋章资源
  final Map<String, String>? _resource;

  /// 勋章资源
  @override
  Map<String, String>? get resource {
    final value = _resource;
    if (value == null) return null;
    if (_resource is EqualUnmodifiableMapView) return _resource;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// 是否获得 (1是 0否)
  @override
  final int? isGet;

  /// 获得时间 (时间戳)
  @override
  final int? getTime;

  /// 是否查看 (1是 0否)
  @override
  final int? isView;

  /// 勋章关联的任务描述 (主任务)
  @override
  final String? remark;

  /// 勋章详情路由
  @override
  final String? detailRoute;

  /// 分享页路由
  @override
  final String? shareRoute;

  /// 进度信息
  @override
  final MedalProgress? progress;

  /// 奖章标签
  final List<MedalTag>? _tags;

  /// 奖章标签
  @override
  List<MedalTag>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// 奖章介绍
  @override
  final String? description;

  /// 奖章介绍音频
  @override
  final String? descriptionAudio;

  @override
  String toString() {
    return 'MedalModel(id: $id, collectionId: $collectionId, title: $title, resource: $resource, isGet: $isGet, getTime: $getTime, isView: $isView, remark: $remark, detailRoute: $detailRoute, shareRoute: $shareRoute, progress: $progress, tags: $tags, description: $description, descriptionAudio: $descriptionAudio)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MedalModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.collectionId, collectionId) ||
                other.collectionId == collectionId) &&
            (identical(other.title, title) || other.title == title) &&
            const DeepCollectionEquality().equals(other._resource, _resource) &&
            (identical(other.isGet, isGet) || other.isGet == isGet) &&
            (identical(other.getTime, getTime) || other.getTime == getTime) &&
            (identical(other.isView, isView) || other.isView == isView) &&
            (identical(other.remark, remark) || other.remark == remark) &&
            (identical(other.detailRoute, detailRoute) ||
                other.detailRoute == detailRoute) &&
            (identical(other.shareRoute, shareRoute) ||
                other.shareRoute == shareRoute) &&
            (identical(other.progress, progress) ||
                other.progress == progress) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.descriptionAudio, descriptionAudio) ||
                other.descriptionAudio == descriptionAudio));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      collectionId,
      title,
      const DeepCollectionEquality().hash(_resource),
      isGet,
      getTime,
      isView,
      remark,
      detailRoute,
      shareRoute,
      progress,
      const DeepCollectionEquality().hash(_tags),
      description,
      descriptionAudio);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MedalModelCopyWith<_$_MedalModel> get copyWith =>
      __$$_MedalModelCopyWithImpl<_$_MedalModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MedalModelToJson(
      this,
    );
  }
}

abstract class _MedalModel implements MedalModel {
  factory _MedalModel(
      {final int? id,
      final String? collectionId,
      final String? title,
      final Map<String, String>? resource,
      final int? isGet,
      final int? getTime,
      final int? isView,
      final String? remark,
      final String? detailRoute,
      final String? shareRoute,
      final MedalProgress? progress,
      final List<MedalTag>? tags,
      final String? description,
      final String? descriptionAudio}) = _$_MedalModel;

  factory _MedalModel.fromJson(Map<String, dynamic> json) =
      _$_MedalModel.fromJson;

  @override

  /// 勋章ID
  int? get id;
  @override

  /// 收集系统分布式主键ID
  String? get collectionId;
  @override

  /// 勋章称号
  String? get title;
  @override

  /// 勋章资源
  Map<String, String>? get resource;
  @override

  /// 是否获得 (1是 0否)
  int? get isGet;
  @override

  /// 获得时间 (时间戳)
  int? get getTime;
  @override

  /// 是否查看 (1是 0否)
  int? get isView;
  @override

  /// 勋章关联的任务描述 (主任务)
  String? get remark;
  @override

  /// 勋章详情路由
  String? get detailRoute;
  @override

  /// 分享页路由
  String? get shareRoute;
  @override

  /// 进度信息
  MedalProgress? get progress;
  @override

  /// 奖章标签
  List<MedalTag>? get tags;
  @override

  /// 奖章介绍
  String? get description;
  @override

  /// 奖章介绍音频
  String? get descriptionAudio;
  @override
  @JsonKey(ignore: true)
  _$$_MedalModelCopyWith<_$_MedalModel> get copyWith =>
      throw _privateConstructorUsedError;
}

MedalProgress _$MedalProgressFromJson(Map<String, dynamic> json) {
  return _MedalProgress.fromJson(json);
}

/// @nodoc
mixin _$MedalProgress {
  /// 奖章进度总量
  int? get total => throw _privateConstructorUsedError;

  /// 奖章当前进度值
  int? get current => throw _privateConstructorUsedError;

  /// 进度单位 (天、字、次)
  String? get unit => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MedalProgressCopyWith<MedalProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedalProgressCopyWith<$Res> {
  factory $MedalProgressCopyWith(
          MedalProgress value, $Res Function(MedalProgress) then) =
      _$MedalProgressCopyWithImpl<$Res, MedalProgress>;
  @useResult
  $Res call({int? total, int? current, String? unit});
}

/// @nodoc
class _$MedalProgressCopyWithImpl<$Res, $Val extends MedalProgress>
    implements $MedalProgressCopyWith<$Res> {
  _$MedalProgressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = freezed,
    Object? current = freezed,
    Object? unit = freezed,
  }) {
    return _then(_value.copyWith(
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      unit: freezed == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MedalProgressCopyWith<$Res>
    implements $MedalProgressCopyWith<$Res> {
  factory _$$_MedalProgressCopyWith(
          _$_MedalProgress value, $Res Function(_$_MedalProgress) then) =
      __$$_MedalProgressCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? total, int? current, String? unit});
}

/// @nodoc
class __$$_MedalProgressCopyWithImpl<$Res>
    extends _$MedalProgressCopyWithImpl<$Res, _$_MedalProgress>
    implements _$$_MedalProgressCopyWith<$Res> {
  __$$_MedalProgressCopyWithImpl(
      _$_MedalProgress _value, $Res Function(_$_MedalProgress) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = freezed,
    Object? current = freezed,
    Object? unit = freezed,
  }) {
    return _then(_$_MedalProgress(
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      unit: freezed == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MedalProgress implements _MedalProgress {
  _$_MedalProgress({this.total, this.current, this.unit});

  factory _$_MedalProgress.fromJson(Map<String, dynamic> json) =>
      _$$_MedalProgressFromJson(json);

  /// 奖章进度总量
  @override
  final int? total;

  /// 奖章当前进度值
  @override
  final int? current;

  /// 进度单位 (天、字、次)
  @override
  final String? unit;

  @override
  String toString() {
    return 'MedalProgress(total: $total, current: $current, unit: $unit)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MedalProgress &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.unit, unit) || other.unit == unit));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, total, current, unit);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MedalProgressCopyWith<_$_MedalProgress> get copyWith =>
      __$$_MedalProgressCopyWithImpl<_$_MedalProgress>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MedalProgressToJson(
      this,
    );
  }
}

abstract class _MedalProgress implements MedalProgress {
  factory _MedalProgress(
      {final int? total,
      final int? current,
      final String? unit}) = _$_MedalProgress;

  factory _MedalProgress.fromJson(Map<String, dynamic> json) =
      _$_MedalProgress.fromJson;

  @override

  /// 奖章进度总量
  int? get total;
  @override

  /// 奖章当前进度值
  int? get current;
  @override

  /// 进度单位 (天、字、次)
  String? get unit;
  @override
  @JsonKey(ignore: true)
  _$$_MedalProgressCopyWith<_$_MedalProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

MedalTag _$MedalTagFromJson(Map<String, dynamic> json) {
  return _MedalTag.fromJson(json);
}

/// @nodoc
mixin _$MedalTag {
  /// 标签名称
  String? get name => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MedalTagCopyWith<MedalTag> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MedalTagCopyWith<$Res> {
  factory $MedalTagCopyWith(MedalTag value, $Res Function(MedalTag) then) =
      _$MedalTagCopyWithImpl<$Res, MedalTag>;
  @useResult
  $Res call({String? name});
}

/// @nodoc
class _$MedalTagCopyWithImpl<$Res, $Val extends MedalTag>
    implements $MedalTagCopyWith<$Res> {
  _$MedalTagCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
  }) {
    return _then(_value.copyWith(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MedalTagCopyWith<$Res> implements $MedalTagCopyWith<$Res> {
  factory _$$_MedalTagCopyWith(
          _$_MedalTag value, $Res Function(_$_MedalTag) then) =
      __$$_MedalTagCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? name});
}

/// @nodoc
class __$$_MedalTagCopyWithImpl<$Res>
    extends _$MedalTagCopyWithImpl<$Res, _$_MedalTag>
    implements _$$_MedalTagCopyWith<$Res> {
  __$$_MedalTagCopyWithImpl(
      _$_MedalTag _value, $Res Function(_$_MedalTag) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
  }) {
    return _then(_$_MedalTag(
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MedalTag implements _MedalTag {
  _$_MedalTag({this.name});

  factory _$_MedalTag.fromJson(Map<String, dynamic> json) =>
      _$$_MedalTagFromJson(json);

  /// 标签名称
  @override
  final String? name;

  @override
  String toString() {
    return 'MedalTag(name: $name)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MedalTag &&
            (identical(other.name, name) || other.name == name));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, name);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MedalTagCopyWith<_$_MedalTag> get copyWith =>
      __$$_MedalTagCopyWithImpl<_$_MedalTag>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MedalTagToJson(
      this,
    );
  }
}

abstract class _MedalTag implements MedalTag {
  factory _MedalTag({final String? name}) = _$_MedalTag;

  factory _MedalTag.fromJson(Map<String, dynamic> json) = _$_MedalTag.fromJson;

  @override

  /// 标签名称
  String? get name;
  @override
  @JsonKey(ignore: true)
  _$$_MedalTagCopyWith<_$_MedalTag> get copyWith =>
      throw _privateConstructorUsedError;
}
