
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/view_model.dart';

class MyAchievementsState {
  final PageStatus pageStatus;
  final Exception? exception;

  final bool onlyTrainingCamp;
  final int medalsCount;
  /// 最近获取到的奖章总数
  final int recentObtainedCount;

  final String commonResZip;
  final List<String> mediaPaths;
  final List<MyAchievementsItem> items;

  /// 页面标题
  final String? title;

  /// 科目颜色
  final String? subjectColor;

  MyAchievementsState(
    this.pageStatus, {
      this.exception,
    this.onlyTrainingCamp = false,
    this.items = const [],
    this.medalsCount = 0,
    this.recentObtainedCount = 0,
    this.commonResZip = "",
    this.mediaPaths = const [],
    this.title,
    this.subjectColor,
  });

  MyAchievementsState copyWith({
    PageStatus? pageStatus,
    Exception? exception,
    bool? onlyTrainingCamp,
    int? medalsCount,
    int? recentObtainedCount,
    String? commonResZip,
    List<MyAchievementsItem>? items,
    List<String>? mediaPaths,
    String? title,
    String? subjectColor,
  }) {
    return MyAchievementsState(
      pageStatus ?? this.pageStatus,
      exception: exception,
      onlyTrainingCamp: onlyTrainingCamp ?? this.onlyTrainingCamp,
      medalsCount: medalsCount ?? this.medalsCount,
      recentObtainedCount: recentObtainedCount ?? this.recentObtainedCount,
      commonResZip: commonResZip ?? this.commonResZip,
      items: items ?? this.items,
      mediaPaths: mediaPaths ?? this.mediaPaths,
      title: title ?? this.title,
      subjectColor: subjectColor ?? this.subjectColor,
    );
  }
}
