import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/achievement_detail/widget/medal_tag.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/model/medal_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/card_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/viewmodel/view_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/widget/carousel_indicator.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home/model/subject_type.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/simple_progress_bar.dart';

import '../../my_achievements/viewmodel/card_state.dart';
import '../../widget/achievement_card_wapper.dart';
import '../../widget/medal_speaker.dart';
import 'controller.dart';
import 'state.dart';

class TeacherAchievementDetailView extends StatefulWidget {
  final AudioPlayer audioPlayer;
  const TeacherAchievementDetailView({Key? key, required this.audioPlayer})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => TeacherAchievementDetailViewState();
}

class TeacherAchievementDetailViewState
    extends State<TeacherAchievementDetailView>
    with SingleTickerProviderStateMixin {
  late TeacherAchievementDetailState _state;
  late ValueNotifier<int> _indicatorController;

  /// 播放音频相关的变量
  int _currentAudioIndex = 0;
  StreamSubscription<void>? _audioCompletionSubscription;

  late AnimationController _lottieController;
  bool _isDescriptionPlaying = false;
  StreamSubscription<void>? _audioStateSubscription;

  @override
  void initState() {
    super.initState();
    _state = context.read<TeacherAchievementDetailController>().state;
    _indicatorController = ValueNotifier<int>(_state.initialIndex);

    if (_state.aiMediaPaths.isNotEmpty) {
      _playAiAudios();
    } else if (_state.localMediaPath.isNotEmpty) {
      _playLocalAudios();
    }

    _lottieController = AnimationController(vsync: this);
    _lottieController.addStatusListener(
      (status) {
        if (status == AnimationStatus.completed && _isDescriptionPlaying) {
          _lottieController.repeat();
        }
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    _indicatorController.dispose();
    _audioCompletionSubscription?.cancel();
    _lottieController.removeStatusListener((status) {});
    _lottieController.dispose();
    _audioStateSubscription?.cancel();
    widget.audioPlayer.stop();
  }

  _playAiAudios() {
    if (_state.aiMediaPaths.isNotEmpty) {
      _currentAudioIndex = 0;
      widget.audioPlayer
          .play(DeviceFileSource(_state.aiMediaPaths[_currentAudioIndex]));
      _audioCompletionSubscription =
          widget.audioPlayer.onPlayerComplete.listen((event) {
        _currentAudioIndex++;
        if (_currentAudioIndex < _state.aiMediaPaths.length) {
          widget.audioPlayer
              .play(DeviceFileSource(_state.aiMediaPaths[_currentAudioIndex]));
        }
      });
    }
  }

  _playLocalAudios() {
    if (_state.localMediaPath.isNotEmpty) {
      String? package = RunEnv.package;
      String keyName = package == null
          ? _state.localMediaPath
          : 'packages/$package/${_state.localMediaPath}';

      widget.audioPlayer.audioCache.prefix = '';
      widget.audioPlayer.play(AssetSource(keyName));
    }
  }

  @override
  Widget build(BuildContext context) {
    _state = context.read<TeacherAchievementDetailController>().state;

    return Stack(
      children: [
        Positioned.fill(child: _buildPage()),
      ],
    );
  }

  Widget _buildCarouselIndicator() {
    return CarouselIndicator(
        count: max(_state.medals.length, 1),
        itemSize: 6.rdp,
        spacing: 8.rdp,
        controller: _indicatorController);
  }

  Widget _buildPage() {
    return BlocProvider(
      key: Key(_state.medals[_state.initialIndex].id.toString()),
      create: (_) {
        MedalModel medal = _state.medals[_state.initialIndex];
        AchievementCard card = AchievementCard(
          1,
          "",
          medal,
          null,
        );
        var ctrl = CardController(card, _state.commonResZip);

        ctrl.shouldUpdateState = (CardState state) {
          return state.copyWith(isView: true, needShowProgress: false);
        };
        return ctrl;
      },
      child: BlocBuilder<CardController, CardState>(
        builder: (context, state) {
          return SingleChildScrollView(
            child: _buildDetail(true, _state.medals, _state.initialIndex),
          );
        },
      ),
    );
  }

  Widget _buildDetail(bool canUpgrade, List<MedalModel> medals, int index) {
    MedalModel currentMedal = medals[index];
    MedalModel? nextStageMedal =
        index + 1 < medals.length ? medals[index + 1] : null;

    DateFormat dateFormat = DateFormat("yyyy年MM月dd日获得");
    String medalAwardTime = dateFormat
        .format(DateTime.fromMillisecondsSinceEpoch(currentMedal.getTime ?? 0));

    String progressText = "";
    if (nextStageMedal == null && canUpgrade) {
      progressText = S.of(context).highestMedalMessage;
    }

    return Column(
      children: [
        SizedBox(height: 20.rdp),

        // 奖章动效
        SizedBox(
          width: 320.rdp,
          height: 180.rdp,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 70.rdp,
                height: 180.rdp,
                child: MaterialButton(
                  onPressed: () {
                    widget.audioPlayer.stop();
                    setState(() {
                      _isDescriptionPlaying = false;
                      context
                          .read<TeacherAchievementDetailController>()
                          .updateDisplayMedalAtIndex(_state.initialIndex - 1);
                    });
                  },
                  child: const Icon(Icons.navigate_before),
                ),
              ),
              AchievementCardWrapper(width: 180.rdp, height: 180.rdp),
              SizedBox(
                width: 70.rdp,
                height: 180.rdp,
                child: MaterialButton(
                  onPressed: () {
                    widget.audioPlayer.stop();
                    setState(() {
                      _isDescriptionPlaying = false;
                      context
                          .read<TeacherAchievementDetailController>()
                          .updateDisplayMedalAtIndex(_state.initialIndex + 1);
                    });
                  },
                  child: const Icon(Icons.navigate_next),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.rdp),

        // 奖章名称
        Text(
          currentMedal.title ?? "",
          style: context.textstyles.largestTextEmphasis.pf.copyWith(
            color: context.appColors.jColorGray6,
          ),
        ),
        SizedBox(height: 8.rdp),

        // 奖章标签
        if ((currentMedal.tags?.length ?? 0) > 0) _buildMedalTag(currentMedal),
        if ((currentMedal.tags?.length ?? 0) > 0) SizedBox(height: 8.rdp),

        // 奖章描述
        Text(
          currentMedal.remark ?? "",
          style: context.textstyles.bodyText.pf.copyWith(
            color: context.appColors.jColorGray5,
          ),
        ),
        SizedBox(height: 8.rdp),

        // 奖章获得时间
        Text(
          medalAwardTime,
          style: context.textstyles.remark.pf.copyWith(
            color: context.appColors.jColorGray4,
          ),
        ),
        if (canUpgrade) SizedBox(height: 28.rdp),

        // 进度条
        if (canUpgrade)
          Text(
            progressText,
            style: context.textstyles.bodyText.pf.copyWith(
              color: context.appColors.jColorGray5,
            ),
          ),
        SizedBox(height: 120.rdp),

        // 奖章介绍
        (currentMedal.description?.length ?? 0) > 0
            ? _buildMedalDetail(currentMedal)
            : Container()
      ],
    );
  }

  Widget _buildMedalDetail(MedalModel? medal) {
    double bottomMargin = 28.rdp;
    return GestureDetector(
      onTap: () {
        _playDescriptionAudio(medal);
      },
      child: Container(
        width: double.infinity,
        margin:
            EdgeInsets.only(left: 20.rdp, right: 20.rdp, bottom: bottomMargin),
        padding: EdgeInsets.all(14.rdp),
        decoration: BoxDecoration(
          border: Border.all(
            color: context.appColors.jColorGray3,
            width: 1.rdp,
          ),
          borderRadius:
              BorderRadius.circular(context.dimensions.largeCornerRadius),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 24.rdp,
              child: Row(
                children: [
                  Text(
                    S.of(context).medalDescription,
                    style: context.textstyles.bodyTextEmphasis.pf.copyWith(
                      color: context.appColors.jColorGray5,
                    ),
                  ),
                  Expanded(child: Container()),
                  (medal?.descriptionAudio ?? '').isNotEmpty
                      ? MedalSpeaker(
                          playing: _isDescriptionPlaying,
                          lottieController: _lottieController,
                          width: 24.rdp,
                          height: 24.rdp,
                        )
                      : Container(),
                ],
              ),
            ),
            SizedBox(height: 8.rdp),
            Text(
              medal?.description ?? "",
              style: context.textstyles.remark.pf.copyWith(
                color: context.appColors.jColorGray4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMedalTag(MedalModel? medal) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 4.rdp,
      children: [
        for (MedalTag tag in medal?.tags ?? [])
          tag.name == "稀有"
              ? RareMedalTag(tag.name ?? "")
              : NormalMedalTag(tag.name ?? ""),
      ],
    );
  }

  /// 播放描述音频
  void _playDescriptionAudio(MedalModel? medal) {
    if (_isDescriptionPlaying == true) {
      return;
    }

    var audioPath = medal?.descriptionAudio ?? '';
    if (audioPath.isNotEmpty) {
      setState(() {
        _isDescriptionPlaying = true;
      });
      widget.audioPlayer.play(UrlSource(audioPath));
      _audioStateSubscription ??=
          widget.audioPlayer.onPlayerStateChanged.listen((event) {
        if (_isDescriptionPlaying) {
          if (event == PlayerState.playing) {
            _lottieController.forward(from: 0.0);
          } else if (event == PlayerState.completed) {
            _stopSpeakerAnime();
          }
        }
      });
    }
  }

  void _stopSpeakerAnime() {
    if (_lottieController.isAnimating) {
      _lottieController.stop();
      setState(() {
        _isDescriptionPlaying = false;
      });
      widget.audioPlayer.stop();
    }
  }
}
