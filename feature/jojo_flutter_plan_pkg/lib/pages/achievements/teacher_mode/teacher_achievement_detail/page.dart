import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../../model/medal_model.dart';
import 'controller.dart';
import 'state.dart';
import 'view.dart';

class TeacherAchievementDetailPage extends BasePage {
  final MedalModel selectedMedal;
  final List<MedalModel> medals;
  final String commonResZip;
  const TeacherAchievementDetailPage(
      this.selectedMedal, this.medals, this.commonResZip,
      {super.key});

  @override
  State<StatefulWidget> createState() => TeacherAchievementDetailPageState();
}

class TeacherAchievementDetailPageState
    extends BaseState<TeacherAchievementDetailPage> {
  late final AudioPlayer _audioPlayer;

  @override
  initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
  }

  @override
  void dispose() {
    _audioPlayer.stop();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  void onPause() {
    super.onPause();
    if (_audioPlayer.state == PlayerState.playing) {
      _audioPlayer.pause();
    }
  }

  @override
  void onResume() {
    super.onResume();
    if (_audioPlayer.state == PlayerState.paused) {
      _audioPlayer.resume();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: JoJoAppBar(
          title: "成就详情",
          onBack: (backHandler) {
            if (_audioPlayer.state == PlayerState.playing) {
              _audioPlayer.stop();
            }
            backHandler?.call();
          },
        ),
        body: BlocProvider(create: (_) {
          return TeacherAchievementDetailController.withDefault(
              selectedMedal: widget.selectedMedal,
              medals: widget.medals,
              commonResZip: widget.commonResZip);
        }, child: BlocBuilder<TeacherAchievementDetailController,
            TeacherAchievementDetailState>(builder: (context, state) {
          return JoJoPageLoadingV25(
            status: state.status,
            hideProgress: true,
            exception: state.exception,
            retry: () {
              context
                  .read<TeacherAchievementDetailController>()
                  .setPageStatus(PageStatus.loading);
              context.read<TeacherAchievementDetailController>().refreshData();
            },
            child: TeacherAchievementDetailView(audioPlayer: _audioPlayer),
          );
        })));
  }
}
