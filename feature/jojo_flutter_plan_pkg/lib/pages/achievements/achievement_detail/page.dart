import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_lce.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/achievement_detail/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/achievement_detail/view.dart';

import '../../../generated/l10n.dart';
import 'controller.dart';

class AchievementDetailPage extends BasePage {
  final int medalId;

  const AchievementDetailPage(this.medalId, {super.key});

  @override
  State<StatefulWidget> createState() => AchievementDetailPageState();
}

class AchievementDetailPageState extends BaseState<AchievementDetailPage>
    with BasicInitPage {
  late final AudioPlayer _audioPlayer;

  late AchievementDetailController _controller;

  @override
  initState() {
    super.initState();
    _controller =
        AchievementDetailController.withDefault(medalId: widget.medalId);
    _audioPlayer = AudioPlayer();
  }

  @override
  void dispose() {
    _audioPlayer.stop();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  void onPause() {
    super.onPause();
    if (_audioPlayer.state == PlayerState.playing) {
      _audioPlayer.pause();
    }
  }

  @override
  void onResume() {
    super.onResume();
    if (_audioPlayer.state == PlayerState.paused) {
      _audioPlayer.resume();
    }
  }

  @override
  Widget body(context) {
    return Scaffold(
        appBar: JoJoAppBar(title: S.of(context).achievementsDetail),
        body: BlocProvider(create: (_) {
          return _controller;
        }, child:
            BlocBuilder<AchievementDetailController, AchievementDetailState>(
                builder: (context, state) {
          return JoJoPageLoadingV25(
            status: state.status,
            hideProgress: true,
            exception: state.exception,
            retry: () {
              context
                  .read<AchievementDetailController>()
                  .setPageStatus(PageStatus.loading);
              context.read<AchievementDetailController>().refreshData();
            },
            child: AchievementDetailView(audioPlayer: _audioPlayer),
          );
        })));
  }
}
