import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/share_achievement/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/share_achievement/view.dart';

import '../../../generated/l10n.dart';
import 'controller.dart';

class ShareAchievementPage extends BasePage {
  final int medalId;
  const ShareAchievementPage({Key? key, required this.medalId})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => ShareAchievementPageState();
}

class ShareAchievementPageState extends BaseState<ShareAchievementPage>
    with BasicInitPage {
  @override
  Widget body(context) {
    return BlocProvider(
        create: (context) =>
            ShareAchievementController.withDefault(medalId: widget.medalId),
        child: <PERSON><PERSON><PERSON><PERSON><ShareAchievementController, ShareAchievementState>(
            builder: (context, state) {
          return state.pageStatus != PageStatus.success
              ? JoJoPageLoadingV25(
                  status: state.pageStatus,
                  hideProgress: true,
                  exception: state.exception,
                  backWidget: JoJoAppBar(
                      title: S.of(context).achievementsDetail,
                      backgroundColor: Colors.transparent),
                  retry: () {
                    context
                        .read<ShareAchievementController>()
                        .setPageStatus(PageStatus.loading);
                    context.read<ShareAchievementController>().refreshData();
                  },
                  child: Container())
              : ShareAchievementView(
                  medalId: widget.medalId,
                  model: state.model!,
                  commonResZip: state.commonResZip!,
                );
        }));
  }
}
