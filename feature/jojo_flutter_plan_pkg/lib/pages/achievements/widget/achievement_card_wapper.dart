import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/widget/role_card.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/simple_progress_bar.dart';

import '../../../common/host_env/host_env.dart';
import '../my_achievements/viewmodel/card_controller.dart';
import '../my_achievements/viewmodel/card_state.dart';
import 'medal_card.dart';

class AchievementCardWrapper extends StatefulWidget {
  final double width;
  final double height;

  const AchievementCardWrapper(
      {super.key, required this.width, required this.height});

  @override
  State<StatefulWidget> createState() => AchievementCardWrapperState();
}

class AchievementCardWrapperState extends State<AchievementCardWrapper> {
  CardState get _state => context.read<CardController>().state;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // 内容
          Positioned.fill(child: _buildBody()),
          // 新角标
          if (!_state.isView && _state.isGet)
            Positioned(
              top: -4.rdp,
              right: -4.rdp,
              child: ImageAssetWeb(
                width: 32.rdp,
                height: 16.rdp,
                assetName: AssetsImg.MEDALS_MEDAL_NEW,
                fit: BoxFit.cover,
                package: RunEnv.package,
              ),
            ),
          // 调试信息
          if (kDebugMode) ...[
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDebugText("${_state.status}"),
                _buildDebugText("${_state.type}"),
                _buildDebugText("标题: ${_state.title}"),
                _buildDebugText("进度: ${_state.progress}"),
                _buildDebugText("显示进度: ${_state.needShowProgress}"),
                _buildDebugText("获取: ${_state.isGet}"),
                _buildDebugText("已查看: ${_state.isView}"),
              ],
            )
          ],
        ],
      ),
    );
  }

  Widget _buildDebugText(String text) {
    // return Container();
    return Text(
      text,
      style: TextStyle(
        fontSize: widget.width * 0.08,
        color: Colors.black.withOpacity(0.5),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 卡片
        Expanded(child: _buildCard()),

        // 进度条
        if (_state.needShowProgress) ...[
          SizedBox(height: 8.rdp),
          SizedBox(
            height: 6.rdp,
            child: SimpleProgressBar(
              progress: _state.progress,
              backgroundColor: context.appColors.jColorYellow2,
              backgroundBorderColor:
                  context.appColors.jColorYellow5.withOpacity(0.3),
              progressColor: context.appColors.jColorYellow4,
              progressBorderColor: context.appColors.jColorYellow5,
              padding: EdgeInsets.symmetric(horizontal: 31.rdp),
              borderRadius: BorderRadius.circular(3.rdp),
              progressBorderWidth: 1.rdp,
              backgroundBorderWidth: 1.rdp,
            ),
          )
        ],
      ],
    );
  }

  Widget _buildCard() {
    if (_state.status == CardStatus.show && _state.type == CardType.role) {
      return const RoleCard();
    } else if (_state.status == CardStatus.show &&
        _state.type == CardType.medal) {
      return const MedalCard();
    } else if (_state.status == CardStatus.downloading) {
      return ImageAssetWeb(
        assetName: AssetsImg.MEDALS_MEDAL_PLACEHOLDER,
        fit: BoxFit.cover,
        package: RunEnv.package,
      );
    } else {
      return ImageAssetWeb(
        assetName: AssetsImg.MEDALS_MEDAL_PLACEHOLDER,
        fit: BoxFit.cover,
        package: RunEnv.package,
      );
    }
  }
}
