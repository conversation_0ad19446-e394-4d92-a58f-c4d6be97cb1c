import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/static/lottie.dart';
import 'package:lottie/lottie.dart';

import '../../../common/config/config.dart';
import '../../../common/host_env/host_env.dart';
import '../../../static/img.dart';

class MedalSpeaker extends StatelessWidget {
  final bool playing;
  final AnimationController lottieController;
  final double width;
  final double height;

  const MedalSpeaker({
    super.key,
    required this.playing,
    required this.lottieController, required this.width, required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      width: width,
      height: height,
      child: Stack(
        children: [
          Positioned.fill(
            child: Visibility(
              visible: playing,
              child: Lottie.asset(
                AssetsLottie.SESSION_DAILY_TASK_VOICE,
                fit: BoxFit.contain,
                animate: false,
                package: RunEnv.package,
                controller: lottieController,
                onLoaded: (composition) {
                  lottieController.duration = composition.duration;
                },
              ),
            ),
          ),
          Positioned.fill(
            child: Visibility(
              visible: !playing,
              child: ImageAssetWeb(
                assetName: AssetsImg.MEDALS_MEDAL_SPEAKER_PLAY,
                package: Config.package,
                fit: BoxFit.contain,
                width: width,
                height: height,
              ),
            ),
          ),
          Positioned.fill(
            child: ImageAssetWeb(
              assetName: AssetsImg.MEDALS_MEDAL_SPEAKER,
              package: Config.package,
              fit: BoxFit.contain,
              width: width,
              height: height,
            ),
          ),
        ],
      ),
    );
  }
}
