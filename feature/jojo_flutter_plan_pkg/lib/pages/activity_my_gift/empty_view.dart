import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';

import '../../common/config/config.dart';
import '../../generated/l10n.dart';
import '../../static/img.dart';

class ActivityMyGiftEmptyWidget extends StatefulWidget {

  const ActivityMyGiftEmptyWidget({
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return _ActivityMyGiftEmptyWidgetState();
  }
}

class _ActivityMyGiftEmptyWidgetState extends State<ActivityMyGiftEmptyWidget> {

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Center(
        child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ImageAssetWeb(
                assetName :AssetsImg.PLAN_IMAGE_PLAN_ACTIVITY_GIFT_EMPTY,
                width: 200.rdp,
                height: 200.rdp,
                fit: BoxFit.cover,
                package: Config.package,
              ),
              SizedBox(height: 4.rdp,),
              Text(
                S.of(context).noGift,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: context.appColors.jColorGray4,
                    fontSize: 16.rdp,
                    fontWeight: FontWeight.w400),
              )
            ]
        ),
      ),
    );
  }
}
