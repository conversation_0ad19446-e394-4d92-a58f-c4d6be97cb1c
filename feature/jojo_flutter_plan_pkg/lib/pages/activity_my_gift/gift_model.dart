
import '../plan_home_lesson/model/course_promote_finish.dart';

class MyGiftModel {
  bool isNew = false;
  bool isGet = false; // 是否已经获得
  int index = 0;  // 属于第几个节点
  int getTime = 0;  // 获得时间
  int? type;  // 奖励类型
  int? medalId;  // 奖章id（当类型是奖章时，分享时需要用到）
  String? title;
  String? subTitle;
  String? spineAnimationName;
  String? route;
  PromoteLessonFinishSpineResourceVo? spineResourceVo;  // 阶段奖励动效资源对象

  MyGiftModel({
    required this.isNew,
    required this.index,
    required this.getTime,
    required this.title,
    required this.subTitle,
    required this.spineAnimationName,
    required this.route,
    required this.isGet,
    required this.type,
    required this.medalId,});
}