import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/pages/activity_my_gift/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/activity_my_gift/view.dart';

import '../../common/host_env/host_env.dart';
import '../plan_home_lesson/utils/spine_download_manager.dart';
import 'controller.dart';

class ActivityMyGiftPageModel extends BasePage {
  final String? buriedString;
  final String? subjectColor;
  final int? activityId;
  final int? classId;
  final int? courseId;
  final int? pageId;

  const ActivityMyGiftPageModel({Key? key, this.activityId, this.buriedString, this.subjectColor, this.classId, this.courseId, this.pageId}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ActivityMyGiftPageModelState();
}

class _ActivityMyGiftPageModelState extends BaseState<ActivityMyGiftPageModel> with BasicInitPage {

  late ActivityMyGiftCtrl _controller;

  @override
  void initState() {
    super.initState();
    _controller = ActivityMyGiftCtrl(activityId: widget.activityId, buriedString: widget.buriedString, subjectColor: widget.subjectColor, classId: widget.classId, courseId: widget.courseId, pageId: widget.pageId);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        if (widget.buriedString != null) {
          Map<String, dynamic> buriedMap = jsonDecode(widget.buriedString!);
          Map<String, dynamic> properties = {
            '\$element_name': "完课活动_进入我的奖励",
            ...buriedMap,
          };
          RunEnv.sensorsTrack('\$AppViewScreen',properties,);
        }
      } catch (e) {
        l.i("促完课活动", "我的奖励页，首页浏览埋点异常");
      }
    });
  }

  @override
  void onResume() {
    super.onResume();
    _controller.getDetailInfoData();
  }

  @override
  void dispose() {
    // 页面销毁了,取消下载
    GlobalDownloadManager downloadManager = GlobalDownloadManager();
    downloadManager.cancelAllDownloads();
    super.dispose();
  }

  @override
  Widget body(context) {
    return BlocProvider(
      create: (BuildContext context) => _controller,
      child: BlocBuilder<ActivityMyGiftCtrl, ActivityMyGiftState>(
          builder: (context, state) {
            return ActivityMyGiftPageView(state: state);
          }),
    );
  }
}
