import '../model/models.dart';
import '../view_models/my_partners_view_models.dart';

/// 我的学伴页面数据映射器
class MyPartnersMapper {
  /// 将接口的发现学伴数据映射为 View 数据模型
  static DiscoverPartnersViewModel mapDiscoverPartnersData(
      DiscoverPartnersData apiData) {
    return DiscoverPartnersViewModel(
      partnerList: (apiData.partners ?? [])
          .map((partner) => mapDiscoverPartner(partner))
          .toList(),
      sectionTitle: apiData.title ?? '',
      moreButtonText: apiData.viewMore ?? '',
      jumpRoute: apiData.jumpRoute,
    );
  }

  /// 将接口的发现学伴项映射为 View 数据模型
  static DiscoverPartnerViewModel mapDiscoverPartner(
      DiscoverPartner apiPartner) {
    return DiscoverPartnerViewModel(
      displayName: apiPartner.nickName ?? '',
      avatarUrl: apiPartner.img ?? '',
      partnerId: apiPartner.partnerId ?? 0,
      continuousDays: apiPartner.continuousDays,
      totalStudyDays: apiPartner.studyDays,
      profileUrl: apiPartner.url,
    );
  }

  /// 将接口的动态列表数据映射为 View 数据模型
  static DynamicsListViewModel mapDynamicsListData(DynamicsListData apiData) {
    return DynamicsListViewModel(
      dynamics: (apiData.dynamics ?? [])
          .map((dynamic) => mapDynamicItem(dynamic))
          .toList(),
      nextPageId: apiData.minId,
      pageSize: apiData.size,
    );
  }

  /// 将接口的动态项映射为 View 数据模型
  static DynamicViewModel mapDynamicItem(DynamicItem apiDynamic) {
    return DynamicViewModel(
      userName: apiDynamic.nickName ?? '',
      userAvatar: apiDynamic.img ?? '',
      dynamicContent: apiDynamic.content ?? '',
      publishTime: apiDynamic.timeDesc ?? '',
      dynamicId: apiDynamic.dynamicRelationId ?? 0,
      userId: apiDynamic.partnerId ?? 0,
      actionState: mapActionState(apiDynamic.actionState),
      dynamicType: apiDynamic.type,
      userProfileUrl: apiDynamic.url,
      subjectColor: apiDynamic.subjectColor,
      messageTypeName: apiDynamic.messageTypeName,
    );
  }

  /// 将接口的操作状态映射为 View 数据模型
  static DynamicActionViewModel mapActionState(ActionState? apiActionState) {
    return DynamicActionViewModel(
      hasPoked: (apiActionState?.poke ?? 0) == 1,
      hasGivenFlower: (apiActionState?.flower ?? 0) == 1,
    );
  }

  /// 将接口的学伴列表数据映射为 View 数据模型
  static PartnersListViewModel mapPartnersListData(PartnersListData apiData) {
    return PartnersListViewModel(
      partners: (apiData.partners ?? [])
          .map((partner) => mapPartnerItem(partner))
          .toList(),
      nextPageId: apiData.minId,
      pageSize: apiData.size,
    );
  }

  /// 将接口的学伴项映射为 View 数据模型
  static PartnerViewModel mapPartnerItem(PartnerItem apiPartner) {
    return PartnerViewModel(
      partnerName: apiPartner.nickName ?? '',
      partnerAvatar: apiPartner.img ?? '',
      partnerId: apiPartner.partnerId ?? 0,
      profileUrl: apiPartner.url,
    );
  }

  /// 将 View 数据模型的操作状态映射回接口格式（用于发送操作请求）
  static ActionState mapActionStateToApi(DynamicActionViewModel viewModel) {
    return ActionState(
      poke: viewModel.hasPoked ? 1 : 0,
      flower: viewModel.hasGivenFlower ? 1 : 0,
    );
  }

  /// 更新动态的操作状态
  static DynamicViewModel updateDynamicAction(
    DynamicViewModel original,
    String actionType,
    bool isActive,
  ) {
    final updatedActionState = actionType == 'poke'
        ? original.actionState.copyWith(hasPoked: isActive)
        : original.actionState.copyWith(hasGivenFlower: isActive);

    return original.copyWith(actionState: updatedActionState);
  }
}
