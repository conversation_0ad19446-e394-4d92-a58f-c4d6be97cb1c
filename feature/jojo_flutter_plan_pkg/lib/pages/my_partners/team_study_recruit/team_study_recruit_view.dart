import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/team_study_recruit/team_study_reruit_user_header.dart';
import 'package:lottie/lottie.dart';

import '../../../generated/l10n.dart';
import '../../../static/img.dart';
import '../../../static/lottie.dart';
import '../../../static/svg.dart';

class TeamStudyRecruitWidget extends StatefulWidget {
  final VoidCallback? onAddMemberTap;

  const TeamStudyRecruitWidget({
    super.key,
    this.onAddMemberTap,
  });

  @override
  State<TeamStudyRecruitWidget> createState() => _TeamStudyRecruitWidgetState();
}

// 用户数据模型
class TeamMemberData {
  final bool isSelf;
  final String? userName;
  final int? continuousDays;
  final String? avatarUrl;

  TeamMemberData({
    required this.isSelf,
    this.userName,
    this.continuousDays,
    this.avatarUrl,
  });
}

class _TeamStudyRecruitWidgetState extends State<TeamStudyRecruitWidget> {
  // 模拟数据，实际使用时替换为真实数据
  List<TeamMemberData> teamMembers = [
    TeamMemberData(
      isSelf: true,
      userName: '鳄鱼粑粑来了一起学习吧',
      continuousDays: 12,
    ),
    // TeamMemberData(
    //   isMine: false,
    //   userName: '鳄鱼粑粑来了一起学习吧',
    //   continuousDays: 999,
    // ),
    //     TeamMemberData(
    //   isMine: false,
    //   userName: '鳄鱼粑粑来了一起学习吧',
    //   continuousDays: 998,
    // ),
    // 第三个位置为空，会显示添加按钮
  ];
  @override
  Widget build(BuildContext context) {
    final bottom = MediaQuery.of(context).viewPadding.bottom;
    var tips = S.of(context).tempStudyRecruitTipTwoLeft;
    if (teamMembers.length == 2) {
      tips = S.of(context).tempStudyRecruitTipOneLeft;
    } else if (teamMembers.length == 3) {
      tips = S.of(context).tempStudyRecruitTipComplete;
    }

    return Stack(
      children: [
        Positioned(
          left: 20.rdp,
          right: 20.rdp,
          bottom: 43.rdp + bottom,
          child: Container(
            // color: Colors.red,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                    context.dimensions.largeCornerRadius.rdp),
                border: Border.all(
                    width: 1.rdp, color: context.appColors.jColorGray3),
                color: Colors.white),
            child: Padding(
              padding: EdgeInsets.all(context.dimensions.mediumSpacing.rdp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Container(
                    height: 49.rdp,
                    padding: EdgeInsets.only(
                        left: context.dimensions.minimumSpacing.rdp),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          S.of(context).tempStudyRecruitTitle,
                          style: context.textstyles.headingLargeEmphasis.pf
                              .copyWith(
                            color: context.appColors.jColorGray6,
                          ),
                        ),
                        // 副标题
                        Text(
                          S.of(context).tempStudyRecruitDetail,
                          style: context.textstyles.remark.pf.copyWith(
                            color: context.appColors.jColorGray6,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: context.dimensions.mediumSpacing.rdp),
                  // 头像和添加按钮区域
                  Container(
                    height: 90.rdp,
                    padding: EdgeInsets.symmetric(
                        horizontal: (context.dimensions.mediumSpacing -
                                context.dimensions.minimumSpacing)
                            .rdp,
                        vertical: context.dimensions.minimumSpacing.rdp),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: _buildTeamMemberWidgets(),
                    ),
                  ),
                  SizedBox(height: context.dimensions.mediumSpacing.rdp),
                  // 底部按钮
                  Container(
                    width: double.infinity,
                    height: 40.rdp,
                    decoration: BoxDecoration(
                      color: context.appColors.jColorYellow1,
                      borderRadius: BorderRadius.circular(20.rdp),
                      border: Border.all(
                          width: 1.rdp, color: context.appColors.jColorYellow2),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: context.dimensions.mediumSpacing.rdp,
                        vertical: context.dimensions.smallSpacing.rdp),
                    child: teamMembers.length == 3
                        ? Row(
                            children: [
                              ImageAssetWeb(
                                assetName:
                                    AssetsImg.PLAN_PURE_ENJOY_STEP_FINISH,
                                width: 24.rdp,
                                height: 24.rdp,
                                package: RunEnv.package,
                              ),
                              SizedBox(
                                  width: context.dimensions.smallSpacing.rdp),
                              Text(
                                tips,
                                style: context.textstyles.bodyText.pf.copyWith(
                                    color: context.appColors.jColorYellow6),
                              ),
                            ],
                          )
                        : Text(
                            tips,
                            style: context.textstyles.bodyText.pf.copyWith(
                                color: context.appColors.jColorYellow6),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 动态生成团队成员 widgets
  List<Widget> _buildTeamMemberWidgets() {
    List<Widget> widgets = [];

    for (int i = 0; i < 3; i++) {
      if (i < teamMembers.length) {
        final member = teamMembers[i];
        widgets.add(
          SizedBox(
            width: 88.rdp,
            height: 82.rdp,
            child: TeamStudyRecruitHeaderWidget(
              isSelf: member.isSelf,
              userName: member.userName,
              continuousDays: 0, // 目前不现实
              avatarUrl: member.avatarUrl,
            ),
          ),
        );
      } else {
        widgets.add(
          ClickWidget(
            type: ClickType.throttleWithTimeout,
            onTap: widget.onAddMemberTap,
            child: SizedBox(
              width: 88.rdp,
              height: 82.rdp,
              child: Center(
                child: SvgAssetWeb(
                  assetName: AssetsSvg.TEMP_STUDY_ADD,
                  height: 82.rdp,
                  width: 82.rdp,
                  package: RunEnv.package,
                ),
              ),
            ),
          ),
        );
      }
    }

    return widgets;
  }

      Widget _fingerGuideWidget(Rect? rect, double top) {
    return Transform(
        transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
        alignment: Alignment.center,
        child: IgnorePointer(
          ignoring: true,
          child: SizedBox(
              width: 80.rdp,
              height: 80.rdp,
              child: Lottie.asset(
                AssetsLottie.YEAR_FINGER_POINT,
                package: RunEnv.package,
                repeat: true,
              )),
        ),
      );
  }
}
