import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:jojo_flutter_base/base.dart';

class TeamStudyRecruitWidget extends StatefulWidget {
  const TeamStudyRecruitWidget({super.key});

  @override
  State<TeamStudyRecruitWidget> createState() => _TeamStudyRecruitWidgetState();
}

class _TeamStudyRecruitWidgetState extends State<TeamStudyRecruitWidget> {
  @override
  Widget build(BuildContext context) {
    final bottom = MediaQuery.of(context).viewPadding.bottom;
    return Container(color: Colors.orange,
    child: Stack(
      children: [
        Positioned(
          left: 20.rdp,
          right: 20.rdp,
          bottom: 43.rdp + bottom,
          child: Container(
            // color: Colors.red,
            decoration: BoxDecoration(),
            height: 235.rdp,
          ),
        ),
      ],
    ),);
  }
}