import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:jojo_flutter_base/base.dart';

class TeamStudyRecruitWidget extends StatefulWidget {
  const TeamStudyRecruitWidget({super.key});

  @override
  State<TeamStudyRecruitWidget> createState() => _TeamStudyRecruitWidgetState();
}

class _TeamStudyRecruitWidgetState extends State<TeamStudyRecruitWidget> {
  @override
  Widget build(BuildContext context) {
    final bottom = MediaQuery.of(context).viewPadding.bottom;
    return Container(color: Colors.orange,
    child: Stack(
      children: [
        Positioned(
          left: 20.rdp,
          right: 20.rdp,
          bottom: 43.rdp + bottom,
          child: Container(
              // color: Colors.red,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                    context.dimensions.largeCornerRadius.rdp),
                    border: Border.all(width: 1.rdp, color: context.appColors.jColorGray3),
                color: Colors.white
              ),
                              padding: EdgeInsets.all(context.dimensions.mediumSpacing.rdp),

              height: 237.rdp,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Container(
                    height: 49.rdp,
                    padding: EdgeInsets.only(left: context.dimensions.minimumSpacing.rdp),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '邀请伙伴加入学习号',
                          style: 
                          context.textstyles.headingLargeEmphasis.pf.copyWith(
                            color: context.appColors.jColorGray6,
                          ),
                        ),
                        // 副标题
                        Text(
                          '一起探索知识的海洋',
                          style: context.textstyles.remark.pf.copyWith(
                            color: context.appColors.jColorGray6,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: context.dimensions.mediumSpacing.rdp),
                  // 头像和添加按钮区域
                  Container(
                    height: 90.rdp,
                    padding: EdgeInsets.symmetric(
                        horizontal: context.dimensions.mediumSpacing.rdp,
                        vertical: context.dimensions.minimumSpacing.rdp),
                    child: Row(
                      children: [
                        // 用户头像
                        Container(
                          width: 50.rdp,
                          height: 50.rdp,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(25.rdp),
                            color: Colors.orange[100],
                          ),
                          child: Stack(
                            children: [
                              // 头像图片
                              Center(
                                child: Icon(
                                  Icons.person,
                                  size: 30.rdp,
                                  color: Colors.orange[400],
                                ),
                              ),
                              // 小徽章
                              Positioned(
                                bottom: 0,
                                right: 0,
                                child: Container(
                                  width: 16.rdp,
                                  height: 16.rdp,
                                  decoration: BoxDecoration(
                                    color: Colors.orange,
                                    borderRadius: BorderRadius.circular(8.rdp),
                                    border: Border.all(color: Colors.white, width: 1),
                                  ),
                                  child: Center(
                                    child: Text(
                                      '9',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 10.rdp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 8.rdp),
                        // 用户名标签
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 12.rdp, vertical: 6.rdp),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(15.rdp),
                          ),
                          child: Text(
                            '鳄鱼*... (我)',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.rdp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        SizedBox(width: 15.rdp),
                        // 添加按钮1
                        Container(
                          width: 50.rdp,
                          height: 50.rdp,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.orange, width: 2),
                            borderRadius: BorderRadius.circular(25.rdp),
                          ),
                          child: Icon(
                            Icons.add,
                            color: Colors.orange,
                            size: 24.rdp,
                          ),
                        ),
                        SizedBox(width: 15.rdp),
                        // 添加按钮2
                        Container(
                          width: 50.rdp,
                          height: 50.rdp,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.orange, width: 2),
                            borderRadius: BorderRadius.circular(25.rdp),
                          ),
                          child: Icon(
                            Icons.add,
                            color: Colors.orange,
                            size: 24.rdp,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: context.dimensions.mediumSpacing.rdp),
                  // 底部按钮
                  Container(
                    width: double.infinity,
                    height: 40.rdp,
                    decoration: BoxDecoration(
                      color: context.appColors.jColorYellow1,
                      borderRadius: BorderRadius.circular(20.rdp),
                      border: Border.all(
                          width: 1.rdp,
                          color: context.appColors.jColorYellow2),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: context.dimensions.mediumSpacing.rdp,
                        vertical: context.dimensions.smallSpacing.rdp),
                    child: Text(
                      '凑齐三人出发吧！',
                      style: context.textstyles.bodyText.pf
                          .copyWith(color: context.appColors.jColorYellow6),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}