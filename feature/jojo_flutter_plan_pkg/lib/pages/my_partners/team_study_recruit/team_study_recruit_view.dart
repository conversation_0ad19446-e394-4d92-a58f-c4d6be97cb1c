import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/team_study_recruit/team_study_reruit_user_header.dart';

class TeamStudyRecruitWidget extends StatefulWidget {
  const TeamStudyRecruitWidget({super.key});

  @override
  State<TeamStudyRecruitWidget> createState() => _TeamStudyRecruitWidgetState();
}

class _TeamStudyRecruitWidgetState extends State<TeamStudyRecruitWidget> {
  @override
  Widget build(BuildContext context) {
    final bottom = MediaQuery.of(context).viewPadding.bottom;
    return Container(color: Colors.orange,
    child: Stack(
      children: [
        Positioned(
          left: 20.rdp,
          right: 20.rdp,
          bottom: 43.rdp + bottom,
          child: Container(
              // color: Colors.red,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                    context.dimensions.largeCornerRadius.rdp),
                    border: Border.all(width: 1.rdp, color: context.appColors.jColorGray3),
                color: Colors.white
              ),
              child: Padding(
                padding: EdgeInsets.all(context.dimensions.mediumSpacing.rdp),
                child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Container(
                    height: 49.rdp,
                    padding: EdgeInsets.only(left: context.dimensions.minimumSpacing.rdp),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '邀请伙伴加入学习号',
                          style: 
                          context.textstyles.headingLargeEmphasis.pf.copyWith(
                            color: context.appColors.jColorGray6,
                          ),
                        ),
                        // 副标题
                        Text(
                          '一起探索知识的海洋',
                          style: context.textstyles.remark.pf.copyWith(
                            color: context.appColors.jColorGray6,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: context.dimensions.mediumSpacing.rdp),
                  // 头像和添加按钮区域
                  Container(
                    height: 90.rdp,
                    padding: EdgeInsets.symmetric(
                          horizontal: (context.dimensions.mediumSpacing -
                                  context.dimensions.minimumSpacing)
                              .rdp,
                        vertical: context.dimensions.minimumSpacing.rdp),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 用户头像
                        SizedBox(
                          width: 88.rdp,
                          height: 82.rdp,
                            child: const TeamStudyRecruitHeaderWidget(
                              isMine: true,
                              userName: '鳄鱼粑粑来了一起学习吧',
                              continuousDays: 12,
                            ),
                        ),
                        Container(
                          width: 88.rdp,
                          height: 82.rdp,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.orange, width: 2),
                            borderRadius: BorderRadius.circular(25.rdp),
                          ),
                          child: Icon(
                            Icons.add,
                            color: Colors.orange,
                            size: 24.rdp,
                          ),
                        ),
                        Container(
                          width: 88.rdp,
                          height: 50.rdp,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.orange, width: 2),
                            borderRadius: BorderRadius.circular(25.rdp),
                          ),
                          child: Icon(
                            Icons.add,
                            color: Colors.orange,
                            size: 24.rdp,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: context.dimensions.mediumSpacing.rdp),
                  // 底部按钮
                  Container(
                    width: double.infinity,
                    height: 40.rdp,
                    decoration: BoxDecoration(
                      color: context.appColors.jColorYellow1,
                      borderRadius: BorderRadius.circular(20.rdp),
                      border: Border.all(
                          width: 1.rdp,
                          color: context.appColors.jColorYellow2),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: context.dimensions.mediumSpacing.rdp,
                        vertical: context.dimensions.smallSpacing.rdp),
                    child: Text(
                      '凑齐三人出发吧！',
                      style: context.textstyles.bodyText.pf
                          .copyWith(color: context.appColors.jColorYellow6),
                    ),
                  ),
                ],
              ),
                ),
            ),
          ),
        ],
      ),
    );
  }
}