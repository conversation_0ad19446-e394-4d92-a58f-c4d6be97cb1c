import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/team_study_recruit/team_study_reruit_user_header.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/team_study_recruit/team_study_invite_partner_dialog.dart';
import 'package:jojo_flutter_plan_pkg/pages/find_study_partner/model/find_study_partner_model.dart';
import 'package:lottie/lottie.dart';

import '../../../generated/l10n.dart';
import '../../../static/img.dart';
import '../../../static/lottie.dart';
import '../../../static/svg.dart';

class TeamStudyRecruitWidget extends StatefulWidget {
  final VoidCallback? onAddMemberTap;

  const TeamStudyRecruitWidget({
    super.key,
    this.onAddMemberTap,
  });


  /// 显示邀请伙伴弹窗的示例方法
  /// 在实际使用时，将此方法作为 onAddMemberTap 回调传入
  static void showInvitePartnerDialog() {
    // 模拟伙伴数据，实际使用时从API获取
    List<FindStudyPartnerModel> mockPartners = [
      const FindStudyPartnerModel(
        nickName: '***勇士',
        continuousDays: 32,
        studyDays: 524,
        img: 'https://example.com/avatar1.jpg',
        partnerId: 1,
        partnerState: StudyPartnerState.canApply,
      ),
      const FindStudyPartnerModel(
        nickName: '蝴蝶小子',
        continuousDays: 32,
        studyDays: 524,
        img: 'https://example.com/avatar2.jpg',
        partnerId: 2,
        partnerState: StudyPartnerState.waitingAgreement,
      ),
      const FindStudyPartnerModel(
        nickName: '精灵泡泡',
        continuousDays: 32,
        studyDays: 524,
        img: 'https://example.com/avatar3.jpg',
        partnerId: 3,
        partnerState: StudyPartnerState.forbiddenApply,
      ),
    ];

    InvitePartnerDialog.show(
      partners: mockPartners,
      onInviteAll: () {
        // 一键邀请所有伙伴的逻辑
        print('一键邀请所有伙伴');
        // TODO: 实现一键邀请逻辑
      },
      onInvitePartner: (partner) {
        // 邀请单个伙伴的逻辑
        print('邀请伙伴: ${partner.nickName}');
        // TODO: 实现单个邀请逻辑
      },
      onClose: () {
        // 弹窗关闭的回调
        print('弹窗已关闭');
      },
    );
  }

  @override
  State<TeamStudyRecruitWidget> createState() => _TeamStudyRecruitWidgetState();
}

// 用户数据模型
class TeamMemberData {
  final bool isSelf;
  final String? userName;
  final int? continuousDays;
  final String? avatarUrl;

  TeamMemberData({
    required this.isSelf,
    this.userName,
    this.continuousDays,
    this.avatarUrl,
  });
}

class _TeamStudyRecruitWidgetState extends State<TeamStudyRecruitWidget> {
  // 控制手指引导显示
  bool _showFingerGuide = true;

  // 模拟数据，实际使用时替换为真实数据
  List<TeamMemberData> teamMembers = [
    TeamMemberData(
      isSelf: true,
      userName: '鳄鱼粑粑来了一起学习吧',
      continuousDays: 12,
    ),
    TeamMemberData(
      isSelf: false,
      userName: '鳄鱼粑粑来了一起学习吧',
      continuousDays: 999,
    ),
    // TeamMemberData(
    //   isSelf: false,
    //   userName: '鳄鱼粑粑来了一起学习吧',
    //   continuousDays: 998,
    // ),
    // 第三个位置为空，会显示添加按钮
  ];
  @override
  Widget build(BuildContext context) {
    final bottom = MediaQuery.of(context).viewPadding.bottom;
    var tips = S.of(context).teamStudyRecruitTipTwoLeft;
    if (teamMembers.length == 2) {
      tips = S.of(context).teamStudyRecruitTipOneLeft;
    } else if (teamMembers.length == 3) {
      tips = S.of(context).teamStudyRecruitTipComplete;
    }

    return Stack(
      children: [
        Positioned(
          left: 20.rdp,
          right: 20.rdp,
          bottom: 43.rdp + bottom,
          child: Container(
            // color: Colors.red,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                    context.dimensions.largeCornerRadius.rdp),
                border: Border.all(
                    width: 1.rdp, color: context.appColors.jColorGray3),
                color: Colors.white),
            child: Padding(
              padding: EdgeInsets.all(context.dimensions.mediumSpacing.rdp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Container(
                    height: 49.rdp,
                    padding: EdgeInsets.only(
                        left: context.dimensions.minimumSpacing.rdp),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          S.of(context).teamStudyRecruitTitle,
                          style: context.textstyles.headingLargeEmphasis.pf
                              .copyWith(
                            color: context.appColors.jColorGray6,
                          ),
                        ),
                        // 副标题
                        Text(
                          S.of(context).teamStudyRecruitDetail,
                          style: context.textstyles.remark.pf.copyWith(
                            color: context.appColors.jColorGray6,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: context.dimensions.mediumSpacing.rdp),
                  // 头像和添加按钮区域
                  Container(
                    height: 90.rdp,
                    padding: EdgeInsets.symmetric(
                        horizontal: (context.dimensions.mediumSpacing -
                                context.dimensions.minimumSpacing)
                            .rdp,
                        vertical: context.dimensions.minimumSpacing.rdp),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: _buildTeamMemberWidgets(),
                    ),
                  ),
                  SizedBox(height: context.dimensions.mediumSpacing.rdp),
                  // 底部按钮
                  Container(
                    width: double.infinity,
                    height: 40.rdp,
                    decoration: BoxDecoration(
                      color: context.appColors.jColorYellow1,
                      borderRadius: BorderRadius.circular(20.rdp),
                      border: Border.all(
                          width: 1.rdp, color: context.appColors.jColorYellow2),
                    ),
                    padding: EdgeInsets.symmetric(
                        horizontal: context.dimensions.mediumSpacing.rdp,
                        vertical: context.dimensions.smallSpacing.rdp),
                    child: teamMembers.length == 3
                        ? Row(
                            children: [
                              ImageAssetWeb(
                                assetName:
                                    AssetsImg.PLAN_PURE_ENJOY_STEP_FINISH,
                                width: 24.rdp,
                                height: 24.rdp,
                                package: RunEnv.package,
                              ),
                              SizedBox(
                                  width: context.dimensions.smallSpacing.rdp),
                              Text(
                                tips,
                                style: context.textstyles.bodyText.pf.copyWith(
                                    color: context.appColors.jColorYellow6),
                              ),
                            ],
                          )
                        : Text(
                            tips,
                            style: context.textstyles.bodyText.pf.copyWith(
                                color: context.appColors.jColorYellow6),
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildTeamMemberWidgets() {
    List<Widget> widgets = [];
    bool isFirstClickWidget = true; // 标记是否是第一个 ClickWidget

    for (int i = 0; i < 3; i++) {
      if (i < teamMembers.length) {
        final member = teamMembers[i];
        widgets.add(
          SizedBox(
            width: 88.rdp,
            height: 82.rdp,
            child: TeamStudyRecruitHeaderWidget(
              isSelf: member.isSelf,
              userName: member.userName,
              continuousDays: 0, // 目前不现实
              avatarUrl: member.avatarUrl,
            ),
          ),
        );
      } else {
        widgets.add(
          Stack(
            clipBehavior: Clip.none,
            children: [
              ClickWidget(
                type: ClickType.throttleWithTimeout,
                onTap: () {
                  if (_showFingerGuide) {
                    setState(() {
                      _showFingerGuide = false;
                    });
                  }
                  widget.onAddMemberTap?.call();
                },
                child: SizedBox(
                  width: 88.rdp,
                  height: 82.rdp,
                  child: Center(
                    child: SvgAssetWeb(
                      assetName: AssetsSvg.TEMP_STUDY_ADD,
                      height: 82.rdp,
                      width: 82.rdp,
                      package: RunEnv.package,
                    ),
                  ),
                ),
              ),
              if (isFirstClickWidget && _showFingerGuide)
                Positioned(
                  top: 5.rdp,
                  left: 15.rdp,
                  child: _fingerGuideWidget(),
                ),
            ],
          ),
        );
        isFirstClickWidget = false;
      }
    }

    return widgets;
  }

  Widget _fingerGuideWidget() {
    return Transform(
      transform: Matrix4.identity()..scale(-1.0, 1.0, 1.0),
      alignment: Alignment.center,
      child: IgnorePointer(
        ignoring: true,
        child: SizedBox(
            width: 100.rdp,
            height: 100.rdp,
            child: Lottie.asset(
              AssetsLottie.YEAR_FINGER_POINT,
              package: RunEnv.package,
              repeat: true,
            )),
      ),
    );
  }
}
