import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';

class TeamStudyRecruitHeaderWidget extends StatelessWidget {
  final bool isMine;
  final String? userName;
  final int? continuousDays;
  final String? avatarUrl;
  const TeamStudyRecruitHeaderWidget(
      {super.key,
      required this.isMine,
      this.userName,
      this.continuousDays,
      this.avatarUrl});

//   @override
//   State<TeamStudyRecruitHeaderWidget> createState() => _TeamStudyRecruitHeaderWidgetState();
// }

// class _TeamStudyRecruitHeaderWidgetState extends State<TeamStudyRecruitHeaderWidget> {
  @override
  Widget build(BuildContext context) {
    String name = userName ?? '';
    if (name.length > 6) {
      name = "${name.substring(0, 6)}...";
    }
    return Container(
      color: Colors.purple[200],
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildHeader(),
          Container(
            color: Colors.green,
            // width: double.infinity,
            height: 18.rdp,
            child: Text(
              name,
              textAlign: TextAlign.center,
              style: context.textstyles.smallestText
                  .copyWith(color: context.appColors.jColorGray6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Stack(
      children: [
        Positioned.fill(
          child: Container(
            color: Colors.red,
          ),
        ),
        Positioned(child: ContinuousDaysFire(continuousDays: 4, size: 24.rdp,)),
      ],
    );
  }
}