import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter/src/widgets/placeholder.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/incentive_module/home_incentive_info_widget.dart';
import 'package:path/path.dart';

import '../../learning_incentives/widget/alternative_image_widget.dart';

class TeamStudyRecruitHeaderWidget extends StatelessWidget {
  final bool isMine;
  final String? userName;
  final int? continuousDays;
  final String? avatarUrl;
  const TeamStudyRecruitHeaderWidget(
      {super.key,
      required this.isMine,
      this.userName,
      this.continuousDays,
      this.avatarUrl});

//   @override
//   State<TeamStudyRecruitHeaderWidget> createState() => _TeamStudyRecruitHeaderWidgetState();
// }

// class _TeamStudyRecruitHeaderWidgetState extends State<TeamStudyRecruitHeaderWidget> {
  @override
  Widget build(BuildContext context) {
    String name = userName ?? '';
    if (name.length > 6) {
      name = "${name.substring(0, 6)}...";
    }
    return Container(
      color: Colors.purple[200],
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildHeader(context),
          Container(
            color: Colors.green,
            // width: double.infinity,
            height: 18.rdp,
            child: Stack(clipBehavior: Clip.none,children: [
              Text(
              name,
              textAlign: TextAlign.center,
              style: context.textstyles.smallestText
                  .copyWith(color: context.appColors.jColorGray6),
            )
            ],),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return SizedBox(
      width: 60.rdp,
      height: 60.rdp,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                    color: context.appColors.jColorGray3, width: 1.rdp),
                color: hexToColor('#F5F4F4'),
                shape: BoxShape.circle,
              ),
              child: AlternativeImageWidget(
                imageUrl: avatarUrl ?? '',
                displayWidth: 58.rdp,
                displayHeight: 58.rdp,
                displayConfig: ImageDisplayConfig.head,
                hideDefaultHolder: true,
              ),
            ),
          ),
          Positioned(
              left: -2.rdp,
              bottom: -2.rdp,
              child: ContinuousDaysFire(
                continuousDays: 4,
                size: 24.rdp,
              )),
        ],
      ),
    );
  }
}
