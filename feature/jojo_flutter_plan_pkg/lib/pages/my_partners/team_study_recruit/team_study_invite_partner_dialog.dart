import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/widget/alternative_image_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

import '../../../static/img.dart';
import '../../find_study_partner/model/find_study_partner_model.dart';

class InvitePartnerDialog extends StatefulWidget {
  final List<FindStudyPartnerModel> partners;
  final VoidCallback? onInviteAll;
  final Function(FindStudyPartnerModel)? onInvitePartner;
  final VoidCallback? onClose;

  const InvitePartnerDialog({
    super.key,
    required this.partners,
    this.onInviteAll,
    this.onInvitePartner,
    this.onClose,
  });

  @override
  State<InvitePartnerDialog> createState() => _InvitePartnerDialogState();

  /// 显示邀请伙伴弹窗的静态方法
  static void show({
    required List<FindStudyPartnerModel> partners,
    VoidCallback? onInviteAll,
    Function(FindStudyPartnerModel)? onInvitePartner,
    VoidCallback? onClose,
  }) {
    if (partners.isEmpty) return;

    SmartDialog.show(
      builder: (context) => InvitePartnerDialog(
        partners: partners,
        onInviteAll: onInviteAll,
        onInvitePartner: onInvitePartner,
        onClose: onClose,
      ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withOpacity(0.7),
      clickMaskDismiss: false,
      backDismiss: true,
    );
  }
}

class _InvitePartnerDialogState extends State<InvitePartnerDialog> {
  @override
  Widget build(BuildContext context) {
    var mediaData = MediaQuery.of(context);
    var height = mediaData.viewPadding.bottom + 647.rdp;
    final top = 131.rdp;
    if (height > mediaData.size.height - top) {
      height = mediaData.size.height - top;
    }

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(32.rdp),
          topRight: Radius.circular(32.rdp),
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Column(
        children: [
          SizedBox(height: 28.rdp),
          _buildHeader(),
          _buildSubtitle(),
          Expanded(child: _buildPartnerList()),
          _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 30.rdp,
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Row(
        children: [
          Expanded(
            child: Center(
              child: Text(
                '邀请伙伴一起探索知识之海',
                style: context.textstyles.headingLargeEmphasis.pf.copyWith(
                  color: context.appColors.jColorGray6,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtitle() {
    return Container(
      height: 27.rdp,
      margin: EdgeInsets.only(top: context.dimensions.minimumSpacing.rdp),
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Text(
        '集齐三人领取奖励',
        style: context.textstyles.heading.pf.copyWith(
          color: context.appColors.jColorGray5,
        ),
      ),
    );
  }

  Widget _buildPartnerList() {
    if (widget.partners.isEmpty) {
      return _buildEmptyView();
    }

    final mediumSpacing = context.dimensions.mediumSpacing.rdp;

    return Container(
      margin: EdgeInsets.only(top: mediumSpacing),
      child: ListView.builder(
        itemCount: widget.partners.length,
        itemBuilder: (context, index) {
          final partner = widget.partners[index];
          return Container(
            margin: EdgeInsets.only(bottom: mediumSpacing),
            child: _buildPartnerItem(partner),
          );
        },
      ),
    );
  }

  Widget _buildPartnerItem(FindStudyPartnerModel partner) {
    return Container(
      height: 113.rdp,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius:
            BorderRadius.circular(context.dimensions.largeCornerRadius.rdp),
        border: Border.all(
          color: context.appColors.jColorGray3,
          width: 1.rdp,
        ),
      ),
      child: Stack(
        children: [
          _buildItemBg(),
          _buildAvatar(partner.img ?? ""),
          _buildContinuousDaysWidget(partner.continuousDays ?? 0),
          _buildNameWidget(partner.nickName ?? "",
              partner.partnerState == StudyPartnerState.forbiddenApply),
          _buildStudyDays(partner.studyDays ?? 0),
          _buildButton(partner),
        ],
      ),
    );
  }

  Widget _buildItemBg() {
    return Positioned(
      left: 0,
      top: 0,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.rdp),
        child: ImageAssetWeb(
          assetName: AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_BG,
          fit: BoxFit.contain,
          package: Config.package,
          height: 113.rdp,
          width: 233.rdp,
        ),
      ),
    );
  }

  Widget _buildAvatar(String avatar) {
    return Positioned(
      left: 16.rdp,
      top: 4.rdp,
      child: AlternativeImageWidget(
        imageUrl: avatar,
        displayWidth: 113.rdp,
        displayHeight: 100.rdp,
        displayConfig: ImageDisplayConfig.waist,
        hideDefaultHolder: true,
      ),
    );
  }

  Widget _buildContinuousDaysWidget(int continuousDays) {
    final size = 36.rdp;
    return Positioned(
      left: 28.rdp,
      bottom: 8.rdp,
      height: size,
      width: size,
      child: ContinuousDaysFire(continuousDays: continuousDays, size: size),
    );
  }

  Widget _buildNameWidget(String name, bool isMyPartner) {
    return Positioned(
      left: 165.rdp,
      top: context.dimensions.mediumSpacing.rdp,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            name,
            style: context.textstyles.bodyTextEmphasis.pf
                .copyWith(color: context.appColors.jColorGray6),
          ),
          if (isMyPartner)
            Padding(
              padding:
                  EdgeInsets.only(left: context.dimensions.minimumSpacing.rdp),
              child: Text(
                "互为学伴",
                style: context.textstyles.smallestText.pf
                    .copyWith(color: context.appColors.jColorGray4),
              ),
            )
        ],
      ),
    );
  }

  Widget _buildStudyDays(int days) {
    return Positioned(
      left: 165.rdp,
      top: 39.rdp,
      child: Text(
        S.of(context).studyDays(days),
        style: context.textstyles.remark.pf
            .copyWith(color: context.appColors.jColorGray4),
      ),
    );
  }

  Widget _buildButton(FindStudyPartnerModel partner) {
    if (partner.partnerState == StudyPartnerState.canApply) {
      return _buildInviteButton(partner);
    } else if (partner.partnerState == StudyPartnerState.waitingAgreement) {
      return _buildWaitingButton(partner);
    }
    return const SizedBox.shrink();
  }

  Widget _buildWaitingButton(FindStudyPartnerModel partner) {
    final spaceing = context.dimensions.mediumSpacing;
    return Positioned(
      right: spaceing.rdp,
      bottom: spaceing.rdp,
      width: 108.rdp,
      height: 32.rdp,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25.rdp),
          border:
              Border.all(color: context.appColors.jColorGray2, width: 1.rdp),
        ),
        child: Row(children: [
          SizedBox(width: context.dimensions.smallSpacing.rdp),
          SizedBox(
            width: 20.rdp,
            height: 20.rdp,
            child: Icon(Icons.hourglass_bottom,
                color: context.appColors.jColorYellow6, size: 16.rdp),
          ),
          SizedBox(width: context.dimensions.minimumSpacing.rdp),
          Text(
            '等待通过',
            style: context.textstyles.remark.pf
                .copyWith(color: context.appColors.jColorGray4),
          ),
        ]),
      ),
    );
  }

  Widget _buildInviteButton(FindStudyPartnerModel partner) {
    final spaceing = context.dimensions.mediumSpacing;
    return Positioned(
      right: spaceing.rdp,
      bottom: spaceing.rdp,
      width: 80.rdp,
      height: 32.rdp,
      child: GestureDetector(
        onTap: () => widget.onInvitePartner?.call(partner),
        child: Container(
          decoration: BoxDecoration(
            color: context.appColors.jColorYellow4,
            borderRadius: BorderRadius.circular(25.rdp),
          ),
          child: Row(children: [
            SizedBox(width: context.dimensions.smallSpacing.rdp),
            SizedBox(
              width: 20.rdp,
              height: 20.rdp,
              child: Icon(Icons.add,
                  color: context.appColors.jColorYellow6, size: 16.rdp),
            ),
            SizedBox(width: context.dimensions.minimumSpacing.rdp),
            Text(
              '邀请',
              style: context.textstyles.remark.pf
                  .copyWith(color: context.appColors.jColorYellow6),
            ),
          ]),
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ImageAssetWeb(
            assetName: AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_LIST_EMPTY,
            package: Config.package,
            height: 120.rdp,
            width: 120.rdp,
          ),
          SizedBox(height: 16.rdp),
          Text(
            '暂无可邀请的伙伴',
            style: TextStyle(
              fontSize: 16.rdp,
              color: context.appColors.jColorGray4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.only(
          top: 36.rdp, left: 20.rdp, right: 20.rdp, bottom: 16.rdp),
      child: Column(
        children: [
          // 一键邀请按钮
          GestureDetector(
            onTap: widget.onInviteAll,
            child: Container(
              width: double.infinity,
              height: 44.rdp,
              decoration: BoxDecoration(
                color: context.appColors.mainColor,
                borderRadius: BorderRadius.circular(24.rdp),
              ),
              child: Center(
                child: Text(
                  '一键邀请',
                  style: context.textstyles.headingEmphasis.pf
                      .copyWith(color: context.appColors.jColorYellow6),
                ),
              ),
            ),
          ),
          SizedBox(height: context.dimensions.smallSpacing.rdp),
          // 关闭弹窗按钮
          SizedBox(
            width: double.infinity,
            height: 44.rdp,
            child: GestureDetector(
              onTap: () {
                SmartDialog.dismiss();
                widget.onClose?.call();
              },
              child: Text(
                '关闭弹窗',
                style: context.textstyles.heading.pf
                    .copyWith(color: context.appColors.jColorGray5),
                    textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
