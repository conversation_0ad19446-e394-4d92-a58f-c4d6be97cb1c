import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/incentive/continuous_days_fire.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/widget/alternative_image_widget.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

import '../../../static/img.dart';
import '../../find_study_partner/model/find_study_partner_model.dart';

class InvitePartnerDialog extends StatefulWidget {
  final List<FindStudyPartnerModel> partners;
  final VoidCallback? onInviteAll;
  final Function(FindStudyPartnerModel)? onInvitePartner;
  final VoidCallback? onClose;

  const InvitePartnerDialog({
    super.key,
    required this.partners,
    this.onInviteAll,
    this.onInvitePartner,
    this.onClose,
  });

  @override
  State<InvitePartnerDialog> createState() => _InvitePartnerDialogState();

  /// 显示邀请伙伴弹窗的静态方法
  static void show({
    required List<FindStudyPartnerModel> partners,
    VoidCallback? onInviteAll,
    Function(FindStudyPartnerModel)? onInvitePartner,
    VoidCallback? onClose,
  }) {
    SmartDialog.show(
      builder: (context) => InvitePartnerDialog(
        partners: partners,
        onInviteAll: onInviteAll,
        onInvitePartner: onInvitePartner,
        onClose: onClose,
      ),
      alignment: Alignment.center,
      maskColor: Colors.black.withOpacity(0.5),
      clickMaskDismiss: true,
      backDismiss: true,
    );
  }
}

class _InvitePartnerDialogState extends State<InvitePartnerDialog> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 335.rdp,
      height: 600.rdp,
      margin: EdgeInsets.symmetric(horizontal: 20.rdp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.rdp),
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildSubtitle(),
          Expanded(child: _buildPartnerList()),
          _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 60.rdp,
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              SmartDialog.dismiss();
              widget.onClose?.call();
            },
            child: Icon(
              Icons.arrow_back_ios,
              size: 20.rdp,
              color: context.appColors.jColorGray6,
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                '邀请伙伴一起探索知识之海',
                style: TextStyle(
                  fontSize: 18.rdp,
                  fontWeight: FontWeight.w600,
                  color: context.appColors.jColorGray6,
                ),
              ),
            ),
          ),
          SizedBox(width: 20.rdp), // 占位，保持标题居中
        ],
      ),
    );
  }

  Widget _buildSubtitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.rdp),
      child: Text(
        '集齐三人领取奖励',
        style: TextStyle(
          fontSize: 14.rdp,
          color: context.appColors.jColorGray4,
        ),
      ),
    );
  }

  Widget _buildPartnerList() {
    if (widget.partners.isEmpty) {
      return _buildEmptyView();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.rdp, vertical: 20.rdp),
      child: ListView.builder(
        itemCount: widget.partners.length,
        itemBuilder: (context, index) {
          final partner = widget.partners[index];
          return Container(
            margin: EdgeInsets.only(bottom: 16.rdp),
            child: _buildPartnerItem(partner),
          );
        },
      ),
    );
  }

  Widget _buildPartnerItem(FindStudyPartnerModel partner) {
    return Container(
      height: 120.rdp,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.rdp),
        border: Border.all(
          color: context.appColors.jColorGray3,
          width: 1.rdp,
        ),
      ),
      child: Stack(
        children: [
          _buildItemBg(),
          _buildAvatar(partner.img ?? ""),
          _buildContinuousDaysWidget(partner.continuousDays ?? 0),
          _buildNameWidget(partner.nickName ?? ""),
          _buildStudyDays(partner.studyDays ?? 0),
          _buildInviteButton(partner),
        ],
      ),
    );
  }

  Widget _buildItemBg() {
    return Positioned(
      left: 0,
      top: 0,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.rdp),
        child: ImageAssetWeb(
          assetName: AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_ITEM_BG,
          fit: BoxFit.contain,
          package: Config.package,
          height: 113.rdp,
          width: 233.rdp,
        ),
      ),
    );
  }

  Widget _buildAvatar(String avatar) {
    return Positioned(
      left: 16.rdp,
      top: 4.rdp,
      child: AlternativeImageWidget(
        imageUrl: avatar,
        displayWidth: 113.rdp,
        displayHeight: 100.rdp,
        displayConfig: ImageDisplayConfig.waist,
      ),
    );
  }

  Widget _buildContinuousDaysWidget(int continuousDays) {
    final size = 36.rdp;
    return Positioned(
      left: 28.rdp,
      bottom: 8.rdp,
      height: size,
      width: size,
      child: ContinuousDaysFire(continuousDays: continuousDays, size: size),
    );
  }

  Widget _buildNameWidget(String name) {
    return Positioned(
      left: 165.rdp,
      top: 14.rdp,
      child: Text(
        name,
        style: TextStyle(
          fontSize: 16.rdp,
          color: context.appColors.jColorGray6,
          fontFamily: 'PingFang SC',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStudyDays(int days) {
    return Positioned(
      left: 165.rdp,
      top: 39.rdp,
      child: Text(
        S.of(context).studyDays(days),
        style: TextStyle(
          fontSize: 14.rdp,
          color: context.appColors.jColorGray4,
          fontFamily: 'PingFang SC',
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildInviteButton(FindStudyPartnerModel partner) {
    return Positioned(
      right: 10.rdp,
      bottom: 10.rdp,
      width: 60.rdp,
      height: 32.rdp,
      child: GestureDetector(
        onTap: () => widget.onInvitePartner?.call(partner),
        child: Container(
          decoration: BoxDecoration(
            color: context.appColors.jColorYellow4,
            borderRadius: BorderRadius.circular(16.rdp),
          ),
          child: Center(
            child: Text(
              '邀请',
              style: TextStyle(
                fontSize: 14.rdp,
                color: context.appColors.jColorYellow6,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ImageAssetWeb(
            assetName: AssetsImg.PLAN_FIND_PARTNER_FIND_PARTNER_LIST_EMPTY,
            package: Config.package,
            height: 120.rdp,
            width: 120.rdp,
          ),
          SizedBox(height: 16.rdp),
          Text(
            '暂无可邀请的伙伴',
            style: TextStyle(
              fontSize: 16.rdp,
              color: context.appColors.jColorGray4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.all(20.rdp),
      child: Column(
        children: [
          // 一键邀请按钮
          GestureDetector(
            onTap: widget.onInviteAll,
            child: Container(
              width: double.infinity,
              height: 48.rdp,
              decoration: BoxDecoration(
                color: context.appColors.jColorYellow4,
                borderRadius: BorderRadius.circular(24.rdp),
              ),
              child: Center(
                child: Text(
                  '一键邀请',
                  style: TextStyle(
                    fontSize: 16.rdp,
                    fontWeight: FontWeight.w600,
                    color: context.appColors.jColorYellow6,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 12.rdp),
          // 关闭弹窗按钮
          GestureDetector(
            onTap: () {
              SmartDialog.dismiss();
              widget.onClose?.call();
            },
            child: Text(
              '关闭弹窗',
              style: TextStyle(
                fontSize: 14.rdp,
                color: context.appColors.jColorGray4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
