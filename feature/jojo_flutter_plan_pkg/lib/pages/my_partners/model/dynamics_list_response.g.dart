// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dynamics_list_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_DynamicsListData _$$_DynamicsListDataFromJson(Map<String, dynamic> json) =>
    _$_DynamicsListData(
      dynamics: (json['dynamics'] as List<dynamic>?)
          ?.map((e) => DynamicItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      minId: json['minId'] as int?,
      size: json['size'] as int?,
    );

Map<String, dynamic> _$$_DynamicsListDataToJson(_$_DynamicsListData instance) =>
    <String, dynamic>{
      'dynamics': instance.dynamics,
      'minId': instance.minId,
      'size': instance.size,
    };

_$_DynamicItem _$$_DynamicItemFromJson(Map<String, dynamic> json) =>
    _$_DynamicItem(
      type: json['type'] as int?,
      nickName: json['nickName'] as String?,
      img: json['img'] as String?,
      url: json['url'] as String?,
      content: json['content'] as String?,
      actionState: json['actionState'] == null
          ? null
          : ActionState.fromJson(json['actionState'] as Map<String, dynamic>),
      timeDesc: json['timeDesc'] as String?,
      messageTime: json['messageTime'] as int?,
      dynamicRelationId: json['dynamicRelationId'] as int?,
      partnerId: json['partnerId'] as int?,
      subjectColor: json['subjectColor'] as String?,
      messageTypeName: json['messageTypeName'] as String?,
    );

Map<String, dynamic> _$$_DynamicItemToJson(_$_DynamicItem instance) =>
    <String, dynamic>{
      'type': instance.type,
      'nickName': instance.nickName,
      'img': instance.img,
      'url': instance.url,
      'content': instance.content,
      'actionState': instance.actionState,
      'timeDesc': instance.timeDesc,
      'messageTime': instance.messageTime,
      'dynamicRelationId': instance.dynamicRelationId,
      'partnerId': instance.partnerId,
      'subjectColor': instance.subjectColor,
      'messageTypeName': instance.messageTypeName,
    };

_$_ActionState _$$_ActionStateFromJson(Map<String, dynamic> json) =>
    _$_ActionState(
      poke: json['poke'] as int?,
      flower: json['flower'] as int?,
    );

Map<String, dynamic> _$$_ActionStateToJson(_$_ActionState instance) =>
    <String, dynamic>{
      'poke': instance.poke,
      'flower': instance.flower,
    };
