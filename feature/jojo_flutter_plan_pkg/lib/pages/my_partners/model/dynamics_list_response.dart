import 'package:freezed_annotation/freezed_annotation.dart';

part 'dynamics_list_response.freezed.dart';
part 'dynamics_list_response.g.dart';

@freezed
class DynamicsListData with _$DynamicsListData {
  const factory DynamicsListData({
    List<DynamicItem>? dynamics,
    int? minId,
    int? size,
  }) = _DynamicsListData;

  factory DynamicsListData.fromJson(Map<String, dynamic> json) =>
      _$DynamicsListDataFromJson(json);
}

@freezed
class DynamicItem with _$DynamicItem {
  const factory DynamicItem({
    int? type,
    String? nickName,
    String? img,
    String? url,
    String? content,
    ActionState? actionState,
    String? timeDesc,
    int? messageTime,
    int? dynamicRelationId,
    int? partnerId,
    String? subjectColor,
    String? messageTypeName,
  }) = _DynamicItem;

  factory DynamicItem.fromJson(Map<String, dynamic> json) =>
      _$DynamicItemFromJson(json);
}

@freezed
class ActionState with _$ActionState {
  const factory ActionState({
    int? poke, // 1:已戳一戳 0:未戳一戳
    int? flower, // 1:已送花花 0:未送花花
  }) = _ActionState;

  factory ActionState.fromJson(Map<String, dynamic> json) =>
      _$ActionStateFromJson(json);
}
