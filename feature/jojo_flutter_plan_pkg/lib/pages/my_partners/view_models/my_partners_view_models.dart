import 'package:freezed_annotation/freezed_annotation.dart';

part 'my_partners_view_models.freezed.dart';

/// 发现学伴 View 数据模型
@freezed
class DiscoverPartnersViewModel with _$DiscoverPartnersViewModel {
  const factory DiscoverPartnersViewModel({
    @Default([]) List<DiscoverPartnerViewModel> partnerList,
    @Default('') String sectionTitle,
    @Default('') String moreButtonText,
    String? jumpRoute,
  }) = _DiscoverPartnersViewModel;
}

/// 发现学伴项 View 数据模型
@freezed
class DiscoverPartnerViewModel with _$DiscoverPartnerViewModel {
  const factory DiscoverPartnerViewModel({
    @Default('') String displayName,
    @Default('') String avatarUrl,
    @Default(0) int partnerId,
    int? continuousDays,
    int? totalStudyDays,
    String? profileUrl,
  }) = _DiscoverPartnerViewModel;
}

/// 动态项 View 数据模型
@freezed
class DynamicViewModel with _$DynamicViewModel {
  const factory DynamicViewModel({
    @Default('') String userName,
    @Default('') String userAvatar,
    @Default('') String dynamicContent,
    @Default('') String publishTime,
    @Default(0) int dynamicId,
    @Default(0) int userId,
    @Default(DynamicActionViewModel()) DynamicActionViewModel actionState,
    int? dynamicType,
    String? userProfileUrl,
    String? subjectColor,
    String? messageTypeName,
  }) = _DynamicViewModel;
}

/// 动态操作状态 View 数据模型
@freezed
class DynamicActionViewModel with _$DynamicActionViewModel {
  const factory DynamicActionViewModel({
    @Default(false) bool hasPoked, // 是否已戳一戳
    @Default(false) bool hasGivenFlower, // 是否已送花花
  }) = _DynamicActionViewModel;
}

/// 学伴项 View 数据模型
@freezed
class PartnerViewModel with _$PartnerViewModel {
  const factory PartnerViewModel({
    @Default('') String partnerName,
    @Default('') String partnerAvatar,
    @Default(0) int partnerId,
    String? profileUrl,
  }) = _PartnerViewModel;
}

/// 发现学伴列表 View 数据模型
@freezed
class DiscoverPartnersListViewModel with _$DiscoverPartnersListViewModel {
  const factory DiscoverPartnersListViewModel({
    @Default([]) List<DiscoverPartnerViewModel> partners,
    @Default('') String title,
    @Default('') String viewMoreText,
    String? jumpRoute,
  }) = _DiscoverPartnersListViewModel;
}

/// 动态列表 View 数据模型
@freezed
class DynamicsListViewModel with _$DynamicsListViewModel {
  const factory DynamicsListViewModel({
    @Default([]) List<DynamicViewModel> dynamics,
    int? nextPageId,
    int? pageSize,
  }) = _DynamicsListViewModel;
}

/// 学伴列表 View 数据模型
@freezed
class PartnersListViewModel with _$PartnersListViewModel {
  const factory PartnersListViewModel({
    @Default([]) List<PartnerViewModel> partners,
    int? nextPageId,
    int? pageSize,
  }) = _PartnersListViewModel;
}
