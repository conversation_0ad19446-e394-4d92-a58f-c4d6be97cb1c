// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'my_partners_view_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DiscoverPartnersViewModel {
  List<DiscoverPartnerViewModel> get partnerList =>
      throw _privateConstructorUsedError;
  String get sectionTitle => throw _privateConstructorUsedError;
  String get moreButtonText => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DiscoverPartnersViewModelCopyWith<DiscoverPartnersViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoverPartnersViewModelCopyWith<$Res> {
  factory $DiscoverPartnersViewModelCopyWith(DiscoverPartnersViewModel value,
          $Res Function(DiscoverPartnersViewModel) then) =
      _$DiscoverPartnersViewModelCopyWithImpl<$Res, DiscoverPartnersViewModel>;
  @useResult
  $Res call(
      {List<DiscoverPartnerViewModel> partnerList,
      String sectionTitle,
      String moreButtonText,
      String? jumpRoute});
}

/// @nodoc
class _$DiscoverPartnersViewModelCopyWithImpl<$Res,
        $Val extends DiscoverPartnersViewModel>
    implements $DiscoverPartnersViewModelCopyWith<$Res> {
  _$DiscoverPartnersViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partnerList = null,
    Object? sectionTitle = null,
    Object? moreButtonText = null,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      partnerList: null == partnerList
          ? _value.partnerList
          : partnerList // ignore: cast_nullable_to_non_nullable
              as List<DiscoverPartnerViewModel>,
      sectionTitle: null == sectionTitle
          ? _value.sectionTitle
          : sectionTitle // ignore: cast_nullable_to_non_nullable
              as String,
      moreButtonText: null == moreButtonText
          ? _value.moreButtonText
          : moreButtonText // ignore: cast_nullable_to_non_nullable
              as String,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DiscoverPartnersViewModelCopyWith<$Res>
    implements $DiscoverPartnersViewModelCopyWith<$Res> {
  factory _$$_DiscoverPartnersViewModelCopyWith(
          _$_DiscoverPartnersViewModel value,
          $Res Function(_$_DiscoverPartnersViewModel) then) =
      __$$_DiscoverPartnersViewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<DiscoverPartnerViewModel> partnerList,
      String sectionTitle,
      String moreButtonText,
      String? jumpRoute});
}

/// @nodoc
class __$$_DiscoverPartnersViewModelCopyWithImpl<$Res>
    extends _$DiscoverPartnersViewModelCopyWithImpl<$Res,
        _$_DiscoverPartnersViewModel>
    implements _$$_DiscoverPartnersViewModelCopyWith<$Res> {
  __$$_DiscoverPartnersViewModelCopyWithImpl(
      _$_DiscoverPartnersViewModel _value,
      $Res Function(_$_DiscoverPartnersViewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partnerList = null,
    Object? sectionTitle = null,
    Object? moreButtonText = null,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_DiscoverPartnersViewModel(
      partnerList: null == partnerList
          ? _value._partnerList
          : partnerList // ignore: cast_nullable_to_non_nullable
              as List<DiscoverPartnerViewModel>,
      sectionTitle: null == sectionTitle
          ? _value.sectionTitle
          : sectionTitle // ignore: cast_nullable_to_non_nullable
              as String,
      moreButtonText: null == moreButtonText
          ? _value.moreButtonText
          : moreButtonText // ignore: cast_nullable_to_non_nullable
              as String,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_DiscoverPartnersViewModel implements _DiscoverPartnersViewModel {
  const _$_DiscoverPartnersViewModel(
      {final List<DiscoverPartnerViewModel> partnerList = const [],
      this.sectionTitle = '',
      this.moreButtonText = '',
      this.jumpRoute})
      : _partnerList = partnerList;

  final List<DiscoverPartnerViewModel> _partnerList;
  @override
  @JsonKey()
  List<DiscoverPartnerViewModel> get partnerList {
    if (_partnerList is EqualUnmodifiableListView) return _partnerList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_partnerList);
  }

  @override
  @JsonKey()
  final String sectionTitle;
  @override
  @JsonKey()
  final String moreButtonText;
  @override
  final String? jumpRoute;

  @override
  String toString() {
    return 'DiscoverPartnersViewModel(partnerList: $partnerList, sectionTitle: $sectionTitle, moreButtonText: $moreButtonText, jumpRoute: $jumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DiscoverPartnersViewModel &&
            const DeepCollectionEquality()
                .equals(other._partnerList, _partnerList) &&
            (identical(other.sectionTitle, sectionTitle) ||
                other.sectionTitle == sectionTitle) &&
            (identical(other.moreButtonText, moreButtonText) ||
                other.moreButtonText == moreButtonText) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_partnerList),
      sectionTitle,
      moreButtonText,
      jumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DiscoverPartnersViewModelCopyWith<_$_DiscoverPartnersViewModel>
      get copyWith => __$$_DiscoverPartnersViewModelCopyWithImpl<
          _$_DiscoverPartnersViewModel>(this, _$identity);
}

abstract class _DiscoverPartnersViewModel implements DiscoverPartnersViewModel {
  const factory _DiscoverPartnersViewModel(
      {final List<DiscoverPartnerViewModel> partnerList,
      final String sectionTitle,
      final String moreButtonText,
      final String? jumpRoute}) = _$_DiscoverPartnersViewModel;

  @override
  List<DiscoverPartnerViewModel> get partnerList;
  @override
  String get sectionTitle;
  @override
  String get moreButtonText;
  @override
  String? get jumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_DiscoverPartnersViewModelCopyWith<_$_DiscoverPartnersViewModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DiscoverPartnerViewModel {
  String get displayName => throw _privateConstructorUsedError;
  String get avatarUrl => throw _privateConstructorUsedError;
  int get partnerId => throw _privateConstructorUsedError;
  int? get continuousDays => throw _privateConstructorUsedError;
  int? get totalStudyDays => throw _privateConstructorUsedError;
  String? get profileUrl => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DiscoverPartnerViewModelCopyWith<DiscoverPartnerViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoverPartnerViewModelCopyWith<$Res> {
  factory $DiscoverPartnerViewModelCopyWith(DiscoverPartnerViewModel value,
          $Res Function(DiscoverPartnerViewModel) then) =
      _$DiscoverPartnerViewModelCopyWithImpl<$Res, DiscoverPartnerViewModel>;
  @useResult
  $Res call(
      {String displayName,
      String avatarUrl,
      int partnerId,
      int? continuousDays,
      int? totalStudyDays,
      String? profileUrl});
}

/// @nodoc
class _$DiscoverPartnerViewModelCopyWithImpl<$Res,
        $Val extends DiscoverPartnerViewModel>
    implements $DiscoverPartnerViewModelCopyWith<$Res> {
  _$DiscoverPartnerViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayName = null,
    Object? avatarUrl = null,
    Object? partnerId = null,
    Object? continuousDays = freezed,
    Object? totalStudyDays = freezed,
    Object? profileUrl = freezed,
  }) {
    return _then(_value.copyWith(
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      avatarUrl: null == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String,
      partnerId: null == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      totalStudyDays: freezed == totalStudyDays
          ? _value.totalStudyDays
          : totalStudyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      profileUrl: freezed == profileUrl
          ? _value.profileUrl
          : profileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DiscoverPartnerViewModelCopyWith<$Res>
    implements $DiscoverPartnerViewModelCopyWith<$Res> {
  factory _$$_DiscoverPartnerViewModelCopyWith(
          _$_DiscoverPartnerViewModel value,
          $Res Function(_$_DiscoverPartnerViewModel) then) =
      __$$_DiscoverPartnerViewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String displayName,
      String avatarUrl,
      int partnerId,
      int? continuousDays,
      int? totalStudyDays,
      String? profileUrl});
}

/// @nodoc
class __$$_DiscoverPartnerViewModelCopyWithImpl<$Res>
    extends _$DiscoverPartnerViewModelCopyWithImpl<$Res,
        _$_DiscoverPartnerViewModel>
    implements _$$_DiscoverPartnerViewModelCopyWith<$Res> {
  __$$_DiscoverPartnerViewModelCopyWithImpl(_$_DiscoverPartnerViewModel _value,
      $Res Function(_$_DiscoverPartnerViewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayName = null,
    Object? avatarUrl = null,
    Object? partnerId = null,
    Object? continuousDays = freezed,
    Object? totalStudyDays = freezed,
    Object? profileUrl = freezed,
  }) {
    return _then(_$_DiscoverPartnerViewModel(
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      avatarUrl: null == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String,
      partnerId: null == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int,
      continuousDays: freezed == continuousDays
          ? _value.continuousDays
          : continuousDays // ignore: cast_nullable_to_non_nullable
              as int?,
      totalStudyDays: freezed == totalStudyDays
          ? _value.totalStudyDays
          : totalStudyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      profileUrl: freezed == profileUrl
          ? _value.profileUrl
          : profileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_DiscoverPartnerViewModel implements _DiscoverPartnerViewModel {
  const _$_DiscoverPartnerViewModel(
      {this.displayName = '',
      this.avatarUrl = '',
      this.partnerId = 0,
      this.continuousDays,
      this.totalStudyDays,
      this.profileUrl});

  @override
  @JsonKey()
  final String displayName;
  @override
  @JsonKey()
  final String avatarUrl;
  @override
  @JsonKey()
  final int partnerId;
  @override
  final int? continuousDays;
  @override
  final int? totalStudyDays;
  @override
  final String? profileUrl;

  @override
  String toString() {
    return 'DiscoverPartnerViewModel(displayName: $displayName, avatarUrl: $avatarUrl, partnerId: $partnerId, continuousDays: $continuousDays, totalStudyDays: $totalStudyDays, profileUrl: $profileUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DiscoverPartnerViewModel &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.partnerId, partnerId) ||
                other.partnerId == partnerId) &&
            (identical(other.continuousDays, continuousDays) ||
                other.continuousDays == continuousDays) &&
            (identical(other.totalStudyDays, totalStudyDays) ||
                other.totalStudyDays == totalStudyDays) &&
            (identical(other.profileUrl, profileUrl) ||
                other.profileUrl == profileUrl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, displayName, avatarUrl,
      partnerId, continuousDays, totalStudyDays, profileUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DiscoverPartnerViewModelCopyWith<_$_DiscoverPartnerViewModel>
      get copyWith => __$$_DiscoverPartnerViewModelCopyWithImpl<
          _$_DiscoverPartnerViewModel>(this, _$identity);
}

abstract class _DiscoverPartnerViewModel implements DiscoverPartnerViewModel {
  const factory _DiscoverPartnerViewModel(
      {final String displayName,
      final String avatarUrl,
      final int partnerId,
      final int? continuousDays,
      final int? totalStudyDays,
      final String? profileUrl}) = _$_DiscoverPartnerViewModel;

  @override
  String get displayName;
  @override
  String get avatarUrl;
  @override
  int get partnerId;
  @override
  int? get continuousDays;
  @override
  int? get totalStudyDays;
  @override
  String? get profileUrl;
  @override
  @JsonKey(ignore: true)
  _$$_DiscoverPartnerViewModelCopyWith<_$_DiscoverPartnerViewModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DynamicViewModel {
  String get userName => throw _privateConstructorUsedError;
  String get userAvatar => throw _privateConstructorUsedError;
  String get dynamicContent => throw _privateConstructorUsedError;
  String get publishTime => throw _privateConstructorUsedError;
  int get dynamicId => throw _privateConstructorUsedError;
  int get userId => throw _privateConstructorUsedError;
  DynamicActionViewModel get actionState => throw _privateConstructorUsedError;
  int? get dynamicType => throw _privateConstructorUsedError;
  String? get userProfileUrl => throw _privateConstructorUsedError;
  String? get subjectColor => throw _privateConstructorUsedError;
  String? get messageTypeName => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DynamicViewModelCopyWith<DynamicViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DynamicViewModelCopyWith<$Res> {
  factory $DynamicViewModelCopyWith(
          DynamicViewModel value, $Res Function(DynamicViewModel) then) =
      _$DynamicViewModelCopyWithImpl<$Res, DynamicViewModel>;
  @useResult
  $Res call(
      {String userName,
      String userAvatar,
      String dynamicContent,
      String publishTime,
      int dynamicId,
      int userId,
      DynamicActionViewModel actionState,
      int? dynamicType,
      String? userProfileUrl,
      String? subjectColor,
      String? messageTypeName});

  $DynamicActionViewModelCopyWith<$Res> get actionState;
}

/// @nodoc
class _$DynamicViewModelCopyWithImpl<$Res, $Val extends DynamicViewModel>
    implements $DynamicViewModelCopyWith<$Res> {
  _$DynamicViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userName = null,
    Object? userAvatar = null,
    Object? dynamicContent = null,
    Object? publishTime = null,
    Object? dynamicId = null,
    Object? userId = null,
    Object? actionState = null,
    Object? dynamicType = freezed,
    Object? userProfileUrl = freezed,
    Object? subjectColor = freezed,
    Object? messageTypeName = freezed,
  }) {
    return _then(_value.copyWith(
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      userAvatar: null == userAvatar
          ? _value.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String,
      dynamicContent: null == dynamicContent
          ? _value.dynamicContent
          : dynamicContent // ignore: cast_nullable_to_non_nullable
              as String,
      publishTime: null == publishTime
          ? _value.publishTime
          : publishTime // ignore: cast_nullable_to_non_nullable
              as String,
      dynamicId: null == dynamicId
          ? _value.dynamicId
          : dynamicId // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      actionState: null == actionState
          ? _value.actionState
          : actionState // ignore: cast_nullable_to_non_nullable
              as DynamicActionViewModel,
      dynamicType: freezed == dynamicType
          ? _value.dynamicType
          : dynamicType // ignore: cast_nullable_to_non_nullable
              as int?,
      userProfileUrl: freezed == userProfileUrl
          ? _value.userProfileUrl
          : userProfileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      messageTypeName: freezed == messageTypeName
          ? _value.messageTypeName
          : messageTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $DynamicActionViewModelCopyWith<$Res> get actionState {
    return $DynamicActionViewModelCopyWith<$Res>(_value.actionState, (value) {
      return _then(_value.copyWith(actionState: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_DynamicViewModelCopyWith<$Res>
    implements $DynamicViewModelCopyWith<$Res> {
  factory _$$_DynamicViewModelCopyWith(
          _$_DynamicViewModel value, $Res Function(_$_DynamicViewModel) then) =
      __$$_DynamicViewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String userName,
      String userAvatar,
      String dynamicContent,
      String publishTime,
      int dynamicId,
      int userId,
      DynamicActionViewModel actionState,
      int? dynamicType,
      String? userProfileUrl,
      String? subjectColor,
      String? messageTypeName});

  @override
  $DynamicActionViewModelCopyWith<$Res> get actionState;
}

/// @nodoc
class __$$_DynamicViewModelCopyWithImpl<$Res>
    extends _$DynamicViewModelCopyWithImpl<$Res, _$_DynamicViewModel>
    implements _$$_DynamicViewModelCopyWith<$Res> {
  __$$_DynamicViewModelCopyWithImpl(
      _$_DynamicViewModel _value, $Res Function(_$_DynamicViewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userName = null,
    Object? userAvatar = null,
    Object? dynamicContent = null,
    Object? publishTime = null,
    Object? dynamicId = null,
    Object? userId = null,
    Object? actionState = null,
    Object? dynamicType = freezed,
    Object? userProfileUrl = freezed,
    Object? subjectColor = freezed,
    Object? messageTypeName = freezed,
  }) {
    return _then(_$_DynamicViewModel(
      userName: null == userName
          ? _value.userName
          : userName // ignore: cast_nullable_to_non_nullable
              as String,
      userAvatar: null == userAvatar
          ? _value.userAvatar
          : userAvatar // ignore: cast_nullable_to_non_nullable
              as String,
      dynamicContent: null == dynamicContent
          ? _value.dynamicContent
          : dynamicContent // ignore: cast_nullable_to_non_nullable
              as String,
      publishTime: null == publishTime
          ? _value.publishTime
          : publishTime // ignore: cast_nullable_to_non_nullable
              as String,
      dynamicId: null == dynamicId
          ? _value.dynamicId
          : dynamicId // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int,
      actionState: null == actionState
          ? _value.actionState
          : actionState // ignore: cast_nullable_to_non_nullable
              as DynamicActionViewModel,
      dynamicType: freezed == dynamicType
          ? _value.dynamicType
          : dynamicType // ignore: cast_nullable_to_non_nullable
              as int?,
      userProfileUrl: freezed == userProfileUrl
          ? _value.userProfileUrl
          : userProfileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      subjectColor: freezed == subjectColor
          ? _value.subjectColor
          : subjectColor // ignore: cast_nullable_to_non_nullable
              as String?,
      messageTypeName: freezed == messageTypeName
          ? _value.messageTypeName
          : messageTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_DynamicViewModel implements _DynamicViewModel {
  const _$_DynamicViewModel(
      {this.userName = '',
      this.userAvatar = '',
      this.dynamicContent = '',
      this.publishTime = '',
      this.dynamicId = 0,
      this.userId = 0,
      this.actionState = const DynamicActionViewModel(),
      this.dynamicType,
      this.userProfileUrl,
      this.subjectColor,
      this.messageTypeName});

  @override
  @JsonKey()
  final String userName;
  @override
  @JsonKey()
  final String userAvatar;
  @override
  @JsonKey()
  final String dynamicContent;
  @override
  @JsonKey()
  final String publishTime;
  @override
  @JsonKey()
  final int dynamicId;
  @override
  @JsonKey()
  final int userId;
  @override
  @JsonKey()
  final DynamicActionViewModel actionState;
  @override
  final int? dynamicType;
  @override
  final String? userProfileUrl;
  @override
  final String? subjectColor;
  @override
  final String? messageTypeName;

  @override
  String toString() {
    return 'DynamicViewModel(userName: $userName, userAvatar: $userAvatar, dynamicContent: $dynamicContent, publishTime: $publishTime, dynamicId: $dynamicId, userId: $userId, actionState: $actionState, dynamicType: $dynamicType, userProfileUrl: $userProfileUrl, subjectColor: $subjectColor, messageTypeName: $messageTypeName)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DynamicViewModel &&
            (identical(other.userName, userName) ||
                other.userName == userName) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.dynamicContent, dynamicContent) ||
                other.dynamicContent == dynamicContent) &&
            (identical(other.publishTime, publishTime) ||
                other.publishTime == publishTime) &&
            (identical(other.dynamicId, dynamicId) ||
                other.dynamicId == dynamicId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.actionState, actionState) ||
                other.actionState == actionState) &&
            (identical(other.dynamicType, dynamicType) ||
                other.dynamicType == dynamicType) &&
            (identical(other.userProfileUrl, userProfileUrl) ||
                other.userProfileUrl == userProfileUrl) &&
            (identical(other.subjectColor, subjectColor) ||
                other.subjectColor == subjectColor) &&
            (identical(other.messageTypeName, messageTypeName) ||
                other.messageTypeName == messageTypeName));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      userName,
      userAvatar,
      dynamicContent,
      publishTime,
      dynamicId,
      userId,
      actionState,
      dynamicType,
      userProfileUrl,
      subjectColor,
      messageTypeName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DynamicViewModelCopyWith<_$_DynamicViewModel> get copyWith =>
      __$$_DynamicViewModelCopyWithImpl<_$_DynamicViewModel>(this, _$identity);
}

abstract class _DynamicViewModel implements DynamicViewModel {
  const factory _DynamicViewModel(
      {final String userName,
      final String userAvatar,
      final String dynamicContent,
      final String publishTime,
      final int dynamicId,
      final int userId,
      final DynamicActionViewModel actionState,
      final int? dynamicType,
      final String? userProfileUrl,
      final String? subjectColor,
      final String? messageTypeName}) = _$_DynamicViewModel;

  @override
  String get userName;
  @override
  String get userAvatar;
  @override
  String get dynamicContent;
  @override
  String get publishTime;
  @override
  int get dynamicId;
  @override
  int get userId;
  @override
  DynamicActionViewModel get actionState;
  @override
  int? get dynamicType;
  @override
  String? get userProfileUrl;
  @override
  String? get subjectColor;
  @override
  String? get messageTypeName;
  @override
  @JsonKey(ignore: true)
  _$$_DynamicViewModelCopyWith<_$_DynamicViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DynamicActionViewModel {
  bool get hasPoked => throw _privateConstructorUsedError; // 是否已戳一戳
  bool get hasGivenFlower => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DynamicActionViewModelCopyWith<DynamicActionViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DynamicActionViewModelCopyWith<$Res> {
  factory $DynamicActionViewModelCopyWith(DynamicActionViewModel value,
          $Res Function(DynamicActionViewModel) then) =
      _$DynamicActionViewModelCopyWithImpl<$Res, DynamicActionViewModel>;
  @useResult
  $Res call({bool hasPoked, bool hasGivenFlower});
}

/// @nodoc
class _$DynamicActionViewModelCopyWithImpl<$Res,
        $Val extends DynamicActionViewModel>
    implements $DynamicActionViewModelCopyWith<$Res> {
  _$DynamicActionViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasPoked = null,
    Object? hasGivenFlower = null,
  }) {
    return _then(_value.copyWith(
      hasPoked: null == hasPoked
          ? _value.hasPoked
          : hasPoked // ignore: cast_nullable_to_non_nullable
              as bool,
      hasGivenFlower: null == hasGivenFlower
          ? _value.hasGivenFlower
          : hasGivenFlower // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DynamicActionViewModelCopyWith<$Res>
    implements $DynamicActionViewModelCopyWith<$Res> {
  factory _$$_DynamicActionViewModelCopyWith(_$_DynamicActionViewModel value,
          $Res Function(_$_DynamicActionViewModel) then) =
      __$$_DynamicActionViewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool hasPoked, bool hasGivenFlower});
}

/// @nodoc
class __$$_DynamicActionViewModelCopyWithImpl<$Res>
    extends _$DynamicActionViewModelCopyWithImpl<$Res,
        _$_DynamicActionViewModel>
    implements _$$_DynamicActionViewModelCopyWith<$Res> {
  __$$_DynamicActionViewModelCopyWithImpl(_$_DynamicActionViewModel _value,
      $Res Function(_$_DynamicActionViewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasPoked = null,
    Object? hasGivenFlower = null,
  }) {
    return _then(_$_DynamicActionViewModel(
      hasPoked: null == hasPoked
          ? _value.hasPoked
          : hasPoked // ignore: cast_nullable_to_non_nullable
              as bool,
      hasGivenFlower: null == hasGivenFlower
          ? _value.hasGivenFlower
          : hasGivenFlower // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$_DynamicActionViewModel implements _DynamicActionViewModel {
  const _$_DynamicActionViewModel(
      {this.hasPoked = false, this.hasGivenFlower = false});

  @override
  @JsonKey()
  final bool hasPoked;
// 是否已戳一戳
  @override
  @JsonKey()
  final bool hasGivenFlower;

  @override
  String toString() {
    return 'DynamicActionViewModel(hasPoked: $hasPoked, hasGivenFlower: $hasGivenFlower)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DynamicActionViewModel &&
            (identical(other.hasPoked, hasPoked) ||
                other.hasPoked == hasPoked) &&
            (identical(other.hasGivenFlower, hasGivenFlower) ||
                other.hasGivenFlower == hasGivenFlower));
  }

  @override
  int get hashCode => Object.hash(runtimeType, hasPoked, hasGivenFlower);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DynamicActionViewModelCopyWith<_$_DynamicActionViewModel> get copyWith =>
      __$$_DynamicActionViewModelCopyWithImpl<_$_DynamicActionViewModel>(
          this, _$identity);
}

abstract class _DynamicActionViewModel implements DynamicActionViewModel {
  const factory _DynamicActionViewModel(
      {final bool hasPoked,
      final bool hasGivenFlower}) = _$_DynamicActionViewModel;

  @override
  bool get hasPoked;
  @override // 是否已戳一戳
  bool get hasGivenFlower;
  @override
  @JsonKey(ignore: true)
  _$$_DynamicActionViewModelCopyWith<_$_DynamicActionViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PartnerViewModel {
  String get partnerName => throw _privateConstructorUsedError;
  String get partnerAvatar => throw _privateConstructorUsedError;
  int get partnerId => throw _privateConstructorUsedError;
  String? get profileUrl => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PartnerViewModelCopyWith<PartnerViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PartnerViewModelCopyWith<$Res> {
  factory $PartnerViewModelCopyWith(
          PartnerViewModel value, $Res Function(PartnerViewModel) then) =
      _$PartnerViewModelCopyWithImpl<$Res, PartnerViewModel>;
  @useResult
  $Res call(
      {String partnerName,
      String partnerAvatar,
      int partnerId,
      String? profileUrl});
}

/// @nodoc
class _$PartnerViewModelCopyWithImpl<$Res, $Val extends PartnerViewModel>
    implements $PartnerViewModelCopyWith<$Res> {
  _$PartnerViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partnerName = null,
    Object? partnerAvatar = null,
    Object? partnerId = null,
    Object? profileUrl = freezed,
  }) {
    return _then(_value.copyWith(
      partnerName: null == partnerName
          ? _value.partnerName
          : partnerName // ignore: cast_nullable_to_non_nullable
              as String,
      partnerAvatar: null == partnerAvatar
          ? _value.partnerAvatar
          : partnerAvatar // ignore: cast_nullable_to_non_nullable
              as String,
      partnerId: null == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int,
      profileUrl: freezed == profileUrl
          ? _value.profileUrl
          : profileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PartnerViewModelCopyWith<$Res>
    implements $PartnerViewModelCopyWith<$Res> {
  factory _$$_PartnerViewModelCopyWith(
          _$_PartnerViewModel value, $Res Function(_$_PartnerViewModel) then) =
      __$$_PartnerViewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String partnerName,
      String partnerAvatar,
      int partnerId,
      String? profileUrl});
}

/// @nodoc
class __$$_PartnerViewModelCopyWithImpl<$Res>
    extends _$PartnerViewModelCopyWithImpl<$Res, _$_PartnerViewModel>
    implements _$$_PartnerViewModelCopyWith<$Res> {
  __$$_PartnerViewModelCopyWithImpl(
      _$_PartnerViewModel _value, $Res Function(_$_PartnerViewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partnerName = null,
    Object? partnerAvatar = null,
    Object? partnerId = null,
    Object? profileUrl = freezed,
  }) {
    return _then(_$_PartnerViewModel(
      partnerName: null == partnerName
          ? _value.partnerName
          : partnerName // ignore: cast_nullable_to_non_nullable
              as String,
      partnerAvatar: null == partnerAvatar
          ? _value.partnerAvatar
          : partnerAvatar // ignore: cast_nullable_to_non_nullable
              as String,
      partnerId: null == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as int,
      profileUrl: freezed == profileUrl
          ? _value.profileUrl
          : profileUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_PartnerViewModel implements _PartnerViewModel {
  const _$_PartnerViewModel(
      {this.partnerName = '',
      this.partnerAvatar = '',
      this.partnerId = 0,
      this.profileUrl});

  @override
  @JsonKey()
  final String partnerName;
  @override
  @JsonKey()
  final String partnerAvatar;
  @override
  @JsonKey()
  final int partnerId;
  @override
  final String? profileUrl;

  @override
  String toString() {
    return 'PartnerViewModel(partnerName: $partnerName, partnerAvatar: $partnerAvatar, partnerId: $partnerId, profileUrl: $profileUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PartnerViewModel &&
            (identical(other.partnerName, partnerName) ||
                other.partnerName == partnerName) &&
            (identical(other.partnerAvatar, partnerAvatar) ||
                other.partnerAvatar == partnerAvatar) &&
            (identical(other.partnerId, partnerId) ||
                other.partnerId == partnerId) &&
            (identical(other.profileUrl, profileUrl) ||
                other.profileUrl == profileUrl));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, partnerName, partnerAvatar, partnerId, profileUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PartnerViewModelCopyWith<_$_PartnerViewModel> get copyWith =>
      __$$_PartnerViewModelCopyWithImpl<_$_PartnerViewModel>(this, _$identity);
}

abstract class _PartnerViewModel implements PartnerViewModel {
  const factory _PartnerViewModel(
      {final String partnerName,
      final String partnerAvatar,
      final int partnerId,
      final String? profileUrl}) = _$_PartnerViewModel;

  @override
  String get partnerName;
  @override
  String get partnerAvatar;
  @override
  int get partnerId;
  @override
  String? get profileUrl;
  @override
  @JsonKey(ignore: true)
  _$$_PartnerViewModelCopyWith<_$_PartnerViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DiscoverPartnersListViewModel {
  List<DiscoverPartnerViewModel> get partners =>
      throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get viewMoreText => throw _privateConstructorUsedError;
  String? get jumpRoute => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DiscoverPartnersListViewModelCopyWith<DiscoverPartnersListViewModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscoverPartnersListViewModelCopyWith<$Res> {
  factory $DiscoverPartnersListViewModelCopyWith(
          DiscoverPartnersListViewModel value,
          $Res Function(DiscoverPartnersListViewModel) then) =
      _$DiscoverPartnersListViewModelCopyWithImpl<$Res,
          DiscoverPartnersListViewModel>;
  @useResult
  $Res call(
      {List<DiscoverPartnerViewModel> partners,
      String title,
      String viewMoreText,
      String? jumpRoute});
}

/// @nodoc
class _$DiscoverPartnersListViewModelCopyWithImpl<$Res,
        $Val extends DiscoverPartnersListViewModel>
    implements $DiscoverPartnersListViewModelCopyWith<$Res> {
  _$DiscoverPartnersListViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = null,
    Object? title = null,
    Object? viewMoreText = null,
    Object? jumpRoute = freezed,
  }) {
    return _then(_value.copyWith(
      partners: null == partners
          ? _value.partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<DiscoverPartnerViewModel>,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      viewMoreText: null == viewMoreText
          ? _value.viewMoreText
          : viewMoreText // ignore: cast_nullable_to_non_nullable
              as String,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DiscoverPartnersListViewModelCopyWith<$Res>
    implements $DiscoverPartnersListViewModelCopyWith<$Res> {
  factory _$$_DiscoverPartnersListViewModelCopyWith(
          _$_DiscoverPartnersListViewModel value,
          $Res Function(_$_DiscoverPartnersListViewModel) then) =
      __$$_DiscoverPartnersListViewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<DiscoverPartnerViewModel> partners,
      String title,
      String viewMoreText,
      String? jumpRoute});
}

/// @nodoc
class __$$_DiscoverPartnersListViewModelCopyWithImpl<$Res>
    extends _$DiscoverPartnersListViewModelCopyWithImpl<$Res,
        _$_DiscoverPartnersListViewModel>
    implements _$$_DiscoverPartnersListViewModelCopyWith<$Res> {
  __$$_DiscoverPartnersListViewModelCopyWithImpl(
      _$_DiscoverPartnersListViewModel _value,
      $Res Function(_$_DiscoverPartnersListViewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = null,
    Object? title = null,
    Object? viewMoreText = null,
    Object? jumpRoute = freezed,
  }) {
    return _then(_$_DiscoverPartnersListViewModel(
      partners: null == partners
          ? _value._partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<DiscoverPartnerViewModel>,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      viewMoreText: null == viewMoreText
          ? _value.viewMoreText
          : viewMoreText // ignore: cast_nullable_to_non_nullable
              as String,
      jumpRoute: freezed == jumpRoute
          ? _value.jumpRoute
          : jumpRoute // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_DiscoverPartnersListViewModel
    implements _DiscoverPartnersListViewModel {
  const _$_DiscoverPartnersListViewModel(
      {final List<DiscoverPartnerViewModel> partners = const [],
      this.title = '',
      this.viewMoreText = '',
      this.jumpRoute})
      : _partners = partners;

  final List<DiscoverPartnerViewModel> _partners;
  @override
  @JsonKey()
  List<DiscoverPartnerViewModel> get partners {
    if (_partners is EqualUnmodifiableListView) return _partners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_partners);
  }

  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final String viewMoreText;
  @override
  final String? jumpRoute;

  @override
  String toString() {
    return 'DiscoverPartnersListViewModel(partners: $partners, title: $title, viewMoreText: $viewMoreText, jumpRoute: $jumpRoute)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DiscoverPartnersListViewModel &&
            const DeepCollectionEquality().equals(other._partners, _partners) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.viewMoreText, viewMoreText) ||
                other.viewMoreText == viewMoreText) &&
            (identical(other.jumpRoute, jumpRoute) ||
                other.jumpRoute == jumpRoute));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_partners),
      title,
      viewMoreText,
      jumpRoute);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DiscoverPartnersListViewModelCopyWith<_$_DiscoverPartnersListViewModel>
      get copyWith => __$$_DiscoverPartnersListViewModelCopyWithImpl<
          _$_DiscoverPartnersListViewModel>(this, _$identity);
}

abstract class _DiscoverPartnersListViewModel
    implements DiscoverPartnersListViewModel {
  const factory _DiscoverPartnersListViewModel(
      {final List<DiscoverPartnerViewModel> partners,
      final String title,
      final String viewMoreText,
      final String? jumpRoute}) = _$_DiscoverPartnersListViewModel;

  @override
  List<DiscoverPartnerViewModel> get partners;
  @override
  String get title;
  @override
  String get viewMoreText;
  @override
  String? get jumpRoute;
  @override
  @JsonKey(ignore: true)
  _$$_DiscoverPartnersListViewModelCopyWith<_$_DiscoverPartnersListViewModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DynamicsListViewModel {
  List<DynamicViewModel> get dynamics => throw _privateConstructorUsedError;
  int? get nextPageId => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DynamicsListViewModelCopyWith<DynamicsListViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DynamicsListViewModelCopyWith<$Res> {
  factory $DynamicsListViewModelCopyWith(DynamicsListViewModel value,
          $Res Function(DynamicsListViewModel) then) =
      _$DynamicsListViewModelCopyWithImpl<$Res, DynamicsListViewModel>;
  @useResult
  $Res call({List<DynamicViewModel> dynamics, int? nextPageId, int? pageSize});
}

/// @nodoc
class _$DynamicsListViewModelCopyWithImpl<$Res,
        $Val extends DynamicsListViewModel>
    implements $DynamicsListViewModelCopyWith<$Res> {
  _$DynamicsListViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dynamics = null,
    Object? nextPageId = freezed,
    Object? pageSize = freezed,
  }) {
    return _then(_value.copyWith(
      dynamics: null == dynamics
          ? _value.dynamics
          : dynamics // ignore: cast_nullable_to_non_nullable
              as List<DynamicViewModel>,
      nextPageId: freezed == nextPageId
          ? _value.nextPageId
          : nextPageId // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DynamicsListViewModelCopyWith<$Res>
    implements $DynamicsListViewModelCopyWith<$Res> {
  factory _$$_DynamicsListViewModelCopyWith(_$_DynamicsListViewModel value,
          $Res Function(_$_DynamicsListViewModel) then) =
      __$$_DynamicsListViewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<DynamicViewModel> dynamics, int? nextPageId, int? pageSize});
}

/// @nodoc
class __$$_DynamicsListViewModelCopyWithImpl<$Res>
    extends _$DynamicsListViewModelCopyWithImpl<$Res, _$_DynamicsListViewModel>
    implements _$$_DynamicsListViewModelCopyWith<$Res> {
  __$$_DynamicsListViewModelCopyWithImpl(_$_DynamicsListViewModel _value,
      $Res Function(_$_DynamicsListViewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dynamics = null,
    Object? nextPageId = freezed,
    Object? pageSize = freezed,
  }) {
    return _then(_$_DynamicsListViewModel(
      dynamics: null == dynamics
          ? _value._dynamics
          : dynamics // ignore: cast_nullable_to_non_nullable
              as List<DynamicViewModel>,
      nextPageId: freezed == nextPageId
          ? _value.nextPageId
          : nextPageId // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$_DynamicsListViewModel implements _DynamicsListViewModel {
  const _$_DynamicsListViewModel(
      {final List<DynamicViewModel> dynamics = const [],
      this.nextPageId,
      this.pageSize})
      : _dynamics = dynamics;

  final List<DynamicViewModel> _dynamics;
  @override
  @JsonKey()
  List<DynamicViewModel> get dynamics {
    if (_dynamics is EqualUnmodifiableListView) return _dynamics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dynamics);
  }

  @override
  final int? nextPageId;
  @override
  final int? pageSize;

  @override
  String toString() {
    return 'DynamicsListViewModel(dynamics: $dynamics, nextPageId: $nextPageId, pageSize: $pageSize)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DynamicsListViewModel &&
            const DeepCollectionEquality().equals(other._dynamics, _dynamics) &&
            (identical(other.nextPageId, nextPageId) ||
                other.nextPageId == nextPageId) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_dynamics), nextPageId, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DynamicsListViewModelCopyWith<_$_DynamicsListViewModel> get copyWith =>
      __$$_DynamicsListViewModelCopyWithImpl<_$_DynamicsListViewModel>(
          this, _$identity);
}

abstract class _DynamicsListViewModel implements DynamicsListViewModel {
  const factory _DynamicsListViewModel(
      {final List<DynamicViewModel> dynamics,
      final int? nextPageId,
      final int? pageSize}) = _$_DynamicsListViewModel;

  @override
  List<DynamicViewModel> get dynamics;
  @override
  int? get nextPageId;
  @override
  int? get pageSize;
  @override
  @JsonKey(ignore: true)
  _$$_DynamicsListViewModelCopyWith<_$_DynamicsListViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PartnersListViewModel {
  List<PartnerViewModel> get partners => throw _privateConstructorUsedError;
  int? get nextPageId => throw _privateConstructorUsedError;
  int? get pageSize => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PartnersListViewModelCopyWith<PartnersListViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PartnersListViewModelCopyWith<$Res> {
  factory $PartnersListViewModelCopyWith(PartnersListViewModel value,
          $Res Function(PartnersListViewModel) then) =
      _$PartnersListViewModelCopyWithImpl<$Res, PartnersListViewModel>;
  @useResult
  $Res call({List<PartnerViewModel> partners, int? nextPageId, int? pageSize});
}

/// @nodoc
class _$PartnersListViewModelCopyWithImpl<$Res,
        $Val extends PartnersListViewModel>
    implements $PartnersListViewModelCopyWith<$Res> {
  _$PartnersListViewModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = null,
    Object? nextPageId = freezed,
    Object? pageSize = freezed,
  }) {
    return _then(_value.copyWith(
      partners: null == partners
          ? _value.partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<PartnerViewModel>,
      nextPageId: freezed == nextPageId
          ? _value.nextPageId
          : nextPageId // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PartnersListViewModelCopyWith<$Res>
    implements $PartnersListViewModelCopyWith<$Res> {
  factory _$$_PartnersListViewModelCopyWith(_$_PartnersListViewModel value,
          $Res Function(_$_PartnersListViewModel) then) =
      __$$_PartnersListViewModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<PartnerViewModel> partners, int? nextPageId, int? pageSize});
}

/// @nodoc
class __$$_PartnersListViewModelCopyWithImpl<$Res>
    extends _$PartnersListViewModelCopyWithImpl<$Res, _$_PartnersListViewModel>
    implements _$$_PartnersListViewModelCopyWith<$Res> {
  __$$_PartnersListViewModelCopyWithImpl(_$_PartnersListViewModel _value,
      $Res Function(_$_PartnersListViewModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? partners = null,
    Object? nextPageId = freezed,
    Object? pageSize = freezed,
  }) {
    return _then(_$_PartnersListViewModel(
      partners: null == partners
          ? _value._partners
          : partners // ignore: cast_nullable_to_non_nullable
              as List<PartnerViewModel>,
      nextPageId: freezed == nextPageId
          ? _value.nextPageId
          : nextPageId // ignore: cast_nullable_to_non_nullable
              as int?,
      pageSize: freezed == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$_PartnersListViewModel implements _PartnersListViewModel {
  const _$_PartnersListViewModel(
      {final List<PartnerViewModel> partners = const [],
      this.nextPageId,
      this.pageSize})
      : _partners = partners;

  final List<PartnerViewModel> _partners;
  @override
  @JsonKey()
  List<PartnerViewModel> get partners {
    if (_partners is EqualUnmodifiableListView) return _partners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_partners);
  }

  @override
  final int? nextPageId;
  @override
  final int? pageSize;

  @override
  String toString() {
    return 'PartnersListViewModel(partners: $partners, nextPageId: $nextPageId, pageSize: $pageSize)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PartnersListViewModel &&
            const DeepCollectionEquality().equals(other._partners, _partners) &&
            (identical(other.nextPageId, nextPageId) ||
                other.nextPageId == nextPageId) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_partners), nextPageId, pageSize);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PartnersListViewModelCopyWith<_$_PartnersListViewModel> get copyWith =>
      __$$_PartnersListViewModelCopyWithImpl<_$_PartnersListViewModel>(
          this, _$identity);
}

abstract class _PartnersListViewModel implements PartnersListViewModel {
  const factory _PartnersListViewModel(
      {final List<PartnerViewModel> partners,
      final int? nextPageId,
      final int? pageSize}) = _$_PartnersListViewModel;

  @override
  List<PartnerViewModel> get partners;
  @override
  int? get nextPageId;
  @override
  int? get pageSize;
  @override
  @JsonKey(ignore: true)
  _$$_PartnersListViewModelCopyWith<_$_PartnersListViewModel> get copyWith =>
      throw _privateConstructorUsedError;
}
