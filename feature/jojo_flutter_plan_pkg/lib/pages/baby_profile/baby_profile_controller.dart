import 'dart:convert';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/mobile_for_token/jojo_user.dart';
import 'package:jojo_flutter_base/models/baby_grade.dart';
import 'package:jojo_flutter_base/models/nuwa_config_data.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/uploadAvator/avator_list_type.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/model/baby_profile_gray_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/model/mine_date.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_state.dart';
import 'package:jojo_flutter_plan_pkg/service/baby_info_api.dart';
import 'package:jojo_flutter_base/mobile_for_token/jojo_user.dart'
    as JoJoUserInfo;

String babyProfileLogTag = "宝贝档案";

class BabyProfileController extends Cubit<BabyProfileState> {
  final BabyInfoApi babyInfoServiceApi;
  final BabyInfoApi babyInfoUcService;
  final BabyInfoApi commonBabyInfoUcService;

  BabyProfileController(super.initialState, this.babyInfoServiceApi,
      this.babyInfoUcService, this.commonBabyInfoUcService);

  Future<BabyProfileState> getBabyProfileData(bool needGradeInfo) async {
    l.d(babyProfileLogTag, "getBabyProfileData  start ${DateTime.now()}");
    final futures = await Future.wait([getBabyInfo(),getBabyDefaultAvatarList(),getBabyGradeInfo()],eagerError: true);
    Map<String, dynamic>? babyMap = futures[0] as Map<String, dynamic>?;
    if (babyMap == null) {
      l.d(babyProfileLogTag, "getBabyProfileData  babyMap is empty");
      BabyProfileState newState = state.copyWith();
      newState.pageStatus = PageStatus.error;
      emit(newState);
      return BabyProfileState(
        pageStatus: PageStatus.error,
      );
    }
    BabyInfo babyInfo = BabyInfo.fromJson(babyMap);
    BabyInfo oldBabyInfo = BabyInfo.fromJson(babyMap);
    BabyProfileState newState =
        BabyProfileState(pageStatus: PageStatus.success);
    newState.babyInfo = babyInfo;
    newState.oldBabyInfo = oldBabyInfo;
    newState.defaultAvatarList = futures[1] as List<AvatarList>?;
    if (needGradeInfo) {
      newState.babyGradeInfo = futures[2] as BabyGradeInfo?;
      if (newState.babyGradeInfo == null) {
        return BabyProfileState(
          pageStatus: PageStatus.error,
        );
      }
    }
    l.d(babyProfileLogTag, "getBabyProfileData  success end ${DateTime.now()}");
    return newState;
  }

  Future<void> refreshBabyProfileData(bool needGradeInfo) async {
    safeEmit(state.copyWith()..pageStatus = PageStatus.loading);
    BabyProfileState babyProfileState = await getBabyProfileData(needGradeInfo);
    safeEmit(babyProfileState);
  }

  void safeEmit(BabyProfileState newState) {
    if (!isClosed) {
      emit(newState);
    }
  }

  Future<List<AvatarList>> getBabyDefaultAvatarList() async {
    l.d(babyProfileLogTag, "getBabyDefaultAvatarList  start =====");
    List? defaultA;
    try {
      Map<String, ConfigData>? configMap = await commonBabyInfoUcService
          .getNuwaBabyConfig("BABYINFGRAY")
          .then((value) => value, onError: (error) {})
          .onError((error, stackTrace) => {});
      ConfigData? babyInfoGray = configMap?["BABYINFGRAY"];
      defaultA = babyInfoGray?.configValue?["defaultAvatarList"];
    } catch (e) {
      l.e(babyProfileLogTag, "接口获取默认头像异常 $e");
    }
    if (defaultA == null) {
      try {
        String? result = await jojoNativeBridge
            .operationNativeValueGet(key: "baby_avatar_list")
            .then((value) => value.data?.value);
        defaultA = jsonDecode(result ?? "");
      } catch (e) {
        l.e(babyProfileLogTag, "获取默认头像异常 $e");
      }
    }
    if (defaultA != null) {
      jojoNativeBridge.operationNativeValueSet(
          key: "baby_avatar_list", value: jsonEncode(defaultA));
      List result = defaultA;
      List<AvatarList> avatarList = [];
      if (result.isNotEmpty) {
        for (Map map in result) {
          //后端oss地址使用base64加密了，需要解密
          final ossBase64Url = map['ossUrl'];
          var ossUrl = utf8.decode(base64Decode(ossBase64Url));
          ossUrl = Uri.decodeFull(ossUrl);
          AvatarList avatar =
              AvatarList(avatarOss: ossUrl, avatarUrl: map['httpUrl']);
          avatarList.add(avatar);
        }
        List<AvatarList> res = [];
        res.addAll(avatarList);
        return avatarList;
      }
    }
    l.d(babyProfileLogTag, "getBabyDefaultAvatarList  end =====");
    return [];
  }

  Future<Map<String, dynamic>?> getBabyInfo() async {
    //获取用户信息
    try {
      l.d(babyProfileLogTag, "wrapMineDataInfo  start =====");
      //uc 可能因为 没有宝贝信息 返回空
      final result = await Future.wait([babyInfoServiceApi.getMineDataInfo().then((value) => value),babyInfoUcService.getLoginSummaryInfo().then((value) => value)],eagerError: true);
      WrapMineDataInfo? wrapMineDataInfo = result[0] as WrapMineDataInfo?;
      MineGradeInfo? ucUserInfo = result[1] as MineGradeInfo?;
      l.d(babyProfileLogTag,
          "wrapMineDataInfo  getMineDataInfo $wrapMineDataInfo");
      l.d(babyProfileLogTag,
          "wrapMineDataInfo  $wrapMineDataInfo ucUserInfo===  $ucUserInfo");
      //数据组合 昵称和头像 来自 mine ，默认性别男 1
      Map<String, dynamic>? babyInfoMap = ucUserInfo?.babyInfo?.toJson();
      babyInfoMap ??= {};
      babyInfoMap["nickname"] =
          wrapMineDataInfo?.userPageInfo?.babyInfo?.babyNickNameInReview == true
              ? wrapMineDataInfo?.userPageInfo?.babyInfo?.reviewingBabyNickname
              : wrapMineDataInfo?.userPageInfo?.babyInfo?.nickname;
      babyInfoMap["avatarUrl"] =
          wrapMineDataInfo?.userPageInfo?.babyInfo?.avatarUrl;
      babyInfoMap["sex"] = ucUserInfo?.babyInfo?.sex ?? 1;
      return babyInfoMap;
    } catch (e) {
      l.d(babyProfileLogTag,"wrapMineDataInfo  error ===== $e");
    }
    return null;
  }

  Future<BabyGradeInfo?> getBabyGradeInfo() async {
    l.d(babyProfileLogTag, "getBabyGradeInfo  start =====");
    BabyGradeInfo? babyGradeInfo;
    try {
      babyGradeInfo = await babyInfoServiceApi.getBabyGradeInfo();
    } catch (e) {
      l.e(babyProfileLogTag, "getBabyGradeInfo from net error ===== $e");
    }
    babyGradeInfo ??= await jojoNativeBridge
        .operationNativeValueGet(key: "new_baby_grade")
        .then((value) {
      String result = value.data?.value ?? "";
      if (result.isNotEmpty) {
        Map<String, dynamic> map = {};
        try {
          map = json.decode(result);
          return BabyGradeInfo.fromJson(map);
        } catch (e) {
          l.e(
            babyProfileLogTag,
            "getBabyGradeInfo 数据解析异常 原始数据: $result 错误信息: $e",
          );
        }
      }
      return null;
    });

    if (babyGradeInfo != null) {
      dynamic jsonString = json.encode(babyGradeInfo.toJson());
      jojoNativeBridge.operationNativeValueSet(
          key: "new_baby_grade", value: jsonString);
    }
    l.d(babyProfileLogTag, "getBabyGradeInfo  end =====");
    return babyGradeInfo;
  }

  Future<BabyProfileGrayData?> getBabyProfileGrayData() async {
    try {
      BabyProfileGrayData? babyProfileGrayData = await babyInfoServiceApi
          .getBabyProfileGrayData(BabyInfoApi.babyProfileDataStagePlan);
      l.d(babyProfileLogTag, "getBabyProfileGrayData success");
      return babyProfileGrayData;
    } catch (e) {
      l.e(babyProfileLogTag, "getBabyProfileGrayData from net error ===== $e");
      return null;
    }
  }
}
