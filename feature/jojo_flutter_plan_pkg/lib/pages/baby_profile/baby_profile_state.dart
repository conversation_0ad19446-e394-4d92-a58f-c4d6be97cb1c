import 'package:jojo_flutter_base/mobile_for_token/jojo_user.dart';
import 'package:jojo_flutter_base/models/baby_grade.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_base/widgets/uploadAvator/avator_list_type.dart';

class BabyProfileState {
  BabyInfo? babyInfo;
  BabyInfo? oldBabyInfo;
  BabyGradeInfo? babyGradeInfo;
  List<AvatarList>? defaultAvatarList;
  PageStatus pageStatus = PageStatus.loading;

  BabyProfileState({
    this.babyInfo,
    this.oldBabyInfo,
    this.babyGradeInfo,
    this.defaultAvatarList,
    required this.pageStatus,
  });

  BabyProfileState copyWith({
    BabyInfo? babyInfo,
    BabyInfo? oldBabyInfo,
    BabyGradeInfo? babyGradeInfo,
    List<AvatarList>? defaultAvatarList,
    PageStatus? pageStatus,
  }) {
    return BabyProfileState(
      babyInfo: babyInfo ?? this.babyInfo,
      oldBabyInfo: oldBabyInfo ?? this.oldBabyInfo,
      babyGradeInfo: babyGradeInfo ?? this.babyGradeInfo,
      defaultAvatarList: defaultAvatarList ?? this.defaultAvatarList,
      pageStatus: pageStatus ?? this.pageStatus,
    );
  }

  @override
  String toString() {
    return 'BabyProfileState(babyInfo: $babyInfo, oldBabyInfo: $oldBabyInfo, babyGradeInfo: $babyGradeInfo, defaultAvatarList: $defaultAvatarList, pageStatus: $pageStatus)';
  }
}
