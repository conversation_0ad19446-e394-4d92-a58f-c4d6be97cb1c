import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ItemSelectWidget extends StatefulWidget {
  final String title;
  final String? selectShowText;
  final String noSelectShowText;
  final Function() onSelectTap;

  const ItemSelectWidget(
      {super.key,
      required this.title,
      this.selectShowText,
      required this.noSelectShowText,
      required this.onSelectTap});

  @override
  State<StatefulWidget> createState() {
    return _ItemSelectWidgetState();
  }
}

class _ItemSelectWidgetState extends State<ItemSelectWidget> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: () {
          widget.onSelectTap();
        },
        child: SizedBox(
          height: 44.rdp,
          width: 295.rdp,
          child: BottomDashedBorder(
            color: context.appColors.jColorBrown3,
            child: Row(children: [
              Text(widget.title,
                  style: TextStyle(
                      fontSize: 16.rdp, color: context.appColors.jColorGray4)),
              Expanded(
                  child:
                      Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                Text(
                  _getShowText(),
                  style: TextStyle(
                      fontSize: 16.rdp, color: context.appColors.jColorGray4),
                ),
                Image.asset(
                  AssetsImg.BABY_PROFILE_BABY_PROFILE_ITEM_END_DOWN_ICON,
                  width: 20.rdp,
                  height: 20.rdp,
                  package: Config.package,
                )
              ])),
            ]),
          ),
        ),
      ),
    );
  }

  String _getShowText() {
    if (widget.selectShowText?.isNotEmpty == true) {
      return widget.selectShowText!;
    } else {
      return widget.noSelectShowText;
    }
  }
}

class DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;
  final double strokeWidth;

  DashedLinePainter({
    this.color = Colors.black,
    this.dashWidth = 5.0,
    this.dashSpace = 3.0,
    this.strokeWidth = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startX = 0.0;
    final double y = size.height;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + dashWidth, y),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class BottomDashedBorder extends StatelessWidget {
  final Widget child;
  final Color color;
  final double dashWidth;
  final double dashSpace;
  final double strokeWidth;

  const BottomDashedBorder({
    Key? key,
    required this.child,
    this.color = Colors.black,
    this.dashWidth = 5.0,
    this.dashSpace = 3.0,
    this.strokeWidth = 1.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: DashedLinePainter(
        color: color,
        dashWidth: dashWidth,
        dashSpace: dashSpace,
        strokeWidth: strokeWidth,
      ),
      child: child,
    );
  }
}
