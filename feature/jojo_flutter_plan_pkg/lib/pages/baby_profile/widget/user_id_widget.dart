import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class UserIdWidget extends StatefulWidget {
  const UserIdWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return _UserIdWidgetState();
  }
}

class _UserIdWidgetState extends State<UserIdWidget> {
  String? userId = "";

  @override
  void initState() {
    super.initState();
    _updateUid();
  }

  void _updateUid() async {
    UserInfo? userInfo =
        await jojoNativeBridge.getUserInfo().then((value) => value.data);
    if (mounted) {
      setState(() {
        userId = userInfo?.uid ?? "";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 267.rdp,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              AssetsImg.BABY_PROFILE_BABY_PROFILE_ID_LINE,
              height: 18.rdp,
              width: 71.rdp,
              package: Config.package,
            ),
            Expanded(
                child: Align(
              alignment: Alignment.center,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "ID: ",
                    style: TextStyle(
                      color: context.appColors.jColorBrown6.withOpacity(0.45),
                      fontSize: 12.rdp,
                      fontFamily: 'MohrRounded-black',
                      package: 'jojo_flutter_design',
                    ),
                  ),
                  Text(
                    "$userId",
                    style: TextStyle(
                      color: context.appColors.jColorBrown6.withOpacity(0.6),
                      fontSize: 12.rdp,
                      fontWeight: FontWeight.w400,
                      package: 'jojo_flutter_base',
                      fontFamily: 'MohrRounded_Bold',
                    ),
                  ),
                ],
              ),
            )),
            Image.asset(
              AssetsImg.BABY_PROFILE_BABY_PROFILE_ID_LINE,
              height: 18.rdp,
              width: 71.rdp,
              package: Config.package,
            )
          ],
        ),
      ),
    );
  }
}
