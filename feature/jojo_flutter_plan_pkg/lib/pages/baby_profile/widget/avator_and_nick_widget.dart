import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/mobile_for_token/jojo_user.dart';
import 'package:jojo_flutter_base/widgets/uploadAvator/avator_list_type.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/widget/image_pick_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/widget/nick_name_input_widget.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class AvatarAndNickWidget extends StatefulWidget {
  final BabyInfo? babyInfo;
  final List<AvatarList>? avatarList;
  final Function(AvatarList) avatorCallback;
  final Function(String) nickNameCallback;
  final FocusNode focusNode;

  const AvatarAndNickWidget(
      {super.key,
      required this.avatorCallback,
      required this.nickNameCallback,
      required this.babyInfo,
      required this.avatarList,
      required this.focusNode});

  @override
  State createState() {
    return _AvatarAndNickWidgetState();
  }
}

class _AvatarAndNickWidgetState extends State<AvatarAndNickWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 74.rdp,
      child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 30.rdp,
            ),
            ImagePickWidget(
              width: 74.rdp,
              height: 74.rdp,
              imageUrl: widget.babyInfo?.avatarUrl ?? '',
              defaultAvatarList: widget.avatarList ?? [],
              avatarCallback: widget.avatorCallback,
              onTap: () {
                widget.focusNode.unfocus();
              },
            ),
            SizedBox(
              width: 14.rdp,
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.only(top: 22.rdp),
                child: NickNameInputWidget(
                  nickName: widget.babyInfo?.nickname ?? '',
                  onChanged: widget.nickNameCallback,
                  focusNode: widget.focusNode,
                ),
              ),
            ),
            SizedBox(
              width: 14.rdp,
            ),
            SizedBox(
                width: 26.rdp,
                child: Image.asset(
                  AssetsImg.BABY_PROFILE_BABY_PROFILE_NICK_NAME_END,
                  height: 68.rdp,
                  width: 26.rdp,
                  package: Config.package,
                )),
            SizedBox(
              width: 27.rdp,
            )
          ]),
    );
  }
}
