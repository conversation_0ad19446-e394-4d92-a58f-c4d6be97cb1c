import 'dart:async';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_controller.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class NickNameInputWidget extends StatefulWidget {
  final String nickName;
  final Function(String) onChanged;
  final FocusNode focusNode;

  const NickNameInputWidget({
    super.key,
    required this.nickName,
    required this.onChanged,
    required this.focusNode,
  });

  @override
  State<StatefulWidget> createState() {
    return _NickNameInputWidgetState();
  }
}

class _NickNameInputWidgetState extends State<NickNameInputWidget> {
  final TextEditingController _textEditingController = TextEditingController();
  final OutlineInputBorder _outlineInputBorder = OutlineInputBorder(
    borderRadius: BorderRadius.circular(8.rdp),
    borderSide: const BorderSide(color: Colors.transparent),
  );

  _NickNameInputWidgetState();

  bool _hasFocus = false;
  final _keyboardManager = KeyboardVisibilityController();
  late StreamSubscription<bool> _streamSubscription;

  @override
  void initState() {
    super.initState();
    _textEditingController.text = widget.nickName;
    widget.focusNode.addListener(_onChanged);
    _hasFocus = widget.focusNode.hasFocus;
    _streamSubscription = _keyboardManager.onChange.listen((bool visible) {
      if (!visible) {
        unFocusNickName();
      }
    });
  }

  void unFocusNickName() {
    try {
      if (mounted && widget.focusNode.canRequestFocus) {
        widget.focusNode.unfocus();
      }
    } catch (e) {
      l.e(babyProfileLogTag, "软键盘收起 焦点移除异常 $e");
    }
  }

  void _onChanged() {
    if (mounted) {
      setState(() {
        _hasFocus = widget.focusNode.hasFocus;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              widget.focusNode.requestFocus();
            },
            child: Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  height: 24.rdp,
                  child: Stack(
                    children: [
                      Row(
                        children: [
                          Opacity(
                            opacity: 0,
                            child: Text(_textEditingController.text,
                                style: TextStyle(
                                  color: context.appColors.jColorBrown6,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16.rdp,
                                )),
                          ),
                          SizedBox(width: 4.rdp),
                          _hasFocus
                              ? SizedBox(
                                  height: 16.rdp,
                                  width: 16.rdp,
                                )
                              : Container(
                                  width: 16.rdp,
                                  height: 16.rdp,
                                  alignment: Alignment.center,
                                  child: Center(
                                    child: Image.asset(
                                      AssetsImg
                                          .BABY_PROFILE_BABY_PROFILE_NICK_NAME_EDIT,
                                      height: 16.rdp,
                                      width: 16.rdp,
                                      package: Config.package,
                                    ),
                                  ),
                                ),
                        ],
                      ),
                      TextField(
                        controller: _textEditingController,
                        focusNode: widget.focusNode,
                        textAlign: TextAlign.start,
                        maxLength: 8,
                        textAlignVertical: TextAlignVertical.top,
                        style: TextStyle(
                          color: context.appColors.jColorBrown6,
                          fontWeight: FontWeight.w600,
                          fontSize: 16.rdp,
                        ),
                        decoration: InputDecoration(
                          labelText: '',
                          counterText: '',
                          hintText: S.of(context).pleaseEditBabyName,
                          constraints: BoxConstraints(
                              minWidth: 50.rdp, maxWidth: 150.rdp),
                          contentPadding: EdgeInsets.zero,
                          enabledBorder: _outlineInputBorder,
                          border: _outlineInputBorder,
                          focusedBorder: _outlineInputBorder,
                        ),
                        obscureText: false,
                        onChanged: (String value) {
                          widget.onChanged(value);
                        },
                        onSubmitted: (String value) {
                          // 处理提交
                          widget.onChanged(value);
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 6.rdp),
          Image.asset(
            AssetsImg.BABY_PROFILE_BABY_PROFILE_NICK_NAME_BOTTOM_LINE,
            height: 2.rdp,
            width: 118.rdp,
            fit: BoxFit.contain,
            package: Config.package,
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _streamSubscription.cancel();
    _textEditingController.dispose();
    widget.focusNode.removeListener(_onChanged);
    super.dispose();
  }
}
