import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/models/baby_grade.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';

import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

// 年级选择组件
class GradeSelectionItemWidget extends StatefulWidget {
  final GradeInfo? gradeInfo;
  final double width;
  final Function(GradeInfo gradeInfo) onGradeSelected;
  final int? selectCode;
  const GradeSelectionItemWidget({
    super.key,
    this.gradeInfo,
    required this.width,
    required this.onGradeSelected,
    this.selectCode,
  });

  @override
  State<StatefulWidget> createState() {
    return _GradeSelectionItemWidgetState();
  }
}

class _GradeSelectionItemWidgetState extends State<GradeSelectionItemWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final isActive = widget.selectCode == widget.gradeInfo?.code;
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (widget.gradeInfo != null) {
          widget.onGradeSelected(widget.gradeInfo!);
        }
      },
      child: Stack(
        children: [
          Container(
            height: 68.rdp,
            width: widget.width,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: isActive ? HexColor('#FFFAE5') : HexColor('#ffffff'),
              borderRadius: BorderRadius.all(Radius.circular(16.rdp)),
              border: Border.all(
                width: isActive ? 2.rdp : 1.rdp,
                color: isActive ? HexColor('#FCDA00') : HexColor('#E0DFDF'),
              ),
            ),
            child: Text(
              widget.gradeInfo?.name ?? '',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18.rdp,
                color: HexColor('#41474F'),
              ),
            ),
          ),
          if (isActive)
            Positioned(
              bottom: 0,
              right: 0,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(16.rdp),
                ),
                child: SvgAssetWeb(
                  assetName: AssetsSvg.BABY_PROFILE_GRADE_SELECTION,
                  width: 24.rdp,
                  package: Config.package,
                ),
              ),
            )
        ],
      ),
    );
  }
}
