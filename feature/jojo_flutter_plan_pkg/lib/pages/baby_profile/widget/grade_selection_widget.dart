import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/models/baby_grade.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/common/svg_asset.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

import 'package:jojo_flutter_plan_pkg/pages/baby_profile/widget/grade_selection_item_widget.dart';
import 'package:jojo_flutter_plan_pkg/static/svg.dart';

// 年级选择组件
class GradeSelectionWidget extends StatefulWidget {
  final BabyGradeInfo babyGradeInfo;
  final Function(GradeInfo gradeInfo) onGradeSelected;
  final int? selectCode;
  final String pageName;
  const GradeSelectionWidget({
    super.key,
    required this.babyGradeInfo,
    required this.onGradeSelected,
    this.selectCode,
    required this.pageName,
  });

  @override
  State<StatefulWidget> createState() {
    return _GradeSelectionWidgetState();
  }
}

class _GradeSelectionWidgetState extends State<GradeSelectionWidget> {
  @override
  void initState() {
    super.initState();
    RunEnv.sensorsTrack('ElementView', {
      'c_element_name': '宝贝资料_年级弹窗',
      '\$screen_name': widget.pageName,
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 469.rdp,
      padding: EdgeInsets.symmetric(horizontal: 20.rdp, vertical: 16.rdp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.rdp),
          topRight: Radius.circular(24.rdp),
        ),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              SizedBox(height: 8.rdp),
              Text(
                '请选择宝贝年级',
                style: TextStyle(
                  fontSize: 18.rdp,
                  color: HexColor('#404040'),
                  fontWeight: FontWeight.bold,
                  height: 1.5,
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      if (widget.babyGradeInfo.gradeDictList?.isNotEmpty ==
                          true)
                        ...widget.babyGradeInfo.gradeDictList!.map((item) {
                          return Column(
                            children: [
                              _buildTitle(item),
                              Wrap(
                                direction: Axis.horizontal,
                                spacing: 8.rdp,
                                runSpacing: 20.rdp,
                                runAlignment: WrapAlignment.center,
                                alignment: WrapAlignment.start,
                                children: List.generate(
                                  item.subDict?.length ?? 0,
                                  (index) {
                                    if (item.subDict?.isNotEmpty == true) {
                                      final _gradeItem = item.subDict![index];
                                      var _width = 163.rdp;
                                      // 根据个数排列 如果是三个数据排列一行，偶数就两列排列数据
                                      if (item.subDict!.length == 3) {
                                        _width = 106.rdp;
                                      }
                                      if (item.subDict!.length == 1) {
                                        _width = 334.rdp;
                                      }
                                      return GradeSelectionItemWidget(
                                        gradeInfo: _gradeItem,
                                        width: _width,
                                        onGradeSelected: widget.onGradeSelected,
                                        selectCode: widget.selectCode,
                                      );
                                    }
                                    return const SizedBox();
                                  },
                                ),
                              )
                            ],
                          );
                        })
                    ],
                  ),
                ),
              ),
              SizedBox(height: 4.rdp),
            ],
          ),
          Positioned(
            right: 0,
            top: 0,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                RunEnv.sensorsTrack('\$AppClick', {
                  '\$element_name': '宝贝资料_年级弹窗_关闭',
                  '\$screen_name': widget.pageName,
                });
                SmartDialog.dismiss();
              },
              child: SvgAssetWeb(
                assetName: AssetsSvg.CLOSE,
                width: 27.rdp,
                package: Config.package,
                color: HexColor('#404040'),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildTitle(GradeInfo gradeInfo) {
    return Padding(
      padding: EdgeInsets.only(top: 20.rdp, bottom: 8.rdp),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 1.rdp,
              color: HexColor('#F5F4F4'),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 14.rdp),
            child: Text(
              gradeInfo.name ?? '',
              style: TextStyle(
                fontSize: 12.rdp,
                color: HexColor('#B2B2B2'),
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 1.rdp,
              color: HexColor('#F5F4F4'),
            ),
          ),
        ],
      ),
    );
  }
}
