import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/common/dio/use.dart';
import 'package:jojo_flutter_base/config/config.dart';

import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/utils/uploader/index.dart';
import 'package:jojo_flutter_base/widgets/common/cached_network_image_pro.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/uploadAvator/avator_list_type.dart';
import 'package:jojo_flutter_base/widgets/uploadAvator/upload_avator.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_controller.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class ImagePickWidget extends StatefulWidget {
  final String imageUrl;
  final List<AvatarList> defaultAvatarList;
  final double width;
  final double height;
  final Function(AvatarList) avatarCallback;
  final Function()? onTap;

  const ImagePickWidget(
      {super.key,
      required this.imageUrl,
      required this.defaultAvatarList,
      required this.width,
      required this.height,
      required this.avatarCallback,
      required this.onTap});

  @override
  State<StatefulWidget> createState() {
    return _ImagePickWidgetState();
  }
}

class _ImagePickWidgetState extends State<ImagePickWidget> {
  late String imageUrl;
  final _upload = Uploader(
      prefix: BaseConfig.share.prefixUrl,
      dio: pageDio,
      noCookieDio: pageNoCookieDio);

  @override
  void initState() {
    super.initState();
    imageUrl = widget.imageUrl;
  }

  @override
  Widget build(BuildContext context) {
    return ClickWidget(
      type: ClickType.debounce,
      onTap: () {
        widget.onTap?.call();
        _showBabyUploadAvatar(context);
      },
      child: AspectRatio(
        aspectRatio: 1.0,
        child: Stack(
          children: [
            ImageNetworkCached(
              imageUrl: imageUrl,
              fit: BoxFit.contain,
              width: widget.width,
              height: widget.height,
              borderRadius: 40.rdp,
              errorWidget: ImageAssetWeb(
                assetName: AssetsImg.BABY_PROFILE_BABY_PROFILE_AVATAR_DEFAULT,
                package: RunEnv.package,
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: ImageAssetWeb(
                assetName: AssetsImg.BABY_PROFILE_BABY_PROFILE_AVATAR_CAMER,
                fit: BoxFit.contain,
                width: widget.width * 30 / 74,
                height: widget.height * 30 / 74,
                package: RunEnv.package,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showBabyUploadAvatar(BuildContext context) {
    showUploadAvator(
      1024,
      1024,
      context,
      _upload,
      '',
      defaultAvatarList: widget.defaultAvatarList,
      changeAvtar: (paths, closeHandler) {
        l.d(babyProfileLogTag,
            "showUploadAvator closeHandle ${paths.avatarOss} - ${paths.avatarUrl}");
        widget.avatarCallback(paths);
        imageUrl = paths.avatarUrl ?? imageUrl;
        if (mounted) {
          setState(() {});
        }
        closeHandler();
      },
      isUseCamera: true,
      pickHandle: () {
        l.d(babyProfileLogTag, "showUploadAvator pickHandle");
      },
    );
  }
}
