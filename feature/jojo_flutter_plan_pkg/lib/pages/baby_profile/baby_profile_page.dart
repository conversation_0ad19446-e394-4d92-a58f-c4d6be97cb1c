import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_container_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/utils/sensor_utils.dart';
import 'package:jojo_flutter_plan_pkg/service/baby_info_api.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';

class BabyProfilePage extends StatefulWidget {
  final bool needCheckAge;
  final bool needCheckGrade;

  const BabyProfilePage(
      {super.key, required this.needCheckAge, required this.needCheckGrade});

  @override
  State<BabyProfilePage> createState() => _BabyProfilePageState();
}

class _BabyProfilePageState extends State<BabyProfilePage> {
  BabyProfileController controller = BabyProfileController(
    BabyProfileState(
      pageStatus: PageStatus.loading,
    ),
    babyInfoService,
    babyInfoUcService,
    commonBabyInfoUcService,
  );

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: BlocProvider(
          create: (BuildContext context) {
            return controller;
          },
          child: BlocBuilder<BabyProfileController, BabyProfileState>(
              builder: (context, state) {
            return ColoredBox(
              color: Colors.white,
              child: JoJoPageLoading(
                status: state.pageStatus,
                child: Stack(
                  children: [
                    BabyProfileContainerWidget(
                      state: state,
                      needCheckAge: widget.needCheckAge,
                      needCheckGrade: widget.needCheckGrade,
                      saveBabyInfoCallback: (bool isSaveed, bool isComplete) {
                        //iOS 关闭页面后无法弹出toast提示 增加延时解决
                        if (RunEnv.isIOS && !isComplete) {
                          Future.delayed(const Duration(milliseconds: 1500), () {
                            JoJoRouter.pop();
                          });
                        } else {
                          JoJoRouter.pop();
                        }
                      },
                      pageName: '首页',
                    ),
                    Positioned(
                        top: 44.rdp,
                        right: 0,
                        child: ClickWidget(
                          type: ClickType.debounce,
                          onTap: () {
                            if (mounted) {
                              BabyProfileSensorUtils.elementClick(
                                  state.babyInfo, "宝贝资料_点击关闭按钮", "首页");
                              JoJoRouter.pop();
                            }
                          },
                          child: ImageAssetWeb(
                            assetName: AssetsImg.BABY_PROFILE_BABY_PROFILE_CLOSE,
                            package: Config.package,
                            width: 60.rdp,
                            height: 60.rdp,
                            fit: BoxFit.fitWidth,
                          ),
                        )),
                  ],
                ),
                retry: () {
                  controller.refreshBabyProfileData(widget.needCheckGrade);
                },
              ),
            );
          }),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    controller.refreshBabyProfileData(widget.needCheckGrade);
    if (RunEnv.isIOS) {
      jojoNativeBridge.setInteractivePopEnable(enable: 'false');
    }
  }
}
