// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'mine_date.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

WrapMineDataInfo _$WrapMineDataInfoFromJson(Map<String, dynamic> json) {
  return _WrapMineDataInfo.fromJson(json);
}

/// @nodoc
mixin _$WrapMineDataInfo {
  MineDataInfo? get userPageInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $WrapMineDataInfoCopyWith<WrapMineDataInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WrapMineDataInfoCopyWith<$Res> {
  factory $WrapMineDataInfoCopyWith(
          WrapMineDataInfo value, $Res Function(WrapMineDataInfo) then) =
      _$WrapMineDataInfoCopyWithImpl<$Res, WrapMineDataInfo>;
  @useResult
  $Res call({MineDataInfo? userPageInfo});

  $MineDataInfoCopyWith<$Res>? get userPageInfo;
}

/// @nodoc
class _$WrapMineDataInfoCopyWithImpl<$Res, $Val extends WrapMineDataInfo>
    implements $WrapMineDataInfoCopyWith<$Res> {
  _$WrapMineDataInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userPageInfo = freezed,
  }) {
    return _then(_value.copyWith(
      userPageInfo: freezed == userPageInfo
          ? _value.userPageInfo
          : userPageInfo // ignore: cast_nullable_to_non_nullable
              as MineDataInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MineDataInfoCopyWith<$Res>? get userPageInfo {
    if (_value.userPageInfo == null) {
      return null;
    }

    return $MineDataInfoCopyWith<$Res>(_value.userPageInfo!, (value) {
      return _then(_value.copyWith(userPageInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_WrapMineDataInfoCopyWith<$Res>
    implements $WrapMineDataInfoCopyWith<$Res> {
  factory _$$_WrapMineDataInfoCopyWith(
          _$_WrapMineDataInfo value, $Res Function(_$_WrapMineDataInfo) then) =
      __$$_WrapMineDataInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({MineDataInfo? userPageInfo});

  @override
  $MineDataInfoCopyWith<$Res>? get userPageInfo;
}

/// @nodoc
class __$$_WrapMineDataInfoCopyWithImpl<$Res>
    extends _$WrapMineDataInfoCopyWithImpl<$Res, _$_WrapMineDataInfo>
    implements _$$_WrapMineDataInfoCopyWith<$Res> {
  __$$_WrapMineDataInfoCopyWithImpl(
      _$_WrapMineDataInfo _value, $Res Function(_$_WrapMineDataInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userPageInfo = freezed,
  }) {
    return _then(_$_WrapMineDataInfo(
      userPageInfo: freezed == userPageInfo
          ? _value.userPageInfo
          : userPageInfo // ignore: cast_nullable_to_non_nullable
              as MineDataInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_WrapMineDataInfo implements _WrapMineDataInfo {
  const _$_WrapMineDataInfo({this.userPageInfo});

  factory _$_WrapMineDataInfo.fromJson(Map<String, dynamic> json) =>
      _$$_WrapMineDataInfoFromJson(json);

  @override
  final MineDataInfo? userPageInfo;

  @override
  String toString() {
    return 'WrapMineDataInfo(userPageInfo: $userPageInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_WrapMineDataInfo &&
            (identical(other.userPageInfo, userPageInfo) ||
                other.userPageInfo == userPageInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userPageInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_WrapMineDataInfoCopyWith<_$_WrapMineDataInfo> get copyWith =>
      __$$_WrapMineDataInfoCopyWithImpl<_$_WrapMineDataInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_WrapMineDataInfoToJson(
      this,
    );
  }
}

abstract class _WrapMineDataInfo implements WrapMineDataInfo {
  const factory _WrapMineDataInfo({final MineDataInfo? userPageInfo}) =
      _$_WrapMineDataInfo;

  factory _WrapMineDataInfo.fromJson(Map<String, dynamic> json) =
      _$_WrapMineDataInfo.fromJson;

  @override
  MineDataInfo? get userPageInfo;
  @override
  @JsonKey(ignore: true)
  _$$_WrapMineDataInfoCopyWith<_$_WrapMineDataInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

MineDataInfo _$MineDataInfoFromJson(Map<String, dynamic> json) {
  return _MineDataInfo.fromJson(json);
}

/// @nodoc
mixin _$MineDataInfo {
  MineBabyInfo? get babyInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MineDataInfoCopyWith<MineDataInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MineDataInfoCopyWith<$Res> {
  factory $MineDataInfoCopyWith(
          MineDataInfo value, $Res Function(MineDataInfo) then) =
      _$MineDataInfoCopyWithImpl<$Res, MineDataInfo>;
  @useResult
  $Res call({MineBabyInfo? babyInfo});

  $MineBabyInfoCopyWith<$Res>? get babyInfo;
}

/// @nodoc
class _$MineDataInfoCopyWithImpl<$Res, $Val extends MineDataInfo>
    implements $MineDataInfoCopyWith<$Res> {
  _$MineDataInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? babyInfo = freezed,
  }) {
    return _then(_value.copyWith(
      babyInfo: freezed == babyInfo
          ? _value.babyInfo
          : babyInfo // ignore: cast_nullable_to_non_nullable
              as MineBabyInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MineBabyInfoCopyWith<$Res>? get babyInfo {
    if (_value.babyInfo == null) {
      return null;
    }

    return $MineBabyInfoCopyWith<$Res>(_value.babyInfo!, (value) {
      return _then(_value.copyWith(babyInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MineDataInfoCopyWith<$Res>
    implements $MineDataInfoCopyWith<$Res> {
  factory _$$_MineDataInfoCopyWith(
          _$_MineDataInfo value, $Res Function(_$_MineDataInfo) then) =
      __$$_MineDataInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({MineBabyInfo? babyInfo});

  @override
  $MineBabyInfoCopyWith<$Res>? get babyInfo;
}

/// @nodoc
class __$$_MineDataInfoCopyWithImpl<$Res>
    extends _$MineDataInfoCopyWithImpl<$Res, _$_MineDataInfo>
    implements _$$_MineDataInfoCopyWith<$Res> {
  __$$_MineDataInfoCopyWithImpl(
      _$_MineDataInfo _value, $Res Function(_$_MineDataInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? babyInfo = freezed,
  }) {
    return _then(_$_MineDataInfo(
      babyInfo: freezed == babyInfo
          ? _value.babyInfo
          : babyInfo // ignore: cast_nullable_to_non_nullable
              as MineBabyInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MineDataInfo implements _MineDataInfo {
  const _$_MineDataInfo({this.babyInfo});

  factory _$_MineDataInfo.fromJson(Map<String, dynamic> json) =>
      _$$_MineDataInfoFromJson(json);

  @override
  final MineBabyInfo? babyInfo;

  @override
  String toString() {
    return 'MineDataInfo(babyInfo: $babyInfo)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MineDataInfo &&
            (identical(other.babyInfo, babyInfo) ||
                other.babyInfo == babyInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, babyInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MineDataInfoCopyWith<_$_MineDataInfo> get copyWith =>
      __$$_MineDataInfoCopyWithImpl<_$_MineDataInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MineDataInfoToJson(
      this,
    );
  }
}

abstract class _MineDataInfo implements MineDataInfo {
  const factory _MineDataInfo({final MineBabyInfo? babyInfo}) = _$_MineDataInfo;

  factory _MineDataInfo.fromJson(Map<String, dynamic> json) =
      _$_MineDataInfo.fromJson;

  @override
  MineBabyInfo? get babyInfo;
  @override
  @JsonKey(ignore: true)
  _$$_MineDataInfoCopyWith<_$_MineDataInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

MineBabyInfo _$MineBabyInfoFromJson(Map<String, dynamic> json) {
  return _MineBabyInfo.fromJson(json);
}

/// @nodoc
mixin _$MineBabyInfo {
  String? get nickname => throw _privateConstructorUsedError;
  int? get sex => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  String? get birthday => throw _privateConstructorUsedError;
  int? get grade => throw _privateConstructorUsedError;
  num? get userId => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get family => throw _privateConstructorUsedError;
  String? get registerTime => throw _privateConstructorUsedError;
  int? get userPoints => throw _privateConstructorUsedError;
  String? get userPointsLink => throw _privateConstructorUsedError;
  String? get reviewingBabyNickname => throw _privateConstructorUsedError;

  ///审核中 昵称，宝贝资料编辑
  bool? get babyNickNameInReview => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MineBabyInfoCopyWith<MineBabyInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MineBabyInfoCopyWith<$Res> {
  factory $MineBabyInfoCopyWith(
          MineBabyInfo value, $Res Function(MineBabyInfo) then) =
      _$MineBabyInfoCopyWithImpl<$Res, MineBabyInfo>;
  @useResult
  $Res call(
      {String? nickname,
      int? sex,
      String? avatarUrl,
      String? birthday,
      int? grade,
      num? userId,
      String? location,
      String? family,
      String? registerTime,
      int? userPoints,
      String? userPointsLink,
      String? reviewingBabyNickname,
      bool? babyNickNameInReview});
}

/// @nodoc
class _$MineBabyInfoCopyWithImpl<$Res, $Val extends MineBabyInfo>
    implements $MineBabyInfoCopyWith<$Res> {
  _$MineBabyInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? sex = freezed,
    Object? avatarUrl = freezed,
    Object? birthday = freezed,
    Object? grade = freezed,
    Object? userId = freezed,
    Object? location = freezed,
    Object? family = freezed,
    Object? registerTime = freezed,
    Object? userPoints = freezed,
    Object? userPointsLink = freezed,
    Object? reviewingBabyNickname = freezed,
    Object? babyNickNameInReview = freezed,
  }) {
    return _then(_value.copyWith(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      sex: freezed == sex
          ? _value.sex
          : sex // ignore: cast_nullable_to_non_nullable
              as int?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as String?,
      grade: freezed == grade
          ? _value.grade
          : grade // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as num?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      family: freezed == family
          ? _value.family
          : family // ignore: cast_nullable_to_non_nullable
              as String?,
      registerTime: freezed == registerTime
          ? _value.registerTime
          : registerTime // ignore: cast_nullable_to_non_nullable
              as String?,
      userPoints: freezed == userPoints
          ? _value.userPoints
          : userPoints // ignore: cast_nullable_to_non_nullable
              as int?,
      userPointsLink: freezed == userPointsLink
          ? _value.userPointsLink
          : userPointsLink // ignore: cast_nullable_to_non_nullable
              as String?,
      reviewingBabyNickname: freezed == reviewingBabyNickname
          ? _value.reviewingBabyNickname
          : reviewingBabyNickname // ignore: cast_nullable_to_non_nullable
              as String?,
      babyNickNameInReview: freezed == babyNickNameInReview
          ? _value.babyNickNameInReview
          : babyNickNameInReview // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_MineBabyInfoCopyWith<$Res>
    implements $MineBabyInfoCopyWith<$Res> {
  factory _$$_MineBabyInfoCopyWith(
          _$_MineBabyInfo value, $Res Function(_$_MineBabyInfo) then) =
      __$$_MineBabyInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickname,
      int? sex,
      String? avatarUrl,
      String? birthday,
      int? grade,
      num? userId,
      String? location,
      String? family,
      String? registerTime,
      int? userPoints,
      String? userPointsLink,
      String? reviewingBabyNickname,
      bool? babyNickNameInReview});
}

/// @nodoc
class __$$_MineBabyInfoCopyWithImpl<$Res>
    extends _$MineBabyInfoCopyWithImpl<$Res, _$_MineBabyInfo>
    implements _$$_MineBabyInfoCopyWith<$Res> {
  __$$_MineBabyInfoCopyWithImpl(
      _$_MineBabyInfo _value, $Res Function(_$_MineBabyInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? sex = freezed,
    Object? avatarUrl = freezed,
    Object? birthday = freezed,
    Object? grade = freezed,
    Object? userId = freezed,
    Object? location = freezed,
    Object? family = freezed,
    Object? registerTime = freezed,
    Object? userPoints = freezed,
    Object? userPointsLink = freezed,
    Object? reviewingBabyNickname = freezed,
    Object? babyNickNameInReview = freezed,
  }) {
    return _then(_$_MineBabyInfo(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      sex: freezed == sex
          ? _value.sex
          : sex // ignore: cast_nullable_to_non_nullable
              as int?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as String?,
      grade: freezed == grade
          ? _value.grade
          : grade // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as num?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      family: freezed == family
          ? _value.family
          : family // ignore: cast_nullable_to_non_nullable
              as String?,
      registerTime: freezed == registerTime
          ? _value.registerTime
          : registerTime // ignore: cast_nullable_to_non_nullable
              as String?,
      userPoints: freezed == userPoints
          ? _value.userPoints
          : userPoints // ignore: cast_nullable_to_non_nullable
              as int?,
      userPointsLink: freezed == userPointsLink
          ? _value.userPointsLink
          : userPointsLink // ignore: cast_nullable_to_non_nullable
              as String?,
      reviewingBabyNickname: freezed == reviewingBabyNickname
          ? _value.reviewingBabyNickname
          : reviewingBabyNickname // ignore: cast_nullable_to_non_nullable
              as String?,
      babyNickNameInReview: freezed == babyNickNameInReview
          ? _value.babyNickNameInReview
          : babyNickNameInReview // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MineBabyInfo implements _MineBabyInfo {
  const _$_MineBabyInfo(
      {this.nickname,
      this.sex,
      this.avatarUrl,
      this.birthday,
      this.grade,
      this.userId,
      this.location,
      this.family,
      this.registerTime,
      this.userPoints,
      this.userPointsLink,
      this.reviewingBabyNickname,
      this.babyNickNameInReview});

  factory _$_MineBabyInfo.fromJson(Map<String, dynamic> json) =>
      _$$_MineBabyInfoFromJson(json);

  @override
  final String? nickname;
  @override
  final int? sex;
  @override
  final String? avatarUrl;
  @override
  final String? birthday;
  @override
  final int? grade;
  @override
  final num? userId;
  @override
  final String? location;
  @override
  final String? family;
  @override
  final String? registerTime;
  @override
  final int? userPoints;
  @override
  final String? userPointsLink;
  @override
  final String? reviewingBabyNickname;

  ///审核中 昵称，宝贝资料编辑
  @override
  final bool? babyNickNameInReview;

  @override
  String toString() {
    return 'MineBabyInfo(nickname: $nickname, sex: $sex, avatarUrl: $avatarUrl, birthday: $birthday, grade: $grade, userId: $userId, location: $location, family: $family, registerTime: $registerTime, userPoints: $userPoints, userPointsLink: $userPointsLink, reviewingBabyNickname: $reviewingBabyNickname, babyNickNameInReview: $babyNickNameInReview)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_MineBabyInfo &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.sex, sex) || other.sex == sex) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            (identical(other.grade, grade) || other.grade == grade) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.family, family) || other.family == family) &&
            (identical(other.registerTime, registerTime) ||
                other.registerTime == registerTime) &&
            (identical(other.userPoints, userPoints) ||
                other.userPoints == userPoints) &&
            (identical(other.userPointsLink, userPointsLink) ||
                other.userPointsLink == userPointsLink) &&
            (identical(other.reviewingBabyNickname, reviewingBabyNickname) ||
                other.reviewingBabyNickname == reviewingBabyNickname) &&
            (identical(other.babyNickNameInReview, babyNickNameInReview) ||
                other.babyNickNameInReview == babyNickNameInReview));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      nickname,
      sex,
      avatarUrl,
      birthday,
      grade,
      userId,
      location,
      family,
      registerTime,
      userPoints,
      userPointsLink,
      reviewingBabyNickname,
      babyNickNameInReview);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MineBabyInfoCopyWith<_$_MineBabyInfo> get copyWith =>
      __$$_MineBabyInfoCopyWithImpl<_$_MineBabyInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MineBabyInfoToJson(
      this,
    );
  }
}

abstract class _MineBabyInfo implements MineBabyInfo {
  const factory _MineBabyInfo(
      {final String? nickname,
      final int? sex,
      final String? avatarUrl,
      final String? birthday,
      final int? grade,
      final num? userId,
      final String? location,
      final String? family,
      final String? registerTime,
      final int? userPoints,
      final String? userPointsLink,
      final String? reviewingBabyNickname,
      final bool? babyNickNameInReview}) = _$_MineBabyInfo;

  factory _MineBabyInfo.fromJson(Map<String, dynamic> json) =
      _$_MineBabyInfo.fromJson;

  @override
  String? get nickname;
  @override
  int? get sex;
  @override
  String? get avatarUrl;
  @override
  String? get birthday;
  @override
  int? get grade;
  @override
  num? get userId;
  @override
  String? get location;
  @override
  String? get family;
  @override
  String? get registerTime;
  @override
  int? get userPoints;
  @override
  String? get userPointsLink;
  @override
  String? get reviewingBabyNickname;
  @override

  ///审核中 昵称，宝贝资料编辑
  bool? get babyNickNameInReview;
  @override
  @JsonKey(ignore: true)
  _$$_MineBabyInfoCopyWith<_$_MineBabyInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

UserInoData _$UserInoDataFromJson(Map<String, dynamic> json) {
  return _UserInoData.fromJson(json);
}

/// @nodoc
mixin _$UserInoData {
  String? get nickname => throw _privateConstructorUsedError;
  int? get sex => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  String? get birthday => throw _privateConstructorUsedError;
  int? get grade => throw _privateConstructorUsedError;
  num? get userId => throw _privateConstructorUsedError;
  String? get location => throw _privateConstructorUsedError;
  String? get family => throw _privateConstructorUsedError;
  String? get registerTime => throw _privateConstructorUsedError;
  int? get userPoints => throw _privateConstructorUsedError;
  String? get userPointsLink => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserInoDataCopyWith<UserInoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserInoDataCopyWith<$Res> {
  factory $UserInoDataCopyWith(
          UserInoData value, $Res Function(UserInoData) then) =
      _$UserInoDataCopyWithImpl<$Res, UserInoData>;
  @useResult
  $Res call(
      {String? nickname,
      int? sex,
      String? avatarUrl,
      String? birthday,
      int? grade,
      num? userId,
      String? location,
      String? family,
      String? registerTime,
      int? userPoints,
      String? userPointsLink});
}

/// @nodoc
class _$UserInoDataCopyWithImpl<$Res, $Val extends UserInoData>
    implements $UserInoDataCopyWith<$Res> {
  _$UserInoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? sex = freezed,
    Object? avatarUrl = freezed,
    Object? birthday = freezed,
    Object? grade = freezed,
    Object? userId = freezed,
    Object? location = freezed,
    Object? family = freezed,
    Object? registerTime = freezed,
    Object? userPoints = freezed,
    Object? userPointsLink = freezed,
  }) {
    return _then(_value.copyWith(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      sex: freezed == sex
          ? _value.sex
          : sex // ignore: cast_nullable_to_non_nullable
              as int?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as String?,
      grade: freezed == grade
          ? _value.grade
          : grade // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as num?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      family: freezed == family
          ? _value.family
          : family // ignore: cast_nullable_to_non_nullable
              as String?,
      registerTime: freezed == registerTime
          ? _value.registerTime
          : registerTime // ignore: cast_nullable_to_non_nullable
              as String?,
      userPoints: freezed == userPoints
          ? _value.userPoints
          : userPoints // ignore: cast_nullable_to_non_nullable
              as int?,
      userPointsLink: freezed == userPointsLink
          ? _value.userPointsLink
          : userPointsLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserInoDataCopyWith<$Res>
    implements $UserInoDataCopyWith<$Res> {
  factory _$$_UserInoDataCopyWith(
          _$_UserInoData value, $Res Function(_$_UserInoData) then) =
      __$$_UserInoDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nickname,
      int? sex,
      String? avatarUrl,
      String? birthday,
      int? grade,
      num? userId,
      String? location,
      String? family,
      String? registerTime,
      int? userPoints,
      String? userPointsLink});
}

/// @nodoc
class __$$_UserInoDataCopyWithImpl<$Res>
    extends _$UserInoDataCopyWithImpl<$Res, _$_UserInoData>
    implements _$$_UserInoDataCopyWith<$Res> {
  __$$_UserInoDataCopyWithImpl(
      _$_UserInoData _value, $Res Function(_$_UserInoData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nickname = freezed,
    Object? sex = freezed,
    Object? avatarUrl = freezed,
    Object? birthday = freezed,
    Object? grade = freezed,
    Object? userId = freezed,
    Object? location = freezed,
    Object? family = freezed,
    Object? registerTime = freezed,
    Object? userPoints = freezed,
    Object? userPointsLink = freezed,
  }) {
    return _then(_$_UserInoData(
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      sex: freezed == sex
          ? _value.sex
          : sex // ignore: cast_nullable_to_non_nullable
              as int?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      birthday: freezed == birthday
          ? _value.birthday
          : birthday // ignore: cast_nullable_to_non_nullable
              as String?,
      grade: freezed == grade
          ? _value.grade
          : grade // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as num?,
      location: freezed == location
          ? _value.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      family: freezed == family
          ? _value.family
          : family // ignore: cast_nullable_to_non_nullable
              as String?,
      registerTime: freezed == registerTime
          ? _value.registerTime
          : registerTime // ignore: cast_nullable_to_non_nullable
              as String?,
      userPoints: freezed == userPoints
          ? _value.userPoints
          : userPoints // ignore: cast_nullable_to_non_nullable
              as int?,
      userPointsLink: freezed == userPointsLink
          ? _value.userPointsLink
          : userPointsLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserInoData implements _UserInoData {
  const _$_UserInoData(
      {this.nickname,
      this.sex,
      this.avatarUrl,
      this.birthday,
      this.grade,
      this.userId,
      this.location,
      this.family,
      this.registerTime,
      this.userPoints,
      this.userPointsLink});

  factory _$_UserInoData.fromJson(Map<String, dynamic> json) =>
      _$$_UserInoDataFromJson(json);

  @override
  final String? nickname;
  @override
  final int? sex;
  @override
  final String? avatarUrl;
  @override
  final String? birthday;
  @override
  final int? grade;
  @override
  final num? userId;
  @override
  final String? location;
  @override
  final String? family;
  @override
  final String? registerTime;
  @override
  final int? userPoints;
  @override
  final String? userPointsLink;

  @override
  String toString() {
    return 'UserInoData(nickname: $nickname, sex: $sex, avatarUrl: $avatarUrl, birthday: $birthday, grade: $grade, userId: $userId, location: $location, family: $family, registerTime: $registerTime, userPoints: $userPoints, userPointsLink: $userPointsLink)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserInoData &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.sex, sex) || other.sex == sex) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.birthday, birthday) ||
                other.birthday == birthday) &&
            (identical(other.grade, grade) || other.grade == grade) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.family, family) || other.family == family) &&
            (identical(other.registerTime, registerTime) ||
                other.registerTime == registerTime) &&
            (identical(other.userPoints, userPoints) ||
                other.userPoints == userPoints) &&
            (identical(other.userPointsLink, userPointsLink) ||
                other.userPointsLink == userPointsLink));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      nickname,
      sex,
      avatarUrl,
      birthday,
      grade,
      userId,
      location,
      family,
      registerTime,
      userPoints,
      userPointsLink);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserInoDataCopyWith<_$_UserInoData> get copyWith =>
      __$$_UserInoDataCopyWithImpl<_$_UserInoData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserInoDataToJson(
      this,
    );
  }
}

abstract class _UserInoData implements UserInoData {
  const factory _UserInoData(
      {final String? nickname,
      final int? sex,
      final String? avatarUrl,
      final String? birthday,
      final int? grade,
      final num? userId,
      final String? location,
      final String? family,
      final String? registerTime,
      final int? userPoints,
      final String? userPointsLink}) = _$_UserInoData;

  factory _UserInoData.fromJson(Map<String, dynamic> json) =
      _$_UserInoData.fromJson;

  @override
  String? get nickname;
  @override
  int? get sex;
  @override
  String? get avatarUrl;
  @override
  String? get birthday;
  @override
  int? get grade;
  @override
  num? get userId;
  @override
  String? get location;
  @override
  String? get family;
  @override
  String? get registerTime;
  @override
  int? get userPoints;
  @override
  String? get userPointsLink;
  @override
  @JsonKey(ignore: true)
  _$$_UserInoDataCopyWith<_$_UserInoData> get copyWith =>
      throw _privateConstructorUsedError;
}

MineGradeInfo _$MineGradeInfoFromJson(Map<String, dynamic> json) {
  return _MineGradeInfo.fromJson(json);
}

/// @nodoc
mixin _$MineGradeInfo {
  BabyInfo? get babyInfo => throw _privateConstructorUsedError;
  set babyInfo(BabyInfo? value) => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MineGradeInfoCopyWith<MineGradeInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MineGradeInfoCopyWith<$Res> {
  factory $MineGradeInfoCopyWith(
          MineGradeInfo value, $Res Function(MineGradeInfo) then) =
      _$MineGradeInfoCopyWithImpl<$Res, MineGradeInfo>;
  @useResult
  $Res call({BabyInfo? babyInfo});

  $BabyInfoCopyWith<$Res>? get babyInfo;
}

/// @nodoc
class _$MineGradeInfoCopyWithImpl<$Res, $Val extends MineGradeInfo>
    implements $MineGradeInfoCopyWith<$Res> {
  _$MineGradeInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? babyInfo = freezed,
  }) {
    return _then(_value.copyWith(
      babyInfo: freezed == babyInfo
          ? _value.babyInfo
          : babyInfo // ignore: cast_nullable_to_non_nullable
              as BabyInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $BabyInfoCopyWith<$Res>? get babyInfo {
    if (_value.babyInfo == null) {
      return null;
    }

    return $BabyInfoCopyWith<$Res>(_value.babyInfo!, (value) {
      return _then(_value.copyWith(babyInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_MineGradeInfoCopyWith<$Res>
    implements $MineGradeInfoCopyWith<$Res> {
  factory _$$_MineGradeInfoCopyWith(
          _$_MineGradeInfo value, $Res Function(_$_MineGradeInfo) then) =
      __$$_MineGradeInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({BabyInfo? babyInfo});

  @override
  $BabyInfoCopyWith<$Res>? get babyInfo;
}

/// @nodoc
class __$$_MineGradeInfoCopyWithImpl<$Res>
    extends _$MineGradeInfoCopyWithImpl<$Res, _$_MineGradeInfo>
    implements _$$_MineGradeInfoCopyWith<$Res> {
  __$$_MineGradeInfoCopyWithImpl(
      _$_MineGradeInfo _value, $Res Function(_$_MineGradeInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? babyInfo = freezed,
  }) {
    return _then(_$_MineGradeInfo(
      babyInfo: freezed == babyInfo
          ? _value.babyInfo
          : babyInfo // ignore: cast_nullable_to_non_nullable
              as BabyInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_MineGradeInfo implements _MineGradeInfo {
  _$_MineGradeInfo({this.babyInfo});

  factory _$_MineGradeInfo.fromJson(Map<String, dynamic> json) =>
      _$$_MineGradeInfoFromJson(json);

  @override
  BabyInfo? babyInfo;

  @override
  String toString() {
    return 'MineGradeInfo(babyInfo: $babyInfo)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_MineGradeInfoCopyWith<_$_MineGradeInfo> get copyWith =>
      __$$_MineGradeInfoCopyWithImpl<_$_MineGradeInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_MineGradeInfoToJson(
      this,
    );
  }
}

abstract class _MineGradeInfo implements MineGradeInfo {
  factory _MineGradeInfo({BabyInfo? babyInfo}) = _$_MineGradeInfo;

  factory _MineGradeInfo.fromJson(Map<String, dynamic> json) =
      _$_MineGradeInfo.fromJson;

  @override
  BabyInfo? get babyInfo;
  set babyInfo(BabyInfo? value);
  @override
  @JsonKey(ignore: true)
  _$$_MineGradeInfoCopyWith<_$_MineGradeInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
