import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/models/graiy_info.dart';

part 'baby_profile_gray_data.freezed.dart';
part 'baby_profile_gray_data.g.dart';

@freezed
class BabyProfileGrayData with _$BabyProfileGrayData{
  const factory BabyProfileGrayData({
      int? babyProfileStyle,
      bool? showBabyProfile,//后端需要新增逻辑 要判断是否有课
      GrayInfo? babyProfileStyleGray,
  }
  ) = _BabyProfileGrayData;

  factory BabyProfileGrayData.fromJson(Map<String, dynamic> json) =>
      _$BabyProfileGrayDataFromJson(json);
}