// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mine_date.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_WrapMineDataInfo _$$_WrapMineDataInfoFromJson(Map<String, dynamic> json) =>
    _$_WrapMineDataInfo(
      userPageInfo: json['userPageInfo'] == null
          ? null
          : MineDataInfo.fromJson(json['userPageInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_WrapMineDataInfoToJson(_$_WrapMineDataInfo instance) =>
    <String, dynamic>{
      'userPageInfo': instance.userPageInfo,
    };

_$_MineDataInfo _$$_MineDataInfoFromJson(Map<String, dynamic> json) =>
    _$_MineDataInfo(
      babyInfo: json['babyInfo'] == null
          ? null
          : MineBabyInfo.fromJson(json['babyInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_MineDataInfoToJson(_$_MineDataInfo instance) =>
    <String, dynamic>{
      'babyInfo': instance.babyInfo,
    };

_$_MineBabyInfo _$$_MineBabyInfoFromJson(Map<String, dynamic> json) =>
    _$_MineBabyInfo(
      nickname: json['nickname'] as String?,
      sex: json['sex'] as int?,
      avatarUrl: json['avatarUrl'] as String?,
      birthday: json['birthday'] as String?,
      grade: json['grade'] as int?,
      userId: json['userId'] as num?,
      location: json['location'] as String?,
      family: json['family'] as String?,
      registerTime: json['registerTime'] as String?,
      userPoints: json['userPoints'] as int?,
      userPointsLink: json['userPointsLink'] as String?,
      reviewingBabyNickname: json['reviewingBabyNickname'] as String?,
      babyNickNameInReview: json['babyNickNameInReview'] as bool?,
    );

Map<String, dynamic> _$$_MineBabyInfoToJson(_$_MineBabyInfo instance) =>
    <String, dynamic>{
      'nickname': instance.nickname,
      'sex': instance.sex,
      'avatarUrl': instance.avatarUrl,
      'birthday': instance.birthday,
      'grade': instance.grade,
      'userId': instance.userId,
      'location': instance.location,
      'family': instance.family,
      'registerTime': instance.registerTime,
      'userPoints': instance.userPoints,
      'userPointsLink': instance.userPointsLink,
      'reviewingBabyNickname': instance.reviewingBabyNickname,
      'babyNickNameInReview': instance.babyNickNameInReview,
    };

_$_UserInoData _$$_UserInoDataFromJson(Map<String, dynamic> json) =>
    _$_UserInoData(
      nickname: json['nickname'] as String?,
      sex: json['sex'] as int?,
      avatarUrl: json['avatarUrl'] as String?,
      birthday: json['birthday'] as String?,
      grade: json['grade'] as int?,
      userId: json['userId'] as num?,
      location: json['location'] as String?,
      family: json['family'] as String?,
      registerTime: json['registerTime'] as String?,
      userPoints: json['userPoints'] as int?,
      userPointsLink: json['userPointsLink'] as String?,
    );

Map<String, dynamic> _$$_UserInoDataToJson(_$_UserInoData instance) =>
    <String, dynamic>{
      'nickname': instance.nickname,
      'sex': instance.sex,
      'avatarUrl': instance.avatarUrl,
      'birthday': instance.birthday,
      'grade': instance.grade,
      'userId': instance.userId,
      'location': instance.location,
      'family': instance.family,
      'registerTime': instance.registerTime,
      'userPoints': instance.userPoints,
      'userPointsLink': instance.userPointsLink,
    };

_$_MineGradeInfo _$$_MineGradeInfoFromJson(Map<String, dynamic> json) =>
    _$_MineGradeInfo(
      babyInfo: json['babyInfo'] == null
          ? null
          : BabyInfo.fromJson(json['babyInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$_MineGradeInfoToJson(_$_MineGradeInfo instance) =>
    <String, dynamic>{
      'babyInfo': instance.babyInfo,
    };
