
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/mobile_for_token/jojo_user.dart';
part 'mine_date.g.dart';
part 'mine_date.freezed.dart';

@freezed
class WrapMineDataInfo with _$WrapMineDataInfo{
  const factory WrapMineDataInfo({
    MineDataInfo? userPageInfo,
  })=_WrapMineDataInfo;
  // 生成 _$WrapMineDataInfoFromJson 和 _$WrapMineDataInfoToJson 方法
  factory WrapMineDataInfo.fromJson(Map<String, dynamic> json) =>
      _$WrapMineDataInfoFromJson(json);
}


@freezed
class MineDataInfo with _$MineDataInfo{
  const factory MineDataInfo({
    MineBabyInfo? babyInfo,
  })=_MineDataInfo;
  // 生成 _$MineDataInfoFromJson 和 _$MineDataInfoToJson 方法
  factory MineDataInfo.fromJson(Map<String, dynamic> json) =>
      _$MineDataInfoFromJson(json);
}

@freezed
class MineBabyInfo with _$MineBabyInfo{
  const factory MineBabyInfo({
    String? nickname,
    int? sex,
    String? avatarUrl,
    String? birthday,
    int? grade,
    num? userId,
    String? location,
    String? family,
    String? registerTime,
    int? userPoints,
    String? userPointsLink,
    String? reviewingBabyNickname,///审核中 昵称，宝贝资料编辑
    bool? babyNickNameInReview,
  })=_MineBabyInfo;
  // 生成 _$MineBabyInfoFromJson 和 _$MineBabyInfoToJson 方法
  factory MineBabyInfo.fromJson(Map<String, dynamic> json) =>
      _$MineBabyInfoFromJson(json);
}

@freezed
class UserInoData with _$UserInoData{
  const factory UserInoData({
    String? nickname,
    int? sex,
    String? avatarUrl,
    String? birthday,
    int? grade,
    num? userId,
    String? location,
    String? family,
    String? registerTime,
    int? userPoints,
    String? userPointsLink,
  })=_UserInoData;
  // 生成 _$UserInoDataFromJson 和 _$UserInoDataToJson 方法
  factory UserInoData.fromJson(Map<String, dynamic> json) => _$UserInoDataFromJson(json);
}

@unfreezed
class MineGradeInfo with _$MineGradeInfo{
  factory MineGradeInfo({
    BabyInfo? babyInfo,
  })=_MineGradeInfo;
  // 生成 _$MineGradeInfoFromJson 和 _$MineGradeInfoToJson 方法
  factory MineGradeInfo.fromJson(Map<String, dynamic> json) =>
      _$MineGradeInfoFromJson(json);
}