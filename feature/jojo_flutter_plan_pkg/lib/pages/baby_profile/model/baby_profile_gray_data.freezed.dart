// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'baby_profile_gray_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

BabyProfileGrayData _$BabyProfileGrayDataFromJson(Map<String, dynamic> json) {
  return _BabyProfileGrayData.fromJson(json);
}

/// @nodoc
mixin _$BabyProfileGrayData {
  int? get babyProfileStyle => throw _privateConstructorUsedError;
  bool? get showBabyProfile =>
      throw _privateConstructorUsedError; //后端需要新增逻辑 要判断是否有课
  GrayInfo? get babyProfileStyleGray => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BabyProfileGrayDataCopyWith<BabyProfileGrayData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BabyProfileGrayDataCopyWith<$Res> {
  factory $BabyProfileGrayDataCopyWith(
          BabyProfileGrayData value, $Res Function(BabyProfileGrayData) then) =
      _$BabyProfileGrayDataCopyWithImpl<$Res, BabyProfileGrayData>;
  @useResult
  $Res call(
      {int? babyProfileStyle,
      bool? showBabyProfile,
      GrayInfo? babyProfileStyleGray});

  $GrayInfoCopyWith<$Res>? get babyProfileStyleGray;
}

/// @nodoc
class _$BabyProfileGrayDataCopyWithImpl<$Res, $Val extends BabyProfileGrayData>
    implements $BabyProfileGrayDataCopyWith<$Res> {
  _$BabyProfileGrayDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? babyProfileStyle = freezed,
    Object? showBabyProfile = freezed,
    Object? babyProfileStyleGray = freezed,
  }) {
    return _then(_value.copyWith(
      babyProfileStyle: freezed == babyProfileStyle
          ? _value.babyProfileStyle
          : babyProfileStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      showBabyProfile: freezed == showBabyProfile
          ? _value.showBabyProfile
          : showBabyProfile // ignore: cast_nullable_to_non_nullable
              as bool?,
      babyProfileStyleGray: freezed == babyProfileStyleGray
          ? _value.babyProfileStyleGray
          : babyProfileStyleGray // ignore: cast_nullable_to_non_nullable
              as GrayInfo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $GrayInfoCopyWith<$Res>? get babyProfileStyleGray {
    if (_value.babyProfileStyleGray == null) {
      return null;
    }

    return $GrayInfoCopyWith<$Res>(_value.babyProfileStyleGray!, (value) {
      return _then(_value.copyWith(babyProfileStyleGray: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_BabyProfileGrayDataCopyWith<$Res>
    implements $BabyProfileGrayDataCopyWith<$Res> {
  factory _$$_BabyProfileGrayDataCopyWith(_$_BabyProfileGrayData value,
          $Res Function(_$_BabyProfileGrayData) then) =
      __$$_BabyProfileGrayDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? babyProfileStyle,
      bool? showBabyProfile,
      GrayInfo? babyProfileStyleGray});

  @override
  $GrayInfoCopyWith<$Res>? get babyProfileStyleGray;
}

/// @nodoc
class __$$_BabyProfileGrayDataCopyWithImpl<$Res>
    extends _$BabyProfileGrayDataCopyWithImpl<$Res, _$_BabyProfileGrayData>
    implements _$$_BabyProfileGrayDataCopyWith<$Res> {
  __$$_BabyProfileGrayDataCopyWithImpl(_$_BabyProfileGrayData _value,
      $Res Function(_$_BabyProfileGrayData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? babyProfileStyle = freezed,
    Object? showBabyProfile = freezed,
    Object? babyProfileStyleGray = freezed,
  }) {
    return _then(_$_BabyProfileGrayData(
      babyProfileStyle: freezed == babyProfileStyle
          ? _value.babyProfileStyle
          : babyProfileStyle // ignore: cast_nullable_to_non_nullable
              as int?,
      showBabyProfile: freezed == showBabyProfile
          ? _value.showBabyProfile
          : showBabyProfile // ignore: cast_nullable_to_non_nullable
              as bool?,
      babyProfileStyleGray: freezed == babyProfileStyleGray
          ? _value.babyProfileStyleGray
          : babyProfileStyleGray // ignore: cast_nullable_to_non_nullable
              as GrayInfo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_BabyProfileGrayData implements _BabyProfileGrayData {
  const _$_BabyProfileGrayData(
      {this.babyProfileStyle, this.showBabyProfile, this.babyProfileStyleGray});

  factory _$_BabyProfileGrayData.fromJson(Map<String, dynamic> json) =>
      _$$_BabyProfileGrayDataFromJson(json);

  @override
  final int? babyProfileStyle;
  @override
  final bool? showBabyProfile;
//后端需要新增逻辑 要判断是否有课
  @override
  final GrayInfo? babyProfileStyleGray;

  @override
  String toString() {
    return 'BabyProfileGrayData(babyProfileStyle: $babyProfileStyle, showBabyProfile: $showBabyProfile, babyProfileStyleGray: $babyProfileStyleGray)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_BabyProfileGrayData &&
            (identical(other.babyProfileStyle, babyProfileStyle) ||
                other.babyProfileStyle == babyProfileStyle) &&
            (identical(other.showBabyProfile, showBabyProfile) ||
                other.showBabyProfile == showBabyProfile) &&
            (identical(other.babyProfileStyleGray, babyProfileStyleGray) ||
                other.babyProfileStyleGray == babyProfileStyleGray));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, babyProfileStyle, showBabyProfile, babyProfileStyleGray);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_BabyProfileGrayDataCopyWith<_$_BabyProfileGrayData> get copyWith =>
      __$$_BabyProfileGrayDataCopyWithImpl<_$_BabyProfileGrayData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_BabyProfileGrayDataToJson(
      this,
    );
  }
}

abstract class _BabyProfileGrayData implements BabyProfileGrayData {
  const factory _BabyProfileGrayData(
      {final int? babyProfileStyle,
      final bool? showBabyProfile,
      final GrayInfo? babyProfileStyleGray}) = _$_BabyProfileGrayData;

  factory _BabyProfileGrayData.fromJson(Map<String, dynamic> json) =
      _$_BabyProfileGrayData.fromJson;

  @override
  int? get babyProfileStyle;
  @override
  bool? get showBabyProfile;
  @override //后端需要新增逻辑 要判断是否有课
  GrayInfo? get babyProfileStyleGray;
  @override
  @JsonKey(ignore: true)
  _$$_BabyProfileGrayDataCopyWith<_$_BabyProfileGrayData> get copyWith =>
      throw _privateConstructorUsedError;
}
