import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/mobile_for_token/jojo_user.dart';
import 'package:jojo_flutter_base/widgets/popup/loading.dart';
import 'package:jojo_flutter_base/widgets/popup/toast.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/service/baby_info_api.dart';

int initAccount = 1;

bool canCommitUserInfo(BabyInfo? babyInfo,bool needCheckAge,bool needCheckGrade) {
  return babyInfo?.nickname?.isNotEmpty == true &&
      babyInfo?.avatarUrl?.isNotEmpty == true &&
      babyInfo?.sex != null &&
      (babyInfo?.birthday?.isNotEmpty == true ||
          needCheckAge == false) &&
      (babyInfo?.grade != null || needCheckGrade == false);
}

void saveBabyInfo(BabyInfoApi babyInfoService, BabyInfo? newBabyInfo,
    BabyInfo? oldBabyInfo, BuildContext context, Function(bool isSaveed, bool isComplete) finishCallback) {
  JoJoLoading.show(msg: "请等待");
  Map<String, dynamic>? newBabyInfoMap = newBabyInfo?.toJson();
  Map<String, dynamic>? oldBabyInfoMap = oldBabyInfo?.toJson();
  Map<String, dynamic>? result = {};
  newBabyInfoMap?.forEach((key, value) {
    if (value != oldBabyInfoMap?[key]) {
      result[key] = value;
    }
  });
  Duration duration = const Duration(milliseconds: 400);
  if (result.isEmpty) {
    JoJoLoading.dismiss(context: context);
    Future.delayed(duration,(){
      finishCallback(false, true);
    });
    return;
  }
  result["defaultInit"] = initAccount;
  Future future = babyInfoService.saveChangeBabyInfo(result);
  future
    ..catchError((error) {
      JoJoLoading.dismiss(context: context);
      JoJoToast.show('叫叫开了会儿小差', JoJoToastType.error);

      Future.delayed(duration,(){
        finishCallback(false, false);
      });
    })
    ..then((value) {
      JoJoLoading.dismiss(context: context);

      Future.delayed(duration,(){
        finishCallback(true, true);
      });
    });
}


bool babyInfoIsComplete(BabyInfo? babyInfo) {
  int? grade = babyInfo?.grade;
  String? birthday = babyInfo?.birthday;
  return grade != null && birthday.isNotNullOrEmpty();
}

class BabyProfileDialogStorage {
  static String get BABY_INFO_SHOW_MONTH_TIME => 'CoursePlanBabyInfoShowMonthTime';

  static Future<void> updateMonthBabyInfoShowTimes() async {

    final lastShowInfo = await jojoNativeBridge.operationNativeValueGet(key: BABY_INFO_SHOW_MONTH_TIME).then((value) => value.data?.value);
    final currentDate = DateTime.now();
    final yearMonthFormatter = DateFormat('yyyyMM');
    final dayFormatter = DateFormat('dd');
    final yearMonth = yearMonthFormatter.format(currentDate);
    final day = dayFormatter.format(currentDate);
    var count = 1;
    String? uid = await jojoNativeBridge.getUserInfo().then((value) => value.data?.uid);
    if (lastShowInfo.isNotNullOrEmpty()) {
      final lastList = lastShowInfo!.split('_');
      // 年月相同的情况下 次数增加
      if (lastList.length == 4 &&
          yearMonth == lastList[1] &&
          uid == lastList[0]) {
        count = int.parse(lastList[3]) + 1;
      }
    }

    await jojoNativeBridge.operationNativeValueSet(
        key:BABY_INFO_SHOW_MONTH_TIME,
        value: '${uid}_${yearMonth}_${day}_$count'
    );
  }

  static Future<bool> monthBabyInfoCanShow() async {
    final lastShowInfo = await jojoNativeBridge.operationNativeValueGet(key: BABY_INFO_SHOW_MONTH_TIME).then((value) => value.data?.value);
    if (lastShowInfo == null || lastShowInfo.isEmpty) {
      return true;
    }

    final lastList = lastShowInfo.split('_');
    if (lastList.length != 4) { // 格式不对 进行显示
      return true;
    }
    String? uid = await jojoNativeBridge.getUserInfo().then((value) => value.data?.uid);
    // uid 不同 用户发生改变 重新计数
    if (lastList[0] != uid) {
      return true;
    }

    final currentDate = DateTime.now();
    final yearMonthFormatter = DateFormat('yyyyMM');
    final dayFormatter = DateFormat('dd');
    final yearMonth = yearMonthFormatter.format(currentDate);
    final day = dayFormatter.format(currentDate);

    // 当天已经显示过了 不显示
    if (yearMonth == lastList[1] && day == lastList[2]) {
      return false;
    }

    // 当月已经显示过2次 不显示
    if (yearMonth == lastList[1] && int.parse(lastList[3]) >= 2) {
      return false;
    }

    return true;
  }
}
