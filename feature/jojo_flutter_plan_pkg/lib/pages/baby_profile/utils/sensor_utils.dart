import 'package:jojo_flutter_base/mobile_for_token/jojo_user.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';

class BabyProfileSensorUtils {
  static void elementView(
      BabyInfo? babyInfo, String cElementName, String pageName) {
    Map<String, dynamic>? properties =
        _buildCommonProWithBabyInfo(pageName, babyInfo);
    properties?.addAll({
      'c_element_name': cElementName,
    });
    RunEnv.sensorsTrack('ElementView', properties);
  }

  static void elementClick(
      BabyInfo? babyInfo, String elementName, String pageName) {
    Map<String, dynamic>? properties =
        _buildCommonProWithBabyInfo(pageName, babyInfo);
    properties?.addAll({
      '\$element_name': elementName,
    });
    RunEnv.sensorsTrack('\$AppClick', properties);
  }

  static Map<String, dynamic>? _buildCommonProWithBabyInfo(
      String pageName, BabyInfo? babyInfo) {
    return _buildCommonPro(
        pageName: pageName,
        avatarUrl: babyInfo?.avatarUrl,
        nickName: babyInfo?.nickname,
        birthday: babyInfo?.birthday,
        grade: babyInfo?.grade);
  }

  // 在 BabyProfileSensorUtils 类中添加以下方法
  static Map<String, dynamic> _buildCommonPro({
    required String pageName,
    required String? avatarUrl,
    required String? nickName,
    required String? birthday,
    required int? grade,
  }) {
    final Map<String, dynamic> data = {};

    if (pageName.isNotEmpty) {
      data['\$screen_name'] = pageName;
    }

    // 上报是否有头像
    data['custom_state'] =
        (avatarUrl != null && avatarUrl.isNotEmpty) ? '是' : '否';

    // 上报是否填写昵称
    data['\$element_content'] =
        (nickName != null && nickName.trim().isNotEmpty == true) ? '是' : '否';

    // 上报是否选择出生日期
    data['material_id'] = (birthday != null && birthday.isNotEmpty) ? '是' : '否';

    // 上报是否选择年级
    data['material_type'] = (grade == null || grade == 0) ? '否' : '是';

    return data;
  }
}
