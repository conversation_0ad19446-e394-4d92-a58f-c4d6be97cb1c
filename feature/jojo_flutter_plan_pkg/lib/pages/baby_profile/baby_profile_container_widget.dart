import 'dart:math';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/models/baby_grade.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:jojo_flutter_base/widgets/common/click_widget.dart';
import 'package:jojo_flutter_base/widgets/common/image_asset_pro.dart';
import 'package:jojo_flutter_plan_pkg/common/config/config.dart';
import 'package:jojo_flutter_plan_pkg/common/host_env/host_env.dart';
import 'package:jojo_flutter_plan_pkg/ext/string_ext.dart';
import 'package:jojo_flutter_plan_pkg/generated/l10n.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/utils/baby_profile_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/utils/sensor_utils.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/widget/avator_and_nick_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/widget/grade_selection_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/widget/item_select_widget.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/widget/user_id_widget.dart';
import 'package:jojo_flutter_plan_pkg/service/baby_info_api.dart';
import 'package:jojo_flutter_plan_pkg/static/img.dart';
import 'package:jojo_flutter_plan_pkg/widgets/common/flutter_datetime_picker/flutter_datetime_picker.dart';

///宝贝档案容器
class BabyProfileContainerWidget extends StatefulWidget {
  final BabyProfileState state;
  final bool needCheckAge;
  final bool needCheckGrade;
  final String pageName;
  final Function(bool isSaveed, bool isComplete) saveBabyInfoCallback;

  const BabyProfileContainerWidget(
      {super.key,
      required this.state,
      required this.needCheckAge,
      required this.needCheckGrade,
      required this.saveBabyInfoCallback,
      required this.pageName});

  @override
  State<StatefulWidget> createState() {
    return _BabyProfileContainerWidgetState();
  }
}

class _BabyProfileContainerWidgetState extends State<BabyProfileContainerWidget>
    with WidgetsBindingObserver {
  //保存宝贝信息时 传递给后端 让后端知道是否需要 在无宝贝信息时 进行初始化
  final int initAccount = 1;

  _BabyProfileContainerWidgetState();

  late TapGestureRecognizer _tapGestureRecognizer;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _tapGestureRecognizer = TapGestureRecognizer()..onTap = handleTap;
    _focusNode = FocusNode();
    WidgetsBinding.instance.addObserver(this);
    BabyProfileSensorUtils.elementView(
        widget.state.babyInfo, "宝贝资料", widget.pageName);
  }

  @override
  dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _tapGestureRecognizer.dispose();
    _focusNode.unfocus();
    _focusNode.dispose();
  }

  @override
  didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      _focusNode.unfocus();
    }
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
  }

  void handleTap() {
    String url =
        "tinman-router://cn.tinman.jojoread/privacy?agreementKey=childrenPolicy";
    RunEnv.jumpLink(url);
  }

  @override
  Widget build(BuildContext context) {
    double height = max(MediaQuery.of(context).size.height, 667.rdp);
    l.d(babyProfileLogTag, "height can use $height");
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        _focusNode.unfocus();
      },
      child: SizedBox(
        height: height,
        child: Stack(children: [
          buildBgWidget(context),
          buildCardBgWidget(context),
          Padding(
            padding: EdgeInsets.only(
                left: 20.rdp, right: 20.rdp, top: 60.rdp, bottom: 35.rdp),
            child: SingleChildScrollView(
              padding: EdgeInsets.zero,
              child: SizedBox(
                height: height-60.rdp-35.rdp,
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ImageAssetWeb(
                      assetName: AssetsImg.BABY_PROFILE_BABY_PROFILE_TITLE,
                      package: Config.package,
                      width: 230.rdp,
                      height: 44.rdp,
                    ),
                    SizedBox(
                      height: 54.rdp,
                    ),
                    AvatarAndNickWidget(
                        avatorCallback: (avatarList) {
                          widget.state.babyInfo?.avatarUrl =
                              avatarList.avatarUrl;
                          widget.state.babyInfo?.avatar = avatarList.avatarOss;
                          safeSetState();
                        },
                        nickNameCallback: (nickName) {
                          widget.state.babyInfo?.nickname = nickName;
                          safeSetState();
                        },
                        babyInfo: widget.state.babyInfo,
                        avatarList: widget.state.defaultAvatarList,
                        focusNode: _focusNode),
                    SizedBox(
                      height: 30.rdp,
                    ),
                    const UserIdWidget(),
                    if (widget.needCheckGrade || widget.needCheckAge)
                      SizedBox(
                        height: 24.rdp,
                      ),
                    buildGradeWidget(context),
                    buildBirthWidget(context),
                    _buildTipText(context),
                    const Expanded(child: SizedBox.shrink()),
                    _buildSureButton(context),
                    const Expanded(child: SizedBox.shrink()),
                    SizedBox(
                      height: 30.rdp,
                    ),
                    _buildPrivacyPolicyText(context),
                    SizedBox(
                      height: 0.rdp,
                    ),
                  ],
                ),
              ),
            ),
          )
        ]),
      ),
    );
  }

  void safeSetState() {
    if (mounted) {
      setState(() {});
    }
  }

  // 添加隐私协议文本组件
  Widget _buildPrivacyPolicyText(BuildContext context) {
    return Container(
      child: Center(
        child: Text.rich(
          TextSpan(
              text: S.of(context).babyProfileJojoWill,
              style: TextStyle(
                  fontSize: 12.rdp, color: context.appColors.jColorGray4),
              children: [
                TextSpan(
                    text: S.of(context).babyProfileChildrenPrivacyPolicy,
                    style: TextStyle(
                        fontSize: 12.rdp,
                        color: context.appColors.jColorOrange4),
                    recognizer: _tapGestureRecognizer),
                TextSpan(
                  text: S.of(context).babyProfileSaveInformation,
                  style: TextStyle(
                      fontSize: 12.rdp, color: context.appColors.jColorGray4),
                )
              ]),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildTipText(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 87.rdp),
      child: Center(
        child: Text(
          S.of(context).babyProfileTipText,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14.rdp,
            height: 1.5,
            color: context.appColors.jColorBrown5.withOpacity(0.7),
          ),
        ),
      ),
    );
  }

  // 获取年级名称
  String gradeName(int? code) {
    final _gradeDictList = widget.state.babyGradeInfo?.gradeDictList;
    var _str = '';
    if (_gradeDictList?.isNotEmpty == true) {
      for (var item in _gradeDictList!) {
        if (item.subDict?.isNotEmpty == true) {
          for (var grade in item.subDict!) {
            if (grade.code == code) {
              _str = grade.name ?? '';
            }
          }
        }
      }
    }
    return _str;
  }

  Widget buildGradeWidget(BuildContext context) {
    if (!widget.needCheckGrade) {
      return SizedBox(
        height: 44.rdp,
      );
    }

    return ItemSelectWidget(
      title: S.of(context).grade,
      selectShowText: gradeName(widget.state.babyInfo?.grade),
      noSelectShowText: S.of(context).pleaseSelect,
      onSelectTap: () {
        _focusNode.unfocus();
        //展示弹窗 更新年级信息
        showGradeDialog();
      },
    );
  }

  // 判断闰年的辅助函数
  bool _isLeapYear(int year) {
    return (year % 4 == 0) && (year % 100 != 0 || year % 400 == 0);
  }

  // 获取当前时间减-三年的操作
  DateTime getCurrentTime() {
    DateTime now = DateTime.now();
    int targetYear = now.year - 3;

    /// 处理2月29日的情况
    DateTime threeYearsAgo;
    if (now.month == 2 && now.day == 29 && !_isLeapYear(targetYear)) {
      threeYearsAgo = DateTime(targetYear, 2, 28, now.hour, now.minute,
          now.second, now.millisecond, now.microsecond);
    } else {
      threeYearsAgo = DateTime(targetYear, now.month, now.day, now.hour,
          now.minute, now.second, now.millisecond, now.microsecond);
    }
    return threeYearsAgo;
  }

  Widget buildBirthWidget(BuildContext context) {
    if (!widget.needCheckAge) {
      return SizedBox(
        height: 44.rdp,
      );
    }

    DateTime dateTime = getCurrentTime();

    try {
      String dateString = widget.state.babyInfo?.birthday ?? '';
      DateFormat format = DateFormat("yyyy-MM-dd");
      dateTime = format.parse(dateString);
      print(dateTime);
    } catch (e) {
      dateTime = getCurrentTime();
      print("日期解析错误: $e");
    }
    String? showDateString;
    try {
      String? dateString = widget.state.babyInfo?.birthday;
      if (dateString != null && dateString.isNotNullOrEmpty()) {
        DateFormat format = DateFormat("yyyy-MM-dd");
        DateTime currentTime = format.parse(dateString);
        showDateString =
            DateFormat(S.of(context).babyProfileTimeFormat).format(currentTime);
      }
    } catch (e) {
      l.e(babyProfileLogTag, "宝贝生日数据解析异常原始数据 $e ");
    }

    return ItemSelectWidget(
      title: S.of(context).birthday,
      selectShowText: showDateString,
      noSelectShowText: S.of(context).pleaseSelect,
      onSelectTap: () {
        _focusNode.unfocus();

        RunEnv.sensorsTrack('\$AppClick', {
          '\$element_name': '宝贝资料_年龄点击',
          '\$screen_name': widget.pageName,
        });

        RunEnv.sensorsTrack('ElementView', {
          'c_element_name': '宝贝资料_年龄弹窗',
          '\$screen_name': widget.pageName,
        });

        //展示弹窗 更新生日信息
        DatePicker.showDatePicker(
          context,
          showTitleActions: true,
          minTime: DateTime(2005, 1, 1),
          maxTime: DateTime.now(),
          currentTime: dateTime,
          onCancel: () {
            RunEnv.sensorsTrack('\$AppClick', {
              '\$element_name': '宝贝资料_年龄弹窗_关闭',
              '\$screen_name': widget.pageName,
            });
          },
          onConfirm: (date) {
            String formattedDate = DateFormat('yyyy-MM-dd').format(date);
            if (mounted) {
              setState(() {
                widget.state.babyInfo?.birthday = formattedDate;
              });
            }
            RunEnv.sensorsTrack('\$AppClick', {
              '\$element_name': '宝贝资料_年龄弹窗_年龄选择',
              '\$screen_name': widget.pageName,
            });
          },
          locale: LocaleType.zh,
        );
      },
    );
  }

  Positioned buildCardBgWidget(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: SizedBox(
        width: double.infinity,
        child: ImageAssetWeb(
          assetName: AssetsImg.BABY_PROFILE_BABY_PROFILE_BG,
          package: Config.package,
          width: MediaQuery.of(context).size.width,
          fit: BoxFit.fitWidth,
        ),
      ),
    );
  }

  Positioned buildBgWidget(BuildContext context) {
    return Positioned(
        left: 0,
        right: 0,
        bottom: 0,
        child: SizedBox(
          width: double.infinity,
          height: MediaQuery.of(context).size.height,
          child: Container(
            color: context.appColors.jColorYellow1,
          ),
        ));
  }

  Widget _buildSureButton(BuildContext context) {
    bool canCommit = canCommitUserInfo(
        widget.state.babyInfo, widget.needCheckAge, widget.needCheckGrade);
    Color textColor = canCommit
        ? context.appColors.jColorYellow6
        : context.appColors.jColorGray4;
    Color borderColor = canCommit
        ? context.appColors.jColorYellow4
        : context.appColors.jColorGray2;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ClickWidget(
          type: ClickType.debounce,
          onTap: () {
            if (canCommit) {
              BabyProfileSensorUtils.elementClick(
                  widget.state.babyInfo, "宝贝资料_点击确认按钮", widget.pageName);
              saveBabyInfo(babyInfoService, widget.state.babyInfo,
                  widget.state.oldBabyInfo, context, (bool isSaveed, bool isComplete) {
                widget.saveBabyInfoCallback(isSaveed, isComplete);
              });
            }
          },
          child: Container(
            height: 44.rdp,
            width: 200.rdp,
            decoration: BoxDecoration(
              color: borderColor, // 背景颜色
              borderRadius: BorderRadius.circular(30.rdp), // 设置圆角
            ),
            child: Center(
              child: Text(
                S.of(context).babyProfileLinkCard,
                style: TextStyle(
                    color: textColor,
                    fontSize: 18.rdp,
                    fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 年级弹窗
  showGradeDialog() {
    RunEnv.sensorsTrack('\$AppClick', {
      '\$element_name': '宝贝资料_年级点击',
      '\$screen_name': widget.pageName,
    });
    if (widget.state.babyGradeInfo != null) {
      SmartDialog.show(
        backDismiss: true,
        clickMaskDismiss: true,
        alignment: Alignment.bottomCenter,
        builder: ((dialogContext) {
          return GradeSelectionWidget(
            babyGradeInfo: widget.state.babyGradeInfo!,
            selectCode: widget.state.babyInfo?.grade,
            pageName: widget.pageName,
            onGradeSelected: (GradeInfo gradeInfo) {
              RunEnv.sensorsTrack('\$AppClick', {
                '\$element_name': '宝贝资料_年级弹窗_年级选择',
                '\$screen_name': widget.pageName,
              });
              if (mounted) {
                setState(() {
                  widget.state.babyInfo?.grade = gradeInfo.code;
                });
              }
              SmartDialog.dismiss();
            },
          );
        }),
      );
    }
  }
}
