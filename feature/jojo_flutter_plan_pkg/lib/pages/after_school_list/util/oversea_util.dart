import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/model/course_after_servers_data.dart';

enum ServerStoryKeyType {
  // 对应后端返回的 key， 和后端确认当前只有这两个会跳伴读相关页面
  sweetownStory("sweetown-story"),
  dubStory("dub-story");

  final String rawValue;

  const ServerStoryKeyType(this.rawValue);
}

class OverseaUtil {

  static List<ServerInfoList>? getServerInfoList(List<ServerInfoList>? sourceList, bool isOversea) {
    if (isOversea) {
      final serverStoryKeyList = ServerStoryKeyType.values.map((e) => e.rawValue).toList();
      return sourceList?.where((element) {
        return !serverStoryKeyList.contains(element.key);
      }).toList();
    }
    return sourceList;
  }
}