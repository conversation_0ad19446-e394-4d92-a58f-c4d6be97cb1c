import 'dart:io';
import 'package:archive/archive.dart';
import 'package:jojo_flutter_base/utils/iterable_extension.dart';
import 'package:jojo_flutter_base/utils/log.dart';
import 'package:path/path.dart' as path;

const String tag = "试用版---弹窗引导-PageGuideHelper-UniformDialogSequenceTag";

Future<String> unzip(String filepath) async {
  final file = File(filepath);
  String filename = getFileNameWithoutExtension(file);
  Directory dir = file.parent;
  String dirPath = path.join(dir.path, filename);

  bool exist = await Directory(dirPath).exists();
  if (exist) {
    l.i(tag, "文件夹已存在，正在删除: $dirPath");
    await Directory(dirPath).delete(recursive: true);
  }

  try {
    final bytes = file.readAsBytesSync();
    final archive = ZipDecoder().decodeBytes(bytes);

    for (final fileInArchive in archive) {
      final absolutePath = path.join(dirPath, fileInArchive.name);
      if (fileInArchive.isFile) {
        final data = fileInArchive.content as List<int>;
        File(absolutePath)
          ..createSync(recursive: true)
          ..writeAsBytesSync(data);
      } else {
        Directory(absolutePath).createSync(recursive: true);
      }
    }

    return dirPath;
  } catch (e, stackTrace) {
    l.e("unzip", "解压失败: $e\n$stackTrace");
    // 删除可能创建但不完整的目录
    Directory(dirPath).deleteSync(recursive: true);
    rethrow;
  }
}

/// 获取去掉后缀的文件名称
String getFileNameWithoutExtension(File file) {
  String fileName = file.uri.pathSegments.last;
  int lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex != -1) {
    return fileName.substring(0, lastDotIndex);
  } else {
    return fileName;
  }
}

/// 递归查找目标文件所在的目录 BFS算法
/// [rootPath] 根目录全路径地址
/// [target] 文件名称
Future<String> findDirectory(String rootPath, String target) async {
  bool isDirectory = await FileSystemEntity.isDirectory(rootPath);
  if (isDirectory) {
    final list = Directory(rootPath).listSync();
    while (list.isNotEmpty) {
      FileSystemEntity entity = list.removeAt(0);
      String dirPath = entity.path;

      if (await FileSystemEntity.isDirectory(dirPath)) {
        String filepath = path.join(dirPath, target);
        bool hasTarget = await File(filepath).exists();
        if (hasTarget) {
          return dirPath;
        }

        final children = Directory(dirPath).listSync();
        list.addAll(children);
      }
    }
  }

  return "";
}

/// 在指定目录下查找具有特定后缀名的第一个文件
File? findFilesByExtension(String directoryPath, String extension) {
  // 创建 Directory 对象
  final directory = Directory(directoryPath);

  // 检查目录是否存在
  if (!directory.existsSync()) {
    return null;
  }

  // 列出目录中的所有文件和子目录
  final files = directory.listSync(recursive: true);

  // 过滤出具有特定后缀名的文件
  return files.whereType<File>().firstWhereOrNull((file) =>
      file.path.endsWith(extension) &&
      !path.basename(file.path).startsWith('.'));
}

//在指定目录下查找具有名称的文件
File? findFilesByName(String directoryPath, String fileName) {
  // 创建 Directory 对象
  final directory = Directory(directoryPath);

  // 检查目录是否存在
  if (!directory.existsSync()) {
    l.i("文件异常", "file_util_file not found");
    return null;
  }

  // 列出目录中的所有文件和子目录
  final files = directory.listSync(recursive: true);

  // 过滤出具有特定名称的文件
  return files.whereType<File>().firstWhereOrNull((file) =>
      path.basename(file.path) == fileName &&
      !path.basename(file.path).startsWith('.'));
}

//在指定目录下查找具有名称的文件
File? findFilesByEnds(String directoryPath, String ends) {
  // 创建 Directory 对象
  final directory = Directory(directoryPath);

  // 检查目录是否存在
  if (!directory.existsSync()) {
    return null;
  }

  // 列出目录中的所有文件和子目录
  final files = directory.listSync(recursive: true);

  // 过滤出具有特定名称的文件
  return files
      .whereType<File>()
      .firstWhereOrNull((file) => file.path.endsWith(ends));
}

///在指定目录下查找具有特定后缀名的文件集合
List<File> findImagesByExtensions(
    String directoryPath, List<String> extensions) {
  // 创建 Directory 对象
  final directory = Directory(directoryPath);

  // 检查目录是否存在
  if (!directory.existsSync()) {
    l.i("文件异常", "file_utils findImagesByExtensions--file not found");
  }

  // 列出目录中的所有文件和子目录
  final files = directory.listSync(recursive: true);

  // 过滤出具有特定后缀名的文件
  return files
      .whereType<File>()
      .where((file) =>
          extensions.any(
              (extension) => file.path.toLowerCase().endsWith(extension)) &&
          !path.basename(file.path).startsWith('.'))
      .toList();
}

/// 删除目录下的无用文件，避免查找到 __MACOSX 下的文件
Future<void> removeUselessFilesAndDirs(Directory directory) async {
  if (!directory.existsSync()) {
    l.w("FileUtil", '目录 "$directory" 不存在');
    return;
  }

  try {
    // 遍历当前目录下的所有实体（文件或文件夹）
    for (var entity in directory.listSync()) {
      if (entity is Directory) {
        // 检查是否为名称为 "__MACOSX" 的文件夹
        if (path.basename(entity.path) == "__MACOSX") {
          try {
            await entity.delete(recursive: true);
            l.i("FileUtil", '已删除目录: ${entity.path}');
          } catch (e) {
            l.w("FileUtil", '删除目录 ${entity.path} 失败: $e');
          }
        } else {
          // 递归遍历子目录
          removeUselessFilesAndDirs(entity);
        }
      } else if (entity is File) {
        // 检查是否为 ".DS_Store" 文件
        if (path.basename(entity.path) == ".DS_Store") {
          try {
            await entity.delete();
            l.i("FileUtil", '已删除文件: ${entity.path}');
          } catch (e) {
            l.w("FileUtil", '删除文件 ${entity.path} 失败: $e');
          }
        }
      }
    }
  } catch (e) {
    l.w("FileUtil", '处理目录 $directory 发生异常: $e');
  }
}

extension StringListExtension on List<String> {
  addIfNotEmpty(String? value) {
    if (value != null && value.isNotEmpty == true) {
      add(value);
    }
  }

  addListsIfNotEmpty(Iterable<String?> list) {
    for (var element in list) {
      addIfNotEmpty(element);
    }
  }
}
