// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'activity_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

PlanActivityData _$PlanActivityDataFromJson(Map<String, dynamic> json) {
  return _PlanActivityData.fromJson(json);
}

/// @nodoc
mixin _$PlanActivityData {
  int? get activityId => throw _privateConstructorUsedError;
  String? get activitySubject => throw _privateConstructorUsedError;
  ClassActivitiesThemeCardVo? get themeCard =>
      throw _privateConstructorUsedError;
  List<ClassActivitiesTaskVo?>? get tasks => throw _privateConstructorUsedError;
  List<PlanActivityFreePagesVo?>? get freePages =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanActivityDataCopyWith<PlanActivityData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanActivityDataCopyWith<$Res> {
  factory $PlanActivityDataCopyWith(
          PlanActivityData value, $Res Function(PlanActivityData) then) =
      _$PlanActivityDataCopyWithImpl<$Res, PlanActivityData>;
  @useResult
  $Res call(
      {int? activityId,
      String? activitySubject,
      ClassActivitiesThemeCardVo? themeCard,
      List<ClassActivitiesTaskVo?>? tasks,
      List<PlanActivityFreePagesVo?>? freePages});

  $ClassActivitiesThemeCardVoCopyWith<$Res>? get themeCard;
}

/// @nodoc
class _$PlanActivityDataCopyWithImpl<$Res, $Val extends PlanActivityData>
    implements $PlanActivityDataCopyWith<$Res> {
  _$PlanActivityDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activityId = freezed,
    Object? activitySubject = freezed,
    Object? themeCard = freezed,
    Object? tasks = freezed,
    Object? freePages = freezed,
  }) {
    return _then(_value.copyWith(
      activityId: freezed == activityId
          ? _value.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as int?,
      activitySubject: freezed == activitySubject
          ? _value.activitySubject
          : activitySubject // ignore: cast_nullable_to_non_nullable
              as String?,
      themeCard: freezed == themeCard
          ? _value.themeCard
          : themeCard // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesThemeCardVo?,
      tasks: freezed == tasks
          ? _value.tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskVo?>?,
      freePages: freezed == freePages
          ? _value.freePages
          : freePages // ignore: cast_nullable_to_non_nullable
              as List<PlanActivityFreePagesVo?>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesThemeCardVoCopyWith<$Res>? get themeCard {
    if (_value.themeCard == null) {
      return null;
    }

    return $ClassActivitiesThemeCardVoCopyWith<$Res>(_value.themeCard!,
        (value) {
      return _then(_value.copyWith(themeCard: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_PlanActivityDataCopyWith<$Res>
    implements $PlanActivityDataCopyWith<$Res> {
  factory _$$_PlanActivityDataCopyWith(
          _$_PlanActivityData value, $Res Function(_$_PlanActivityData) then) =
      __$$_PlanActivityDataCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? activityId,
      String? activitySubject,
      ClassActivitiesThemeCardVo? themeCard,
      List<ClassActivitiesTaskVo?>? tasks,
      List<PlanActivityFreePagesVo?>? freePages});

  @override
  $ClassActivitiesThemeCardVoCopyWith<$Res>? get themeCard;
}

/// @nodoc
class __$$_PlanActivityDataCopyWithImpl<$Res>
    extends _$PlanActivityDataCopyWithImpl<$Res, _$_PlanActivityData>
    implements _$$_PlanActivityDataCopyWith<$Res> {
  __$$_PlanActivityDataCopyWithImpl(
      _$_PlanActivityData _value, $Res Function(_$_PlanActivityData) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activityId = freezed,
    Object? activitySubject = freezed,
    Object? themeCard = freezed,
    Object? tasks = freezed,
    Object? freePages = freezed,
  }) {
    return _then(_$_PlanActivityData(
      activityId: freezed == activityId
          ? _value.activityId
          : activityId // ignore: cast_nullable_to_non_nullable
              as int?,
      activitySubject: freezed == activitySubject
          ? _value.activitySubject
          : activitySubject // ignore: cast_nullable_to_non_nullable
              as String?,
      themeCard: freezed == themeCard
          ? _value.themeCard
          : themeCard // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesThemeCardVo?,
      tasks: freezed == tasks
          ? _value._tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskVo?>?,
      freePages: freezed == freePages
          ? _value._freePages
          : freePages // ignore: cast_nullable_to_non_nullable
              as List<PlanActivityFreePagesVo?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanActivityData implements _PlanActivityData {
  const _$_PlanActivityData(
      {this.activityId,
      this.activitySubject,
      this.themeCard,
      final List<ClassActivitiesTaskVo?>? tasks,
      final List<PlanActivityFreePagesVo?>? freePages})
      : _tasks = tasks,
        _freePages = freePages;

  factory _$_PlanActivityData.fromJson(Map<String, dynamic> json) =>
      _$$_PlanActivityDataFromJson(json);

  @override
  final int? activityId;
  @override
  final String? activitySubject;
  @override
  final ClassActivitiesThemeCardVo? themeCard;
  final List<ClassActivitiesTaskVo?>? _tasks;
  @override
  List<ClassActivitiesTaskVo?>? get tasks {
    final value = _tasks;
    if (value == null) return null;
    if (_tasks is EqualUnmodifiableListView) return _tasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<PlanActivityFreePagesVo?>? _freePages;
  @override
  List<PlanActivityFreePagesVo?>? get freePages {
    final value = _freePages;
    if (value == null) return null;
    if (_freePages is EqualUnmodifiableListView) return _freePages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PlanActivityData(activityId: $activityId, activitySubject: $activitySubject, themeCard: $themeCard, tasks: $tasks, freePages: $freePages)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanActivityData &&
            (identical(other.activityId, activityId) ||
                other.activityId == activityId) &&
            (identical(other.activitySubject, activitySubject) ||
                other.activitySubject == activitySubject) &&
            (identical(other.themeCard, themeCard) ||
                other.themeCard == themeCard) &&
            const DeepCollectionEquality().equals(other._tasks, _tasks) &&
            const DeepCollectionEquality()
                .equals(other._freePages, _freePages));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      activityId,
      activitySubject,
      themeCard,
      const DeepCollectionEquality().hash(_tasks),
      const DeepCollectionEquality().hash(_freePages));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanActivityDataCopyWith<_$_PlanActivityData> get copyWith =>
      __$$_PlanActivityDataCopyWithImpl<_$_PlanActivityData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanActivityDataToJson(
      this,
    );
  }
}

abstract class _PlanActivityData implements PlanActivityData {
  const factory _PlanActivityData(
      {final int? activityId,
      final String? activitySubject,
      final ClassActivitiesThemeCardVo? themeCard,
      final List<ClassActivitiesTaskVo?>? tasks,
      final List<PlanActivityFreePagesVo?>? freePages}) = _$_PlanActivityData;

  factory _PlanActivityData.fromJson(Map<String, dynamic> json) =
      _$_PlanActivityData.fromJson;

  @override
  int? get activityId;
  @override
  String? get activitySubject;
  @override
  ClassActivitiesThemeCardVo? get themeCard;
  @override
  List<ClassActivitiesTaskVo?>? get tasks;
  @override
  List<PlanActivityFreePagesVo?>? get freePages;
  @override
  @JsonKey(ignore: true)
  _$$_PlanActivityDataCopyWith<_$_PlanActivityData> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassActivitiesThemeCardVo _$ClassActivitiesThemeCardVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesThemeCardVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesThemeCardVo {
  String? get progressHeadColor => throw _privateConstructorUsedError;
  String? get progressTailColor => throw _privateConstructorUsedError;
  String? get backgroundRes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesThemeCardVoCopyWith<ClassActivitiesThemeCardVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesThemeCardVoCopyWith<$Res> {
  factory $ClassActivitiesThemeCardVoCopyWith(ClassActivitiesThemeCardVo value,
          $Res Function(ClassActivitiesThemeCardVo) then) =
      _$ClassActivitiesThemeCardVoCopyWithImpl<$Res,
          ClassActivitiesThemeCardVo>;
  @useResult
  $Res call(
      {String? progressHeadColor,
      String? progressTailColor,
      String? backgroundRes});
}

/// @nodoc
class _$ClassActivitiesThemeCardVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesThemeCardVo>
    implements $ClassActivitiesThemeCardVoCopyWith<$Res> {
  _$ClassActivitiesThemeCardVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progressHeadColor = freezed,
    Object? progressTailColor = freezed,
    Object? backgroundRes = freezed,
  }) {
    return _then(_value.copyWith(
      progressHeadColor: freezed == progressHeadColor
          ? _value.progressHeadColor
          : progressHeadColor // ignore: cast_nullable_to_non_nullable
              as String?,
      progressTailColor: freezed == progressTailColor
          ? _value.progressTailColor
          : progressTailColor // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundRes: freezed == backgroundRes
          ? _value.backgroundRes
          : backgroundRes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesThemeCardVoCopyWith<$Res>
    implements $ClassActivitiesThemeCardVoCopyWith<$Res> {
  factory _$$_ClassActivitiesThemeCardVoCopyWith(
          _$_ClassActivitiesThemeCardVo value,
          $Res Function(_$_ClassActivitiesThemeCardVo) then) =
      __$$_ClassActivitiesThemeCardVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? progressHeadColor,
      String? progressTailColor,
      String? backgroundRes});
}

/// @nodoc
class __$$_ClassActivitiesThemeCardVoCopyWithImpl<$Res>
    extends _$ClassActivitiesThemeCardVoCopyWithImpl<$Res,
        _$_ClassActivitiesThemeCardVo>
    implements _$$_ClassActivitiesThemeCardVoCopyWith<$Res> {
  __$$_ClassActivitiesThemeCardVoCopyWithImpl(
      _$_ClassActivitiesThemeCardVo _value,
      $Res Function(_$_ClassActivitiesThemeCardVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? progressHeadColor = freezed,
    Object? progressTailColor = freezed,
    Object? backgroundRes = freezed,
  }) {
    return _then(_$_ClassActivitiesThemeCardVo(
      progressHeadColor: freezed == progressHeadColor
          ? _value.progressHeadColor
          : progressHeadColor // ignore: cast_nullable_to_non_nullable
              as String?,
      progressTailColor: freezed == progressTailColor
          ? _value.progressTailColor
          : progressTailColor // ignore: cast_nullable_to_non_nullable
              as String?,
      backgroundRes: freezed == backgroundRes
          ? _value.backgroundRes
          : backgroundRes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesThemeCardVo implements _ClassActivitiesThemeCardVo {
  const _$_ClassActivitiesThemeCardVo(
      {this.progressHeadColor, this.progressTailColor, this.backgroundRes});

  factory _$_ClassActivitiesThemeCardVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesThemeCardVoFromJson(json);

  @override
  final String? progressHeadColor;
  @override
  final String? progressTailColor;
  @override
  final String? backgroundRes;

  @override
  String toString() {
    return 'ClassActivitiesThemeCardVo(progressHeadColor: $progressHeadColor, progressTailColor: $progressTailColor, backgroundRes: $backgroundRes)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesThemeCardVo &&
            (identical(other.progressHeadColor, progressHeadColor) ||
                other.progressHeadColor == progressHeadColor) &&
            (identical(other.progressTailColor, progressTailColor) ||
                other.progressTailColor == progressTailColor) &&
            (identical(other.backgroundRes, backgroundRes) ||
                other.backgroundRes == backgroundRes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, progressHeadColor, progressTailColor, backgroundRes);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesThemeCardVoCopyWith<_$_ClassActivitiesThemeCardVo>
      get copyWith => __$$_ClassActivitiesThemeCardVoCopyWithImpl<
          _$_ClassActivitiesThemeCardVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesThemeCardVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesThemeCardVo
    implements ClassActivitiesThemeCardVo {
  const factory _ClassActivitiesThemeCardVo(
      {final String? progressHeadColor,
      final String? progressTailColor,
      final String? backgroundRes}) = _$_ClassActivitiesThemeCardVo;

  factory _ClassActivitiesThemeCardVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesThemeCardVo.fromJson;

  @override
  String? get progressHeadColor;
  @override
  String? get progressTailColor;
  @override
  String? get backgroundRes;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesThemeCardVoCopyWith<_$_ClassActivitiesThemeCardVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesTaskVo _$ClassActivitiesTaskVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesTaskVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskVo {
  int? get taskId => throw _privateConstructorUsedError;
  String? get taskType => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get finishTime => throw _privateConstructorUsedError;
  List<ClassActivitiesTaskConditionsVo?>? get conditions =>
      throw _privateConstructorUsedError;
  List<ClassActivitiesTaskRewardsVo?>? get rewards =>
      throw _privateConstructorUsedError;
  ClassActivitiesTaskExtendResourceVo? get taskExtendResource =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskVoCopyWith<ClassActivitiesTaskVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskVoCopyWith<$Res> {
  factory $ClassActivitiesTaskVoCopyWith(ClassActivitiesTaskVo value,
          $Res Function(ClassActivitiesTaskVo) then) =
      _$ClassActivitiesTaskVoCopyWithImpl<$Res, ClassActivitiesTaskVo>;
  @useResult
  $Res call(
      {int? taskId,
      String? taskType,
      String? name,
      int? finishTime,
      List<ClassActivitiesTaskConditionsVo?>? conditions,
      List<ClassActivitiesTaskRewardsVo?>? rewards,
      ClassActivitiesTaskExtendResourceVo? taskExtendResource});

  $ClassActivitiesTaskExtendResourceVoCopyWith<$Res>? get taskExtendResource;
}

/// @nodoc
class _$ClassActivitiesTaskVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskVo>
    implements $ClassActivitiesTaskVoCopyWith<$Res> {
  _$ClassActivitiesTaskVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? taskType = freezed,
    Object? name = freezed,
    Object? finishTime = freezed,
    Object? conditions = freezed,
    Object? rewards = freezed,
    Object? taskExtendResource = freezed,
  }) {
    return _then(_value.copyWith(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      finishTime: freezed == finishTime
          ? _value.finishTime
          : finishTime // ignore: cast_nullable_to_non_nullable
              as int?,
      conditions: freezed == conditions
          ? _value.conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskConditionsVo?>?,
      rewards: freezed == rewards
          ? _value.rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskRewardsVo?>?,
      taskExtendResource: freezed == taskExtendResource
          ? _value.taskExtendResource
          : taskExtendResource // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesTaskExtendResourceVo?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ClassActivitiesTaskExtendResourceVoCopyWith<$Res>? get taskExtendResource {
    if (_value.taskExtendResource == null) {
      return null;
    }

    return $ClassActivitiesTaskExtendResourceVoCopyWith<$Res>(
        _value.taskExtendResource!, (value) {
      return _then(_value.copyWith(taskExtendResource: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskVoCopyWith<$Res>
    implements $ClassActivitiesTaskVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskVoCopyWith(_$_ClassActivitiesTaskVo value,
          $Res Function(_$_ClassActivitiesTaskVo) then) =
      __$$_ClassActivitiesTaskVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? taskId,
      String? taskType,
      String? name,
      int? finishTime,
      List<ClassActivitiesTaskConditionsVo?>? conditions,
      List<ClassActivitiesTaskRewardsVo?>? rewards,
      ClassActivitiesTaskExtendResourceVo? taskExtendResource});

  @override
  $ClassActivitiesTaskExtendResourceVoCopyWith<$Res>? get taskExtendResource;
}

/// @nodoc
class __$$_ClassActivitiesTaskVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskVoCopyWithImpl<$Res, _$_ClassActivitiesTaskVo>
    implements _$$_ClassActivitiesTaskVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskVoCopyWithImpl(_$_ClassActivitiesTaskVo _value,
      $Res Function(_$_ClassActivitiesTaskVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? taskType = freezed,
    Object? name = freezed,
    Object? finishTime = freezed,
    Object? conditions = freezed,
    Object? rewards = freezed,
    Object? taskExtendResource = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskVo(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as int?,
      taskType: freezed == taskType
          ? _value.taskType
          : taskType // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      finishTime: freezed == finishTime
          ? _value.finishTime
          : finishTime // ignore: cast_nullable_to_non_nullable
              as int?,
      conditions: freezed == conditions
          ? _value._conditions
          : conditions // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskConditionsVo?>?,
      rewards: freezed == rewards
          ? _value._rewards
          : rewards // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskRewardsVo?>?,
      taskExtendResource: freezed == taskExtendResource
          ? _value.taskExtendResource
          : taskExtendResource // ignore: cast_nullable_to_non_nullable
              as ClassActivitiesTaskExtendResourceVo?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskVo implements _ClassActivitiesTaskVo {
  const _$_ClassActivitiesTaskVo(
      {this.taskId,
      this.taskType,
      this.name,
      this.finishTime,
      final List<ClassActivitiesTaskConditionsVo?>? conditions,
      final List<ClassActivitiesTaskRewardsVo?>? rewards,
      this.taskExtendResource})
      : _conditions = conditions,
        _rewards = rewards;

  factory _$_ClassActivitiesTaskVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskVoFromJson(json);

  @override
  final int? taskId;
  @override
  final String? taskType;
  @override
  final String? name;
  @override
  final int? finishTime;
  final List<ClassActivitiesTaskConditionsVo?>? _conditions;
  @override
  List<ClassActivitiesTaskConditionsVo?>? get conditions {
    final value = _conditions;
    if (value == null) return null;
    if (_conditions is EqualUnmodifiableListView) return _conditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ClassActivitiesTaskRewardsVo?>? _rewards;
  @override
  List<ClassActivitiesTaskRewardsVo?>? get rewards {
    final value = _rewards;
    if (value == null) return null;
    if (_rewards is EqualUnmodifiableListView) return _rewards;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ClassActivitiesTaskExtendResourceVo? taskExtendResource;

  @override
  String toString() {
    return 'ClassActivitiesTaskVo(taskId: $taskId, taskType: $taskType, name: $name, finishTime: $finishTime, conditions: $conditions, rewards: $rewards, taskExtendResource: $taskExtendResource)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskVo &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.taskType, taskType) ||
                other.taskType == taskType) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.finishTime, finishTime) ||
                other.finishTime == finishTime) &&
            const DeepCollectionEquality()
                .equals(other._conditions, _conditions) &&
            const DeepCollectionEquality().equals(other._rewards, _rewards) &&
            (identical(other.taskExtendResource, taskExtendResource) ||
                other.taskExtendResource == taskExtendResource));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      taskId,
      taskType,
      name,
      finishTime,
      const DeepCollectionEquality().hash(_conditions),
      const DeepCollectionEquality().hash(_rewards),
      taskExtendResource);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskVoCopyWith<_$_ClassActivitiesTaskVo> get copyWith =>
      __$$_ClassActivitiesTaskVoCopyWithImpl<_$_ClassActivitiesTaskVo>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskVo implements ClassActivitiesTaskVo {
  const factory _ClassActivitiesTaskVo(
          {final int? taskId,
          final String? taskType,
          final String? name,
          final int? finishTime,
          final List<ClassActivitiesTaskConditionsVo?>? conditions,
          final List<ClassActivitiesTaskRewardsVo?>? rewards,
          final ClassActivitiesTaskExtendResourceVo? taskExtendResource}) =
      _$_ClassActivitiesTaskVo;

  factory _ClassActivitiesTaskVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesTaskVo.fromJson;

  @override
  int? get taskId;
  @override
  String? get taskType;
  @override
  String? get name;
  @override
  int? get finishTime;
  @override
  List<ClassActivitiesTaskConditionsVo?>? get conditions;
  @override
  List<ClassActivitiesTaskRewardsVo?>? get rewards;
  @override
  ClassActivitiesTaskExtendResourceVo? get taskExtendResource;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskVoCopyWith<_$_ClassActivitiesTaskVo> get copyWith =>
      throw _privateConstructorUsedError;
}

ClassActivitiesTaskConditionsVo _$ClassActivitiesTaskConditionsVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesTaskConditionsVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskConditionsVo {
  int? get currentValue => throw _privateConstructorUsedError;
  int? get targetValue => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskConditionsVoCopyWith<ClassActivitiesTaskConditionsVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskConditionsVoCopyWith<$Res> {
  factory $ClassActivitiesTaskConditionsVoCopyWith(
          ClassActivitiesTaskConditionsVo value,
          $Res Function(ClassActivitiesTaskConditionsVo) then) =
      _$ClassActivitiesTaskConditionsVoCopyWithImpl<$Res,
          ClassActivitiesTaskConditionsVo>;
  @useResult
  $Res call({int? currentValue, int? targetValue});
}

/// @nodoc
class _$ClassActivitiesTaskConditionsVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskConditionsVo>
    implements $ClassActivitiesTaskConditionsVoCopyWith<$Res> {
  _$ClassActivitiesTaskConditionsVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentValue = freezed,
    Object? targetValue = freezed,
  }) {
    return _then(_value.copyWith(
      currentValue: freezed == currentValue
          ? _value.currentValue
          : currentValue // ignore: cast_nullable_to_non_nullable
              as int?,
      targetValue: freezed == targetValue
          ? _value.targetValue
          : targetValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskConditionsVoCopyWith<$Res>
    implements $ClassActivitiesTaskConditionsVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskConditionsVoCopyWith(
          _$_ClassActivitiesTaskConditionsVo value,
          $Res Function(_$_ClassActivitiesTaskConditionsVo) then) =
      __$$_ClassActivitiesTaskConditionsVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? currentValue, int? targetValue});
}

/// @nodoc
class __$$_ClassActivitiesTaskConditionsVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskConditionsVoCopyWithImpl<$Res,
        _$_ClassActivitiesTaskConditionsVo>
    implements _$$_ClassActivitiesTaskConditionsVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskConditionsVoCopyWithImpl(
      _$_ClassActivitiesTaskConditionsVo _value,
      $Res Function(_$_ClassActivitiesTaskConditionsVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentValue = freezed,
    Object? targetValue = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskConditionsVo(
      currentValue: freezed == currentValue
          ? _value.currentValue
          : currentValue // ignore: cast_nullable_to_non_nullable
              as int?,
      targetValue: freezed == targetValue
          ? _value.targetValue
          : targetValue // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskConditionsVo
    implements _ClassActivitiesTaskConditionsVo {
  const _$_ClassActivitiesTaskConditionsVo(
      {this.currentValue, this.targetValue});

  factory _$_ClassActivitiesTaskConditionsVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskConditionsVoFromJson(json);

  @override
  final int? currentValue;
  @override
  final int? targetValue;

  @override
  String toString() {
    return 'ClassActivitiesTaskConditionsVo(currentValue: $currentValue, targetValue: $targetValue)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskConditionsVo &&
            (identical(other.currentValue, currentValue) ||
                other.currentValue == currentValue) &&
            (identical(other.targetValue, targetValue) ||
                other.targetValue == targetValue));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, currentValue, targetValue);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskConditionsVoCopyWith<
          _$_ClassActivitiesTaskConditionsVo>
      get copyWith => __$$_ClassActivitiesTaskConditionsVoCopyWithImpl<
          _$_ClassActivitiesTaskConditionsVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskConditionsVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskConditionsVo
    implements ClassActivitiesTaskConditionsVo {
  const factory _ClassActivitiesTaskConditionsVo(
      {final int? currentValue,
      final int? targetValue}) = _$_ClassActivitiesTaskConditionsVo;

  factory _ClassActivitiesTaskConditionsVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesTaskConditionsVo.fromJson;

  @override
  int? get currentValue;
  @override
  int? get targetValue;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskConditionsVoCopyWith<
          _$_ClassActivitiesTaskConditionsVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesTaskRewardsVo _$ClassActivitiesTaskRewardsVoFromJson(
    Map<String, dynamic> json) {
  return _ClassActivitiesTaskRewardsVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskRewardsVo {
  int? get rewardId => throw _privateConstructorUsedError;
  int? get isGet => throw _privateConstructorUsedError;
  int? get isPopup => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  int? get getTime => throw _privateConstructorUsedError;
  String? get lockImage => throw _privateConstructorUsedError;
  String? get unlockImage => throw _privateConstructorUsedError;
  String? get resourceFlutter => throw _privateConstructorUsedError;
  String? get bizId => throw _privateConstructorUsedError;
  String? get rewardBizUrl => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskRewardsVoCopyWith<ClassActivitiesTaskRewardsVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskRewardsVoCopyWith<$Res> {
  factory $ClassActivitiesTaskRewardsVoCopyWith(
          ClassActivitiesTaskRewardsVo value,
          $Res Function(ClassActivitiesTaskRewardsVo) then) =
      _$ClassActivitiesTaskRewardsVoCopyWithImpl<$Res,
          ClassActivitiesTaskRewardsVo>;
  @useResult
  $Res call(
      {int? rewardId,
      int? isGet,
      int? isPopup,
      int? type,
      int? getTime,
      String? lockImage,
      String? unlockImage,
      String? resourceFlutter,
      String? bizId,
      String? rewardBizUrl});
}

/// @nodoc
class _$ClassActivitiesTaskRewardsVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskRewardsVo>
    implements $ClassActivitiesTaskRewardsVoCopyWith<$Res> {
  _$ClassActivitiesTaskRewardsVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardId = freezed,
    Object? isGet = freezed,
    Object? isPopup = freezed,
    Object? type = freezed,
    Object? getTime = freezed,
    Object? lockImage = freezed,
    Object? unlockImage = freezed,
    Object? resourceFlutter = freezed,
    Object? bizId = freezed,
    Object? rewardBizUrl = freezed,
  }) {
    return _then(_value.copyWith(
      rewardId: freezed == rewardId
          ? _value.rewardId
          : rewardId // ignore: cast_nullable_to_non_nullable
              as int?,
      isGet: freezed == isGet
          ? _value.isGet
          : isGet // ignore: cast_nullable_to_non_nullable
              as int?,
      isPopup: freezed == isPopup
          ? _value.isPopup
          : isPopup // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lockImage: freezed == lockImage
          ? _value.lockImage
          : lockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockImage: freezed == unlockImage
          ? _value.unlockImage
          : unlockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceFlutter: freezed == resourceFlutter
          ? _value.resourceFlutter
          : resourceFlutter // ignore: cast_nullable_to_non_nullable
              as String?,
      bizId: freezed == bizId
          ? _value.bizId
          : bizId // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardBizUrl: freezed == rewardBizUrl
          ? _value.rewardBizUrl
          : rewardBizUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskRewardsVoCopyWith<$Res>
    implements $ClassActivitiesTaskRewardsVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskRewardsVoCopyWith(
          _$_ClassActivitiesTaskRewardsVo value,
          $Res Function(_$_ClassActivitiesTaskRewardsVo) then) =
      __$$_ClassActivitiesTaskRewardsVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? rewardId,
      int? isGet,
      int? isPopup,
      int? type,
      int? getTime,
      String? lockImage,
      String? unlockImage,
      String? resourceFlutter,
      String? bizId,
      String? rewardBizUrl});
}

/// @nodoc
class __$$_ClassActivitiesTaskRewardsVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskRewardsVoCopyWithImpl<$Res,
        _$_ClassActivitiesTaskRewardsVo>
    implements _$$_ClassActivitiesTaskRewardsVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskRewardsVoCopyWithImpl(
      _$_ClassActivitiesTaskRewardsVo _value,
      $Res Function(_$_ClassActivitiesTaskRewardsVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardId = freezed,
    Object? isGet = freezed,
    Object? isPopup = freezed,
    Object? type = freezed,
    Object? getTime = freezed,
    Object? lockImage = freezed,
    Object? unlockImage = freezed,
    Object? resourceFlutter = freezed,
    Object? bizId = freezed,
    Object? rewardBizUrl = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskRewardsVo(
      rewardId: freezed == rewardId
          ? _value.rewardId
          : rewardId // ignore: cast_nullable_to_non_nullable
              as int?,
      isGet: freezed == isGet
          ? _value.isGet
          : isGet // ignore: cast_nullable_to_non_nullable
              as int?,
      isPopup: freezed == isPopup
          ? _value.isPopup
          : isPopup // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      getTime: freezed == getTime
          ? _value.getTime
          : getTime // ignore: cast_nullable_to_non_nullable
              as int?,
      lockImage: freezed == lockImage
          ? _value.lockImage
          : lockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      unlockImage: freezed == unlockImage
          ? _value.unlockImage
          : unlockImage // ignore: cast_nullable_to_non_nullable
              as String?,
      resourceFlutter: freezed == resourceFlutter
          ? _value.resourceFlutter
          : resourceFlutter // ignore: cast_nullable_to_non_nullable
              as String?,
      bizId: freezed == bizId
          ? _value.bizId
          : bizId // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardBizUrl: freezed == rewardBizUrl
          ? _value.rewardBizUrl
          : rewardBizUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskRewardsVo implements _ClassActivitiesTaskRewardsVo {
  const _$_ClassActivitiesTaskRewardsVo(
      {this.rewardId,
      this.isGet,
      this.isPopup,
      this.type,
      this.getTime,
      this.lockImage,
      this.unlockImage,
      this.resourceFlutter,
      this.bizId,
      this.rewardBizUrl});

  factory _$_ClassActivitiesTaskRewardsVo.fromJson(Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskRewardsVoFromJson(json);

  @override
  final int? rewardId;
  @override
  final int? isGet;
  @override
  final int? isPopup;
  @override
  final int? type;
  @override
  final int? getTime;
  @override
  final String? lockImage;
  @override
  final String? unlockImage;
  @override
  final String? resourceFlutter;
  @override
  final String? bizId;
  @override
  final String? rewardBizUrl;

  @override
  String toString() {
    return 'ClassActivitiesTaskRewardsVo(rewardId: $rewardId, isGet: $isGet, isPopup: $isPopup, type: $type, getTime: $getTime, lockImage: $lockImage, unlockImage: $unlockImage, resourceFlutter: $resourceFlutter, bizId: $bizId, rewardBizUrl: $rewardBizUrl)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskRewardsVo &&
            (identical(other.rewardId, rewardId) ||
                other.rewardId == rewardId) &&
            (identical(other.isGet, isGet) || other.isGet == isGet) &&
            (identical(other.isPopup, isPopup) || other.isPopup == isPopup) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.getTime, getTime) || other.getTime == getTime) &&
            (identical(other.lockImage, lockImage) ||
                other.lockImage == lockImage) &&
            (identical(other.unlockImage, unlockImage) ||
                other.unlockImage == unlockImage) &&
            (identical(other.resourceFlutter, resourceFlutter) ||
                other.resourceFlutter == resourceFlutter) &&
            (identical(other.bizId, bizId) || other.bizId == bizId) &&
            (identical(other.rewardBizUrl, rewardBizUrl) ||
                other.rewardBizUrl == rewardBizUrl));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, rewardId, isGet, isPopup, type,
      getTime, lockImage, unlockImage, resourceFlutter, bizId, rewardBizUrl);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskRewardsVoCopyWith<_$_ClassActivitiesTaskRewardsVo>
      get copyWith => __$$_ClassActivitiesTaskRewardsVoCopyWithImpl<
          _$_ClassActivitiesTaskRewardsVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskRewardsVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskRewardsVo
    implements ClassActivitiesTaskRewardsVo {
  const factory _ClassActivitiesTaskRewardsVo(
      {final int? rewardId,
      final int? isGet,
      final int? isPopup,
      final int? type,
      final int? getTime,
      final String? lockImage,
      final String? unlockImage,
      final String? resourceFlutter,
      final String? bizId,
      final String? rewardBizUrl}) = _$_ClassActivitiesTaskRewardsVo;

  factory _ClassActivitiesTaskRewardsVo.fromJson(Map<String, dynamic> json) =
      _$_ClassActivitiesTaskRewardsVo.fromJson;

  @override
  int? get rewardId;
  @override
  int? get isGet;
  @override
  int? get isPopup;
  @override
  int? get type;
  @override
  int? get getTime;
  @override
  String? get lockImage;
  @override
  String? get unlockImage;
  @override
  String? get resourceFlutter;
  @override
  String? get bizId;
  @override
  String? get rewardBizUrl;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskRewardsVoCopyWith<_$_ClassActivitiesTaskRewardsVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesTaskExtendResourceVo
    _$ClassActivitiesTaskExtendResourceVoFromJson(Map<String, dynamic> json) {
  return _ClassActivitiesTaskExtendResourceVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskExtendResourceVo {
  String? get rewardDisplayUrl => throw _privateConstructorUsedError;
  String? get mainText => throw _privateConstructorUsedError;
  String? get subText => throw _privateConstructorUsedError;
  List<ClassActivitiesTaskRewardNodeTextVo?>? get rewardNodeTexts =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskExtendResourceVoCopyWith<
          ClassActivitiesTaskExtendResourceVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskExtendResourceVoCopyWith<$Res> {
  factory $ClassActivitiesTaskExtendResourceVoCopyWith(
          ClassActivitiesTaskExtendResourceVo value,
          $Res Function(ClassActivitiesTaskExtendResourceVo) then) =
      _$ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res,
          ClassActivitiesTaskExtendResourceVo>;
  @useResult
  $Res call(
      {String? rewardDisplayUrl,
      String? mainText,
      String? subText,
      List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts});
}

/// @nodoc
class _$ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskExtendResourceVo>
    implements $ClassActivitiesTaskExtendResourceVoCopyWith<$Res> {
  _$ClassActivitiesTaskExtendResourceVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardDisplayUrl = freezed,
    Object? mainText = freezed,
    Object? subText = freezed,
    Object? rewardNodeTexts = freezed,
  }) {
    return _then(_value.copyWith(
      rewardDisplayUrl: freezed == rewardDisplayUrl
          ? _value.rewardDisplayUrl
          : rewardDisplayUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      subText: freezed == subText
          ? _value.subText
          : subText // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardNodeTexts: freezed == rewardNodeTexts
          ? _value.rewardNodeTexts
          : rewardNodeTexts // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskRewardNodeTextVo?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskExtendResourceVoCopyWith<$Res>
    implements $ClassActivitiesTaskExtendResourceVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskExtendResourceVoCopyWith(
          _$_ClassActivitiesTaskExtendResourceVo value,
          $Res Function(_$_ClassActivitiesTaskExtendResourceVo) then) =
      __$$_ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? rewardDisplayUrl,
      String? mainText,
      String? subText,
      List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts});
}

/// @nodoc
class __$$_ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskExtendResourceVoCopyWithImpl<$Res,
        _$_ClassActivitiesTaskExtendResourceVo>
    implements _$$_ClassActivitiesTaskExtendResourceVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskExtendResourceVoCopyWithImpl(
      _$_ClassActivitiesTaskExtendResourceVo _value,
      $Res Function(_$_ClassActivitiesTaskExtendResourceVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardDisplayUrl = freezed,
    Object? mainText = freezed,
    Object? subText = freezed,
    Object? rewardNodeTexts = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskExtendResourceVo(
      rewardDisplayUrl: freezed == rewardDisplayUrl
          ? _value.rewardDisplayUrl
          : rewardDisplayUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      subText: freezed == subText
          ? _value.subText
          : subText // ignore: cast_nullable_to_non_nullable
              as String?,
      rewardNodeTexts: freezed == rewardNodeTexts
          ? _value._rewardNodeTexts
          : rewardNodeTexts // ignore: cast_nullable_to_non_nullable
              as List<ClassActivitiesTaskRewardNodeTextVo?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskExtendResourceVo
    implements _ClassActivitiesTaskExtendResourceVo {
  const _$_ClassActivitiesTaskExtendResourceVo(
      {this.rewardDisplayUrl,
      this.mainText,
      this.subText,
      final List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts})
      : _rewardNodeTexts = rewardNodeTexts;

  factory _$_ClassActivitiesTaskExtendResourceVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskExtendResourceVoFromJson(json);

  @override
  final String? rewardDisplayUrl;
  @override
  final String? mainText;
  @override
  final String? subText;
  final List<ClassActivitiesTaskRewardNodeTextVo?>? _rewardNodeTexts;
  @override
  List<ClassActivitiesTaskRewardNodeTextVo?>? get rewardNodeTexts {
    final value = _rewardNodeTexts;
    if (value == null) return null;
    if (_rewardNodeTexts is EqualUnmodifiableListView) return _rewardNodeTexts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ClassActivitiesTaskExtendResourceVo(rewardDisplayUrl: $rewardDisplayUrl, mainText: $mainText, subText: $subText, rewardNodeTexts: $rewardNodeTexts)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskExtendResourceVo &&
            (identical(other.rewardDisplayUrl, rewardDisplayUrl) ||
                other.rewardDisplayUrl == rewardDisplayUrl) &&
            (identical(other.mainText, mainText) ||
                other.mainText == mainText) &&
            (identical(other.subText, subText) || other.subText == subText) &&
            const DeepCollectionEquality()
                .equals(other._rewardNodeTexts, _rewardNodeTexts));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, rewardDisplayUrl, mainText,
      subText, const DeepCollectionEquality().hash(_rewardNodeTexts));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskExtendResourceVoCopyWith<
          _$_ClassActivitiesTaskExtendResourceVo>
      get copyWith => __$$_ClassActivitiesTaskExtendResourceVoCopyWithImpl<
          _$_ClassActivitiesTaskExtendResourceVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskExtendResourceVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskExtendResourceVo
    implements ClassActivitiesTaskExtendResourceVo {
  const factory _ClassActivitiesTaskExtendResourceVo(
          {final String? rewardDisplayUrl,
          final String? mainText,
          final String? subText,
          final List<ClassActivitiesTaskRewardNodeTextVo?>? rewardNodeTexts}) =
      _$_ClassActivitiesTaskExtendResourceVo;

  factory _ClassActivitiesTaskExtendResourceVo.fromJson(
          Map<String, dynamic> json) =
      _$_ClassActivitiesTaskExtendResourceVo.fromJson;

  @override
  String? get rewardDisplayUrl;
  @override
  String? get mainText;
  @override
  String? get subText;
  @override
  List<ClassActivitiesTaskRewardNodeTextVo?>? get rewardNodeTexts;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskExtendResourceVoCopyWith<
          _$_ClassActivitiesTaskExtendResourceVo>
      get copyWith => throw _privateConstructorUsedError;
}

ClassActivitiesTaskRewardNodeTextVo
    _$ClassActivitiesTaskRewardNodeTextVoFromJson(Map<String, dynamic> json) {
  return _ClassActivitiesTaskRewardNodeTextVo.fromJson(json);
}

/// @nodoc
mixin _$ClassActivitiesTaskRewardNodeTextVo {
  int? get rewardType => throw _privateConstructorUsedError;
  String? get mainText => throw _privateConstructorUsedError;
  String? get subText => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ClassActivitiesTaskRewardNodeTextVoCopyWith<
          ClassActivitiesTaskRewardNodeTextVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res> {
  factory $ClassActivitiesTaskRewardNodeTextVoCopyWith(
          ClassActivitiesTaskRewardNodeTextVo value,
          $Res Function(ClassActivitiesTaskRewardNodeTextVo) then) =
      _$ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res,
          ClassActivitiesTaskRewardNodeTextVo>;
  @useResult
  $Res call({int? rewardType, String? mainText, String? subText});
}

/// @nodoc
class _$ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res,
        $Val extends ClassActivitiesTaskRewardNodeTextVo>
    implements $ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res> {
  _$ClassActivitiesTaskRewardNodeTextVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardType = freezed,
    Object? mainText = freezed,
    Object? subText = freezed,
  }) {
    return _then(_value.copyWith(
      rewardType: freezed == rewardType
          ? _value.rewardType
          : rewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      subText: freezed == subText
          ? _value.subText
          : subText // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res>
    implements $ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res> {
  factory _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith(
          _$_ClassActivitiesTaskRewardNodeTextVo value,
          $Res Function(_$_ClassActivitiesTaskRewardNodeTextVo) then) =
      __$$_ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? rewardType, String? mainText, String? subText});
}

/// @nodoc
class __$$_ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res>
    extends _$ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<$Res,
        _$_ClassActivitiesTaskRewardNodeTextVo>
    implements _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith<$Res> {
  __$$_ClassActivitiesTaskRewardNodeTextVoCopyWithImpl(
      _$_ClassActivitiesTaskRewardNodeTextVo _value,
      $Res Function(_$_ClassActivitiesTaskRewardNodeTextVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rewardType = freezed,
    Object? mainText = freezed,
    Object? subText = freezed,
  }) {
    return _then(_$_ClassActivitiesTaskRewardNodeTextVo(
      rewardType: freezed == rewardType
          ? _value.rewardType
          : rewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      mainText: freezed == mainText
          ? _value.mainText
          : mainText // ignore: cast_nullable_to_non_nullable
              as String?,
      subText: freezed == subText
          ? _value.subText
          : subText // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_ClassActivitiesTaskRewardNodeTextVo
    implements _ClassActivitiesTaskRewardNodeTextVo {
  const _$_ClassActivitiesTaskRewardNodeTextVo(
      {this.rewardType, this.mainText, this.subText});

  factory _$_ClassActivitiesTaskRewardNodeTextVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_ClassActivitiesTaskRewardNodeTextVoFromJson(json);

  @override
  final int? rewardType;
  @override
  final String? mainText;
  @override
  final String? subText;

  @override
  String toString() {
    return 'ClassActivitiesTaskRewardNodeTextVo(rewardType: $rewardType, mainText: $mainText, subText: $subText)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_ClassActivitiesTaskRewardNodeTextVo &&
            (identical(other.rewardType, rewardType) ||
                other.rewardType == rewardType) &&
            (identical(other.mainText, mainText) ||
                other.mainText == mainText) &&
            (identical(other.subText, subText) || other.subText == subText));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, rewardType, mainText, subText);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith<
          _$_ClassActivitiesTaskRewardNodeTextVo>
      get copyWith => __$$_ClassActivitiesTaskRewardNodeTextVoCopyWithImpl<
          _$_ClassActivitiesTaskRewardNodeTextVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_ClassActivitiesTaskRewardNodeTextVoToJson(
      this,
    );
  }
}

abstract class _ClassActivitiesTaskRewardNodeTextVo
    implements ClassActivitiesTaskRewardNodeTextVo {
  const factory _ClassActivitiesTaskRewardNodeTextVo(
      {final int? rewardType,
      final String? mainText,
      final String? subText}) = _$_ClassActivitiesTaskRewardNodeTextVo;

  factory _ClassActivitiesTaskRewardNodeTextVo.fromJson(
          Map<String, dynamic> json) =
      _$_ClassActivitiesTaskRewardNodeTextVo.fromJson;

  @override
  int? get rewardType;
  @override
  String? get mainText;
  @override
  String? get subText;
  @override
  @JsonKey(ignore: true)
  _$$_ClassActivitiesTaskRewardNodeTextVoCopyWith<
          _$_ClassActivitiesTaskRewardNodeTextVo>
      get copyWith => throw _privateConstructorUsedError;
}

PlanActivityFreePagesVo _$PlanActivityFreePagesVoFromJson(
    Map<String, dynamic> json) {
  return _PlanActivityFreePagesVo.fromJson(json);
}

/// @nodoc
mixin _$PlanActivityFreePagesVo {
  int? get id => throw _privateConstructorUsedError;
  String? get pageType => throw _privateConstructorUsedError;
  String? get pageName => throw _privateConstructorUsedError;
  List<PlanActivityFreePagesComponentsVo?>? get components =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanActivityFreePagesVoCopyWith<PlanActivityFreePagesVo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanActivityFreePagesVoCopyWith<$Res> {
  factory $PlanActivityFreePagesVoCopyWith(PlanActivityFreePagesVo value,
          $Res Function(PlanActivityFreePagesVo) then) =
      _$PlanActivityFreePagesVoCopyWithImpl<$Res, PlanActivityFreePagesVo>;
  @useResult
  $Res call(
      {int? id,
      String? pageType,
      String? pageName,
      List<PlanActivityFreePagesComponentsVo?>? components});
}

/// @nodoc
class _$PlanActivityFreePagesVoCopyWithImpl<$Res,
        $Val extends PlanActivityFreePagesVo>
    implements $PlanActivityFreePagesVoCopyWith<$Res> {
  _$PlanActivityFreePagesVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? pageType = freezed,
    Object? pageName = freezed,
    Object? components = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      pageType: freezed == pageType
          ? _value.pageType
          : pageType // ignore: cast_nullable_to_non_nullable
              as String?,
      pageName: freezed == pageName
          ? _value.pageName
          : pageName // ignore: cast_nullable_to_non_nullable
              as String?,
      components: freezed == components
          ? _value.components
          : components // ignore: cast_nullable_to_non_nullable
              as List<PlanActivityFreePagesComponentsVo?>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PlanActivityFreePagesVoCopyWith<$Res>
    implements $PlanActivityFreePagesVoCopyWith<$Res> {
  factory _$$_PlanActivityFreePagesVoCopyWith(_$_PlanActivityFreePagesVo value,
          $Res Function(_$_PlanActivityFreePagesVo) then) =
      __$$_PlanActivityFreePagesVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? pageType,
      String? pageName,
      List<PlanActivityFreePagesComponentsVo?>? components});
}

/// @nodoc
class __$$_PlanActivityFreePagesVoCopyWithImpl<$Res>
    extends _$PlanActivityFreePagesVoCopyWithImpl<$Res,
        _$_PlanActivityFreePagesVo>
    implements _$$_PlanActivityFreePagesVoCopyWith<$Res> {
  __$$_PlanActivityFreePagesVoCopyWithImpl(_$_PlanActivityFreePagesVo _value,
      $Res Function(_$_PlanActivityFreePagesVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? pageType = freezed,
    Object? pageName = freezed,
    Object? components = freezed,
  }) {
    return _then(_$_PlanActivityFreePagesVo(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      pageType: freezed == pageType
          ? _value.pageType
          : pageType // ignore: cast_nullable_to_non_nullable
              as String?,
      pageName: freezed == pageName
          ? _value.pageName
          : pageName // ignore: cast_nullable_to_non_nullable
              as String?,
      components: freezed == components
          ? _value._components
          : components // ignore: cast_nullable_to_non_nullable
              as List<PlanActivityFreePagesComponentsVo?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanActivityFreePagesVo implements _PlanActivityFreePagesVo {
  const _$_PlanActivityFreePagesVo(
      {this.id,
      this.pageType,
      this.pageName,
      final List<PlanActivityFreePagesComponentsVo?>? components})
      : _components = components;

  factory _$_PlanActivityFreePagesVo.fromJson(Map<String, dynamic> json) =>
      _$$_PlanActivityFreePagesVoFromJson(json);

  @override
  final int? id;
  @override
  final String? pageType;
  @override
  final String? pageName;
  final List<PlanActivityFreePagesComponentsVo?>? _components;
  @override
  List<PlanActivityFreePagesComponentsVo?>? get components {
    final value = _components;
    if (value == null) return null;
    if (_components is EqualUnmodifiableListView) return _components;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'PlanActivityFreePagesVo(id: $id, pageType: $pageType, pageName: $pageName, components: $components)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanActivityFreePagesVo &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.pageType, pageType) ||
                other.pageType == pageType) &&
            (identical(other.pageName, pageName) ||
                other.pageName == pageName) &&
            const DeepCollectionEquality()
                .equals(other._components, _components));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, pageType, pageName,
      const DeepCollectionEquality().hash(_components));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanActivityFreePagesVoCopyWith<_$_PlanActivityFreePagesVo>
      get copyWith =>
          __$$_PlanActivityFreePagesVoCopyWithImpl<_$_PlanActivityFreePagesVo>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanActivityFreePagesVoToJson(
      this,
    );
  }
}

abstract class _PlanActivityFreePagesVo implements PlanActivityFreePagesVo {
  const factory _PlanActivityFreePagesVo(
          {final int? id,
          final String? pageType,
          final String? pageName,
          final List<PlanActivityFreePagesComponentsVo?>? components}) =
      _$_PlanActivityFreePagesVo;

  factory _PlanActivityFreePagesVo.fromJson(Map<String, dynamic> json) =
      _$_PlanActivityFreePagesVo.fromJson;

  @override
  int? get id;
  @override
  String? get pageType;
  @override
  String? get pageName;
  @override
  List<PlanActivityFreePagesComponentsVo?>? get components;
  @override
  @JsonKey(ignore: true)
  _$$_PlanActivityFreePagesVoCopyWith<_$_PlanActivityFreePagesVo>
      get copyWith => throw _privateConstructorUsedError;
}

PlanActivityFreePagesComponentsVo _$PlanActivityFreePagesComponentsVoFromJson(
    Map<String, dynamic> json) {
  return _PlanActivityFreePagesComponentsVo.fromJson(json);
}

/// @nodoc
mixin _$PlanActivityFreePagesComponentsVo {
  int? get orderNum => throw _privateConstructorUsedError;
  String? get componentType => throw _privateConstructorUsedError;
  String? get imgUrl => throw _privateConstructorUsedError;
  String? get videoUrl => throw _privateConstructorUsedError;
  String? get videoCoverImg => throw _privateConstructorUsedError;
  String? get videoBgImg => throw _privateConstructorUsedError;
  String? get topImg => throw _privateConstructorUsedError;
  String? get surroundImg => throw _privateConstructorUsedError;
  String? get bottomImg => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PlanActivityFreePagesComponentsVoCopyWith<PlanActivityFreePagesComponentsVo>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanActivityFreePagesComponentsVoCopyWith<$Res> {
  factory $PlanActivityFreePagesComponentsVoCopyWith(
          PlanActivityFreePagesComponentsVo value,
          $Res Function(PlanActivityFreePagesComponentsVo) then) =
      _$PlanActivityFreePagesComponentsVoCopyWithImpl<$Res,
          PlanActivityFreePagesComponentsVo>;
  @useResult
  $Res call(
      {int? orderNum,
      String? componentType,
      String? imgUrl,
      String? videoUrl,
      String? videoCoverImg,
      String? videoBgImg,
      String? topImg,
      String? surroundImg,
      String? bottomImg});
}

/// @nodoc
class _$PlanActivityFreePagesComponentsVoCopyWithImpl<$Res,
        $Val extends PlanActivityFreePagesComponentsVo>
    implements $PlanActivityFreePagesComponentsVoCopyWith<$Res> {
  _$PlanActivityFreePagesComponentsVoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderNum = freezed,
    Object? componentType = freezed,
    Object? imgUrl = freezed,
    Object? videoUrl = freezed,
    Object? videoCoverImg = freezed,
    Object? videoBgImg = freezed,
    Object? topImg = freezed,
    Object? surroundImg = freezed,
    Object? bottomImg = freezed,
  }) {
    return _then(_value.copyWith(
      orderNum: freezed == orderNum
          ? _value.orderNum
          : orderNum // ignore: cast_nullable_to_non_nullable
              as int?,
      componentType: freezed == componentType
          ? _value.componentType
          : componentType // ignore: cast_nullable_to_non_nullable
              as String?,
      imgUrl: freezed == imgUrl
          ? _value.imgUrl
          : imgUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoCoverImg: freezed == videoCoverImg
          ? _value.videoCoverImg
          : videoCoverImg // ignore: cast_nullable_to_non_nullable
              as String?,
      videoBgImg: freezed == videoBgImg
          ? _value.videoBgImg
          : videoBgImg // ignore: cast_nullable_to_non_nullable
              as String?,
      topImg: freezed == topImg
          ? _value.topImg
          : topImg // ignore: cast_nullable_to_non_nullable
              as String?,
      surroundImg: freezed == surroundImg
          ? _value.surroundImg
          : surroundImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bottomImg: freezed == bottomImg
          ? _value.bottomImg
          : bottomImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_PlanActivityFreePagesComponentsVoCopyWith<$Res>
    implements $PlanActivityFreePagesComponentsVoCopyWith<$Res> {
  factory _$$_PlanActivityFreePagesComponentsVoCopyWith(
          _$_PlanActivityFreePagesComponentsVo value,
          $Res Function(_$_PlanActivityFreePagesComponentsVo) then) =
      __$$_PlanActivityFreePagesComponentsVoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? orderNum,
      String? componentType,
      String? imgUrl,
      String? videoUrl,
      String? videoCoverImg,
      String? videoBgImg,
      String? topImg,
      String? surroundImg,
      String? bottomImg});
}

/// @nodoc
class __$$_PlanActivityFreePagesComponentsVoCopyWithImpl<$Res>
    extends _$PlanActivityFreePagesComponentsVoCopyWithImpl<$Res,
        _$_PlanActivityFreePagesComponentsVo>
    implements _$$_PlanActivityFreePagesComponentsVoCopyWith<$Res> {
  __$$_PlanActivityFreePagesComponentsVoCopyWithImpl(
      _$_PlanActivityFreePagesComponentsVo _value,
      $Res Function(_$_PlanActivityFreePagesComponentsVo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderNum = freezed,
    Object? componentType = freezed,
    Object? imgUrl = freezed,
    Object? videoUrl = freezed,
    Object? videoCoverImg = freezed,
    Object? videoBgImg = freezed,
    Object? topImg = freezed,
    Object? surroundImg = freezed,
    Object? bottomImg = freezed,
  }) {
    return _then(_$_PlanActivityFreePagesComponentsVo(
      orderNum: freezed == orderNum
          ? _value.orderNum
          : orderNum // ignore: cast_nullable_to_non_nullable
              as int?,
      componentType: freezed == componentType
          ? _value.componentType
          : componentType // ignore: cast_nullable_to_non_nullable
              as String?,
      imgUrl: freezed == imgUrl
          ? _value.imgUrl
          : imgUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoUrl: freezed == videoUrl
          ? _value.videoUrl
          : videoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      videoCoverImg: freezed == videoCoverImg
          ? _value.videoCoverImg
          : videoCoverImg // ignore: cast_nullable_to_non_nullable
              as String?,
      videoBgImg: freezed == videoBgImg
          ? _value.videoBgImg
          : videoBgImg // ignore: cast_nullable_to_non_nullable
              as String?,
      topImg: freezed == topImg
          ? _value.topImg
          : topImg // ignore: cast_nullable_to_non_nullable
              as String?,
      surroundImg: freezed == surroundImg
          ? _value.surroundImg
          : surroundImg // ignore: cast_nullable_to_non_nullable
              as String?,
      bottomImg: freezed == bottomImg
          ? _value.bottomImg
          : bottomImg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_PlanActivityFreePagesComponentsVo
    implements _PlanActivityFreePagesComponentsVo {
  const _$_PlanActivityFreePagesComponentsVo(
      {this.orderNum,
      this.componentType,
      this.imgUrl,
      this.videoUrl,
      this.videoCoverImg,
      this.videoBgImg,
      this.topImg,
      this.surroundImg,
      this.bottomImg});

  factory _$_PlanActivityFreePagesComponentsVo.fromJson(
          Map<String, dynamic> json) =>
      _$$_PlanActivityFreePagesComponentsVoFromJson(json);

  @override
  final int? orderNum;
  @override
  final String? componentType;
  @override
  final String? imgUrl;
  @override
  final String? videoUrl;
  @override
  final String? videoCoverImg;
  @override
  final String? videoBgImg;
  @override
  final String? topImg;
  @override
  final String? surroundImg;
  @override
  final String? bottomImg;

  @override
  String toString() {
    return 'PlanActivityFreePagesComponentsVo(orderNum: $orderNum, componentType: $componentType, imgUrl: $imgUrl, videoUrl: $videoUrl, videoCoverImg: $videoCoverImg, videoBgImg: $videoBgImg, topImg: $topImg, surroundImg: $surroundImg, bottomImg: $bottomImg)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_PlanActivityFreePagesComponentsVo &&
            (identical(other.orderNum, orderNum) ||
                other.orderNum == orderNum) &&
            (identical(other.componentType, componentType) ||
                other.componentType == componentType) &&
            (identical(other.imgUrl, imgUrl) || other.imgUrl == imgUrl) &&
            (identical(other.videoUrl, videoUrl) ||
                other.videoUrl == videoUrl) &&
            (identical(other.videoCoverImg, videoCoverImg) ||
                other.videoCoverImg == videoCoverImg) &&
            (identical(other.videoBgImg, videoBgImg) ||
                other.videoBgImg == videoBgImg) &&
            (identical(other.topImg, topImg) || other.topImg == topImg) &&
            (identical(other.surroundImg, surroundImg) ||
                other.surroundImg == surroundImg) &&
            (identical(other.bottomImg, bottomImg) ||
                other.bottomImg == bottomImg));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, orderNum, componentType, imgUrl,
      videoUrl, videoCoverImg, videoBgImg, topImg, surroundImg, bottomImg);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_PlanActivityFreePagesComponentsVoCopyWith<
          _$_PlanActivityFreePagesComponentsVo>
      get copyWith => __$$_PlanActivityFreePagesComponentsVoCopyWithImpl<
          _$_PlanActivityFreePagesComponentsVo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_PlanActivityFreePagesComponentsVoToJson(
      this,
    );
  }
}

abstract class _PlanActivityFreePagesComponentsVo
    implements PlanActivityFreePagesComponentsVo {
  const factory _PlanActivityFreePagesComponentsVo(
      {final int? orderNum,
      final String? componentType,
      final String? imgUrl,
      final String? videoUrl,
      final String? videoCoverImg,
      final String? videoBgImg,
      final String? topImg,
      final String? surroundImg,
      final String? bottomImg}) = _$_PlanActivityFreePagesComponentsVo;

  factory _PlanActivityFreePagesComponentsVo.fromJson(
          Map<String, dynamic> json) =
      _$_PlanActivityFreePagesComponentsVo.fromJson;

  @override
  int? get orderNum;
  @override
  String? get componentType;
  @override
  String? get imgUrl;
  @override
  String? get videoUrl;
  @override
  String? get videoCoverImg;
  @override
  String? get videoBgImg;
  @override
  String? get topImg;
  @override
  String? get surroundImg;
  @override
  String? get bottomImg;
  @override
  @JsonKey(ignore: true)
  _$$_PlanActivityFreePagesComponentsVoCopyWith<
          _$_PlanActivityFreePagesComponentsVo>
      get copyWith => throw _privateConstructorUsedError;
}
