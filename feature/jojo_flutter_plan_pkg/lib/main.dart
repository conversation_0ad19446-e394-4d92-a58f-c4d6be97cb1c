import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/config.dart';
import 'package:jojo_flutter_base/jojo_flutter_run.dart';
import 'package:jojo_flutter_base/widgets/popup/popup.dart';
import 'package:jojo_flutter_plan_pkg/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/widget/preload/preload_gray_manager.dart';
import 'package:jojo_flutter_plan_pkg/pages/guide/guide_helper_local_mocker.dart';
import 'package:jojo_flutter_plan_pkg/src/tangram/generated.tangram.dart';
import 'package:tangram_common_jojo/common_tangram.dart';

import 'common/config/env_web.dart';
import 'common/host_env/host_env.dart';
import 'module.dart';

void main() {
  jojo_flutter_plan_pkg module = jojo_flutter_plan_pkg();
  JoJoModuleManager.addModules([module]);

  jojoFlutterRun(
      () async {
        // const imageUrl = "https%3A%2F%2Fjojopublicfat.jojoread.com%2Fcms%2Fjaguar-admin%2Ffront-resource%2F809792304404393985.png";
        const imageUrl = "https%3A%2F%2Fjojopublicfat.jojoread.com%2Fcms%2Fjaguar-admin%2Ffront-resource%2F807262853443518465.png";
        JoJoRouter.init(
            // initialLocation: AppPage.mineHonorPage.path,
            // initialLocation: AppPage.msgBoxPage.path,
            // initialLocation:
            //     '${AppPage.commendationListPage.path}?classId=27041&courseId=4961',
            // initialLocation:
            //     '${AppPage.commentListPage.path}?classKey=25852_39&courseKey=25852&commentType=2&userId=',
            // initialLocation:
            //     '${AppPage.teacherServicePage.path}?classId=23472&courseKey=27287',
            // initialLocation: '${AppPage.courseTask.path}?classId=27264&courseId=6960',
            // '${AppPage.recognition.path}?citeId=308&teacherId=1&classId=21048&fullScreen=true',
            // initialLocation:
            //     '${AppPage.recognition.path}?citeId=308&teacherId=1&classId=21048&fullScreen=true',
            // initialLocation:
            //     '${AppPage.commendationListPage.path}?classId=22444&&courseKey=24086&windowType=window&nativeStackIndex=1&barHeight=100',
            // initialLocation: '${AppPage.customerCenter.path}?jojoSceneId=2',
            // initialLocation:
            //     '${AppPage.teacherServicePage.path}?courseKey=27287&classId=23472&bgColor=F2FBFF',
            // initialLocation:
            //    '${AppPage.pdfPreviewPage.path}?courseId=10,16,17,22,1212,23,34',
            // initialLocation:'${AppPage.adjustSchedule.path}?pageType=${PageType.pageNative}',
            // initialLocation:'${AppPage.adjustSchedule.path}?pageType=${PageType.pageNative}',
            // initialLocation:
            // '${AppPage.questionBankList.path}?courseKey=27461&classId=34483',
            // initialLocation:
            // '${AppPage.parentsClass.path}?classId=14070&category=3',
            // initialLocation:
            //     '${AppPage.planFullScreenVideoPage.path}?resouceOrientation=portrait&neadRePlay=0&videoUrl=https%3A%2F%2Fjojopublicfat.jojoread.com%2Fcc%2Fcc-admin%2Fcourse%2F246231511678514176%2Fafbb17e408ad30b71bbc6aa656c9b7261615346185003.mp4',
            // initialLocation:
            //     '${AppPage.planFullScreenVideoPage.path}?neadRePlay=0&videoUrl=https%3A%2F%2Fjojopublicfat.jojoread.com%2Fcms%2Fjaguar-admin%2Ffront-resource%2F436170672806800384.mp4%3FossUrl%3Dtinman-oss%253A%252F%252Ffront-jaguar-resource%252F436170672806800384.mp4',
            // initialLocation: "${AppPage.parentToolsPage.path}?classId=48697&taskType=15&showDateTime=*************&schedule=0",
            // initialLocation: '${AppPage.myAchievementsPage.path}?classKey=21071&subjectType=2',
            // initialLocation: '${AppPage.achievementDetailPage.path}?classKey=21071&medalId=8',
            // initialLocation: AppPage.planHomeMapPage.path,
            // initialLocation: '${AppPage.parentsClass.path}?classId=45972&category=3',
            // initialLocation: AppPage.mathScheduleListPage.path,
            // initialLocation: AppPage.courseFinishSettleAccountsPage.path+'?subjectColor=AE84E3&classKey=51088_496&lessonKey=35110_0002',
            // initialLocation: AppPage.trainAddTeacherPage.path+'?classId=classId&serviceKey=serviceKey&addBtnText=addBtnText&closeBtnText=dgsdfg&addRoute=addRoute&closeRoute=closeRoute&imageUrl=$imageUrl&backgroundColor=AE84E3&materialType=materialType&courseStage=courseStage&customState=customState&courseKey=courseKey&materialId=materialId',
            initialLocation: AppPage.partnerMessagePage.path,
            // initialLocation: '${AppPage.joJoCourseSessionList.path}?deviceOrientation=landscape&classKey=51088_498&lessonKey=42367_0018&engineType=0',
            observers: [JoJoPopup.observer, RDPHelper.observer],
            routes: module.routes);

        if (!RunEnv.isWeb) {
          JoJoNativeBridge.registerMocker(GuideHelperLocalMocker());
        }
        await BaseConfig.share.init(RunEnv.isWeb ? EnvWeb.ENV_NAME : null);
      },
      packageName: 'jojo_flutter_plan_pkg',
      debugMode: RunEnv.isTestEnv,
      onAppReady: () async {
        TangramManager.registerFunctionMap(plan_pkg_dynamicMap());
        TangramManager.setup(
          showTangramBanner: RunEnv.isTestEnv,
          forceUseAssets: true,
          forceUseRemote: false,
          assetsPath: 'assets/tangram/tangram_resource.zip',
        );
        await PreloadGrayManager.initConfig();
      });
}

// Copyright 2019 Aleksander Woźniak
// SPDX-License-Identifier: Apache-2.0

// import 'package:flutter/material.dart';
// import 'package:intl/date_symbol_data_local.dart';

// import 'package:jojo_flutter_plan_pkg/pages/calender/pages/basics_example.dart';
// import 'package:jojo_flutter_plan_pkg/pages/calender/pages/complex_example.dart';
// import 'package:jojo_flutter_plan_pkg/pages/calender/pages/events_example.dart';
// import 'package:jojo_flutter_plan_pkg/pages/calender/pages/multi_example.dart';
// import 'package:jojo_flutter_plan_pkg/pages/calender/pages/range_example.dart';
// import 'package:jojo_flutter_plan_pkg/pages/calender/pages/JoJoCalendarPage.dart';

// void main() {
//   initializeDateFormatting().then((_) => runApp(MyApp()));
// }

// class MyApp extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       title: 'TableCalendar Example',
//       theme: ThemeData(
//         primarySwatch: Colors.blue,
//       ),
//       home: StartPage(),
//     );
//   }
// }

// class StartPage extends StatefulWidget {
//   @override
//   _StartPageState createState() => _StartPageState();
// }

// class _StartPageState extends State<StartPage> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('TableCalendar Example'),
//       ),
//       body: Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             const SizedBox(height: 20.0),
//             ElevatedButton(
//               child: Text('Basics'),
//               onPressed: () => Navigator.push(
//                 context,
//                 MaterialPageRoute(builder: (_) => TableBasicsExample()),
//               ),
//             ),
//             const SizedBox(height: 12.0),
//             ElevatedButton(
//               child: Text('Range Selection'),
//               onPressed: () => Navigator.push(
//                 context,
//                 MaterialPageRoute(builder: (_) => TableRangeExample()),
//               ),
//             ),
//             const SizedBox(height: 12.0),
//             ElevatedButton(
//               child: Text('Events'),
//               onPressed: () => Navigator.push(
//                 context,
//                 MaterialPageRoute(builder: (_) => TableEventsExample()),
//               ),
//             ),
//             const SizedBox(height: 12.0),
//             ElevatedButton(
//               child: Text('Multiple Selection'),
//               onPressed: () => Navigator.push(
//                 context,
//                 MaterialPageRoute(builder: (_) => TableMultiExample()),
//               ),
//             ),
//             const SizedBox(height: 12.0),
//             ElevatedButton(
//               child: Text('Complex'),
//               onPressed: () => Navigator.push(
//                 context,
//                 MaterialPageRoute(builder: (_) => TableComplexExample()),
//               ),
//             ),
//             const SizedBox(height: 20.0),
//           ],
//         ),
//       ),
//     );
//   }
// }
