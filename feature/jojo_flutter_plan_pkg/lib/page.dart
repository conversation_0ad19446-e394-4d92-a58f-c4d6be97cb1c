import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievement_page/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/achievement_detail/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/my_achievements/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/share_achievement/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/teacher_mode/teacher_achievement_types/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/activity_my_gift/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_schedule/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/adjust_record_list/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/after_school_list/after_class_service_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/all_courses_page/course_detail/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/all_courses_page/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/art_material_preview/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/commendation_list/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/comment_list/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_session_list/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/customer_center/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/evaluation_second/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/find_study_partner/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/loading/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/full_screen_video/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/jojo_continuology_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/jojo_shop/jojo_shop_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/learning_incentives/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/math_schedule_list/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/mine_honor/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/my_partners/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/course_task/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/logistics/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/msg_box/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_course_gift_page/course_gift_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/parents_class/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/parent_tools/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/personal_home_module/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/widget/new_subject_recm/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_msg/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_pure_enjoy/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_service_add_teacher/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_study_preference/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/prop_shop/prop_shop_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/recognition/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_detail/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/review_search/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/task_center/task_center_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/teacher_service/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/pdf_preview/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/teacher_university/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study/sea_map/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/page.dart';
import 'package:jojo_flutter_plan_pkg/pages/train_add_teacher/train_add_teacher_page.dart';
import 'package:jojo_flutter_plan_pkg/pages/video_intro_page/page.dart';
import 'package:path/path.dart';

import 'pages/plan_home_map/page.dart';

enum AppPage {
  // 家长课堂
  parentsClass("/plan/parentsClass"),
  // PDF预览页面
  pdfPreviewPage("/plan/pdfPreview"),

  // 指导点评页 - 原生改flutter路由特殊处理，不加前缀 plan
  commentListPage("/course/teacherComments"),

  /// 计划信息页
  planMsgPage("/plan/planMsg"),

  /// 表彰榜
  recognition("/plan/recognition/detail"),

  /// 指导师服务
  teacherServicePage("/plan/teacherService"),

  /// 表彰榜列表
  commendationListPage('/plan/commendation/list'),

  /// 消息盒子
  msgBoxPage("/plan/msgBox"),

  /// 我的表彰
  mineHonorPage("/plan/mineHonor"),

  /// 开始页 - 课程任务页面
  courseTask("/plan/course/task"),

  /// 开始页 - 调整记录页面
  adjustSchedule("/plan/adjustSchedule"),

  /// 物流信息页
  logistics("/plan/logistics"),

  /// 指导师服务北师大-中间页
  teacherServiceUniversityTest("/plan/teacherService/BJNormalUniversityTest"),

  /// 班期调整记录页
  classAdjustRecord("/plan/classAdjust/record"),

  /// 客服中心页
  customerCenter("/plan/customerCenter"),

  /// 题库列表
  questionBankList("/plan/questionBankList"),

  /// 纯享引流课
  pureEnjoyDrainageCourse("/plan/pureEnjoyDrainageCourse"),

  /// 上课页测评二级页
  planEvaluationSecondPage("/plan/evaluationSecondPage"),

  /// 上课页全屏视频播放
  planFullScreenVideoPage("/plan/fullScreenVideoPage"),

  /// 家长工具
  parentToolsPage("/plan/parentTools"),

  /// 我的成就
  achievementPage("/plan/myAchievement"),

  /// 新我的成就页
  myAchievementsPage("/plan/myAchievements"),

  /// 成就详情
  achievementDetailPage("/plan/achievementDetail"),

  /// 分享成就
  shareAchievementPage("/plan/shareAchievement"),

  /// 教研模式下成就分类页面
  teacherAchievementTypesPage("/plan/teacherAchievementTypes"),

  /// 2025上课页首页
  planHomeMapPage("/plan/planHomeWithMap"),

  // 个人主页
  personalHome("/personal/home"),

  ///JoJo 小铺
  joJoShop("/plan/joJoShop"),

  ///连续学详情
  joJoContinuology("/plan/joContinuology"),

  //环节列表
  joJoCourseSessionList("/plan/joCourseSessionList"),

  /// 全部课程
  allCoursesPage("/plan/allCourses"),

  /// 课程详情
  courseDetailPage("/plan/lessonList"),

  /// 学习完成结算Loading页
  courseFinishSettleLoadingPage("/course/finish/settleLoading"),

  /// 学习完成结算页
  courseFinishSettleAccountsPage("/course/finish/settleAccounts"),

  /// 课后服务列表页
  planHomeAfterSchoolListPage("/plan/afterClassListPage"),

  /// 视频介绍页
  planHomeVideoIntrocePage("/plan/planHomeVideoIntroducePage"),

  /// 思维科目计划表
  mathScheduleListPage("/plan/mathScheduleListPage"),

  /// 任务中心
  taskCenterPage("/plan/taskCenterPage"),

  /// 道具小铺
  propShopPage("/plan/propShopPage"),

  /// 2025上课页 - 科目推荐
  newSubjectRecommendedPage("/plan/newSubjectRecommended"),

  /// 赠课列表页
  courseGiftPage("/plan/courseGiftPage"),

  /// 里程碑奖励列表
  jojoMilestoneListPage("/plan/jojoMilestoneListPage"),

  /// 补学详情页
  reviewAssistantPage("/plan/reviewAssistantPage"),

  /// 补学搜索页
  reviewAssistantSearchPage("/plan/reviewAssistantSearchPage"),

  /// 学伴列表页
  jojoStudyPartnerPage("/plan/jojoStudyPartnerPage"),

  /// 训练营加老师页面
  trainAddTeacherPage("/plan/trainAddTeacherPage"),

  ///课程替换页
  planLessonChangePage("/plan/lessonChangePage"),

  // 学伴消息页
  partnerMessagePage("/plan/partnerMessagePage"),

  ///计划页服务卡片，加老师页
  planServiceAddTeacherPage("/plan/serviceAddTeacherPage"),

  ///计划页，学习偏好设置
  planStudyPreferencePage("/plan/studyPreferencePage"),

  /// 我的学伴页
  myPartnersPage("/plan/myPartnersPage"),

  ///计划页，完课活动落地页
  planActivityDetailPage("/plan/activityDetailPage"),

  ///我的奖励页，完课活动查看我的奖励
  planActivityMyGiftPage("/plan/activityMyGiftPage"),

  /// 全部画材预览页面
  artMaterialPreview("/plan/artMaterialPreview"),

  /// 多人学-地图
  teamStudySeaMapPage("/teamStudy/seaMapPage"),

  ///宝贝档案
  babyProfilePage("/plan/babyProfilePage"),

  // 组队学活动页面
  teamStudyActivityPage("/plan/teamStudyActivityPage");

  const AppPage(this.path);

  final String path;

  static final routes = AppPage.values
      .map((page) => JoJoRoute(path: page.path, builder: page.builder))
      .toList();
}

extension PageRoute on AppPage {
  GoRouterWidgetBuilder get builder {
    switch (this) {
      case AppPage.commentListPage:
        return (context, state) => CommentListPageModel(
            classKey: state.queryParams['classKey'],
            courseKey: state.queryParams['courseKey'],
            commentType: state.queryParams['commentType'] ?? '2',
            userId: state.queryParams['userId']);
      case AppPage.commendationListPage:
        return (context, state) => CommendationListPageModel(
              classId: state.queryParams['classId'],
              courseId: state.queryParams['courseId'],
            );
      case AppPage.planMsgPage:
        return (context, state) => PlanMsgPageModel(
              classId: state.queryParams['classId'],
              courseKey: state.queryParams['courseKey'],
              defaultMainTab: state.queryParams['defaultMainTab'],
            );

      case AppPage.recognition:
        return (context, state) => RecognitionPageModel(
              classId: state.queryParams['classId'],
              citeId: state.queryParams['citeId'],
              teacherId: state.queryParams['teacherId'],
            );
      case AppPage.teacherServicePage:
        return (context, state) => TeacherServicePageModel(
              classId: state.queryParams['classId'],
              courseKey: state.queryParams['courseKey'],
              bgColor: state.queryParams['bgColor'],
              channel: state.queryParams['channel'],
            );
      case AppPage.msgBoxPage:
        return (context, state) => const MsgBoxPageModel();
      case AppPage.mineHonorPage:
        return (context, state) => const MineHonorPageModel();

      case AppPage.courseTask:
        return (context, state) => CourseTaskPageModel(
              classId: state.queryParams['classId'],
              courseId: state.queryParams['courseId'],
            );
      case AppPage.adjustSchedule:
        return (context, state) =>
            AdjustSchedulePageModel(queryParams: state.queryParams);

      case AppPage.logistics:
        return (context, state) => const LogisticsPageModel(
            // classId: state.queryParams['classId'],
            // courseId: state.queryParams['courseId'],
            );
      case AppPage.classAdjustRecord:
        return (context, state) =>
            AdjustRecordListPageModel(queryParams: state.queryParams);
      case AppPage.customerCenter:
        return (context, state) =>
            CustomerCenterModel(jojoSceneId: state.queryParams['jojoSceneId']);
      case AppPage.pdfPreviewPage:
        return (context, state) => PdfPreviewPageModel(
              courseId: state.queryParams['courseId'],
              classId: state.queryParams['classId'],
              status: state.queryParams['status'],
            );
      case AppPage.questionBankList:
        return (context, state) => QuestionBankListPageModal(
              courseKey: state.queryParams['courseKey'],
              classId: state.queryParams['classId'],
              classKey: state.queryParams['classKey'],
            );
      case AppPage.pureEnjoyDrainageCourse:
        return (context, state) => PlanPureEnjoyModel(
              queryParams: state.queryParams,
            );
      case AppPage.teacherServiceUniversityTest:
        return (context, state) => TeacherUniversityPageModel(
              params: state.queryParams,
            );
      case AppPage.planEvaluationSecondPage:
        return (context, state) => EvaluationSecondPageModel(
            classId: state.queryParams['classId'] ?? "");
      case AppPage.planFullScreenVideoPage:
        return (context, state) {
          final courseType = state.queryParams['courseType'] ?? "";
          final courseSegmentName =
              state.queryParams['courseSegmentName'] ?? "";
          final videoUrl = state.queryParams['videoUrl'] ?? "";
          final resouceOrientation =
              state.queryParams['resouceOrientation'] ?? "landscape";
          final eventServerName = state.queryParams['eventServerName'] ?? "";
          final exitOnVideoEnd = state.queryParams['exitOnVideoEnd'] == '1';
          final scaleType = state.queryParams['scaleType'];

          return FullScreenVideoPageModel(
            courseType: courseType,
            courseSegmentName: courseSegmentName,
            videoUrl: videoUrl,
            resouceOrientation: resouceOrientation,
            eventServerName: eventServerName,
            exitOnVideoEnd: exitOnVideoEnd,
            scaleType: scaleType,
          );
        };
      case AppPage.parentsClass:
        return (context, state) => ParentsClassPageModel(
              category: state.queryParams['category'],
              classId: state.queryParams['classId'],
            );
      case AppPage.parentToolsPage:
        return (context, state) => ParentToolsPageModel(
              state.queryParams['classId'] ?? "",
              state.queryParams['taskType'] ?? "",
              int.parse(state.queryParams['showDateTime'] ?? "0"),
              int.parse(state.queryParams['schedule'] ?? "1"),
            );
      case AppPage.achievementPage:
        return (context, state) => AchievementPage(
            state.queryParams['classId'] ?? "",
            state.queryParams['medalId'] ?? "");
      case AppPage.planHomeMapPage:
        return (context, state) => PlanHomeWithMapPageModel(
            state.queryParams['classKey'] ?? "",
            state.queryParams['routerRedirect'] ?? "");
      case AppPage.personalHome:
        return (context, state) {
          final tabIndexStr = state.queryParams['subjectType'];
          final tabIndex = tabIndexStr != null
              ? int.tryParse(tabIndexStr)
              : null; //处理客态没传 subjectType 的情况
          final partnerIdStr = state.queryParams['partnerId'];
          int? partnerId;
          if (partnerIdStr != null) {
            partnerId = int.tryParse(partnerIdStr);
          }
          String? classKey = state.queryParams['classKey'];
          return PersonalHomePageModel(tabIndex, partnerId, classKey);
        };
      case AppPage.joJoShop:
        return (context, state) => JoJoShopPage(
              state.queryParams['subjectType'] ?? "",
            );
      case AppPage.joJoContinuology:
        return (context, state) => JoJoContinuologyPageModel(
              state.queryParams['subjectType'] ?? "",
              int.tryParse(state.queryParams['loadingScene'] ?? '1') ?? 1,
            );
      case AppPage.joJoCourseSessionList:
        return (context, state) => JoJoCourseSessionListPage(
              classKey: state.queryParams['classKey'] ?? "",
              lessonKey: state.queryParams['lessonKey'] ?? "",
              engineType:
                  int.tryParse(state.queryParams['engineType'] ?? "0") ?? 0,
              extra: state.queryParams['extra'],
              deviceOrientation:
                  state.queryParams['deviceOrientation'] ?? "landscape",
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? '1') ?? 1,
            );
      case AppPage.allCoursesPage:
        return (context, state) => AllCoursesPage(
              subjectType: int.tryParse(state.queryParams['subjectType'] ?? ""),
              classKey: state.queryParams['classKey'],
            );
      case AppPage.courseDetailPage:
        return (context, state) => CourseDetailPage(
              classKey: state.queryParams['classKey'] ?? "",
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? ""),
            );
      case AppPage.myAchievementsPage:
        return (context, state) {
          final partnerIdStr = state.queryParams['partnerId'];
          int? partnerId;
          if (partnerIdStr != null) {
            partnerId = int.tryParse(partnerIdStr);
          }
          return MyAchievementsPage(
            subjectType: int.tryParse(state.queryParams['subjectType'] ?? ""),
            partnerId: partnerId,
          );
        };
      case AppPage.achievementDetailPage:
        return (context, state) => AchievementDetailPage(
              int.tryParse(state.queryParams['medalId'] ?? "") ?? 0,
            );
      case AppPage.shareAchievementPage:
        return (context, state) => ShareAchievementPage(
              medalId: int.tryParse(state.queryParams['medalId'] ?? "") ?? 0,
            );
      case AppPage.teacherAchievementTypesPage:
        return (context, state) {
          final partnerIdStr = state.queryParams['partnerId'];
          int? partnerId;
          if (partnerIdStr != null) {
            partnerId = int.tryParse(partnerIdStr);
          }
          return TeacherAchievementTypesPage(
            subjectType: int.tryParse(state.queryParams['subjectType'] ?? ""),
            partnerId: partnerId,
          );
        };
      case AppPage.courseFinishSettleLoadingPage:
        return (context, state) => FinishCourseSettleLoadingPage(
              state.queryParams,
            );
      case AppPage.courseFinishSettleAccountsPage:
        return (context, state) => FinishCourseSettleAccountsPage(
              state.queryParams,
            );
      case AppPage.planHomeAfterSchoolListPage:
        return (context, state) => AfterSchoolListPageModel(
              subjectType:
                  int.tryParse(state.queryParams['subjectType'] ?? "2"),
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? '1') ?? 1,
            );
      case AppPage.mathScheduleListPage:
        return (context, state) => MathScheduleListPage(
              int.tryParse(state.queryParams['courseId'] ?? "") ?? 0,
              tagCode: int.tryParse(state.queryParams['tagCode'] ?? ""),
            );
      case AppPage.taskCenterPage:
        return (context, state) => const TaskCenterPage();
      case AppPage.propShopPage:
        return (context, state) => PropShopPage(
              subjectType:
                  int.tryParse(state.queryParams['subjectType'] ?? "2") ?? 2,
              lackToolAudioPath: state.queryParams['lackToolAudioPath'] ?? "",
            );
      case AppPage.newSubjectRecommendedPage:
        return (context, state) => NewSubjectRecommendedPage(state.queryParams);
      case AppPage.courseGiftPage:
        return (context, state) => CourseGiftPageModel(
              classKey: state.queryParams['classKey'] ?? "",
            );
      case AppPage.planHomeVideoIntrocePage:
        return (context, state) => VideoIntroducePageModel(
              url: state.queryParams['url'] ?? "",
              isFromDetailPage:
                  int.tryParse(state.queryParams['isFromDetailPage'] ?? "0"),
              dataString: state.queryParams['dataString'],
            );
      case AppPage.jojoMilestoneListPage:
        return (context, state) => MilestoneListPage(
              subjectType:
                  int.tryParse(state.queryParams['subjectType'] ?? "2") ?? 2,
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? "1") ?? 1,
            );
      case AppPage.reviewAssistantPage:
        return (context, state) => ReviewDetailPageModel(
              subjectColor: state.queryParams['subjectColor'],
              classKey: state.queryParams['classKey'],
              courseKey: state.queryParams['courseKey'],
              classId: state.queryParams['classId'],
              buriedString: state.queryParams['buriedString'],
              weekId: int.tryParse(state.queryParams['weekId'] ?? '0'),
              segmentId: int.tryParse(state.queryParams['segmentId'] ?? '0'),
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? '1'),
            );
      case AppPage.reviewAssistantSearchPage:
        return (context, state) => ReviewSearchPageModel(
              subjectColor: state.queryParams['subjectColor'],
              courseKey: state.queryParams['courseKey'],
              classId: state.queryParams['classId'],
              buriedString: state.queryParams['buriedString'],
            );
      case AppPage.jojoStudyPartnerPage:
        return (context, state) {
          int? subjectType;
          var subjectTypeStr = state.queryParams['subjectType'];
          if (subjectTypeStr != null) {
            subjectType = int.tryParse(subjectTypeStr);
          }
          return FindStudyPartnerPage(subjectType: subjectType);
        };
      case AppPage.trainAddTeacherPage:
        return (context, state) => TrainAddTeacherPage(
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? "1") ?? 1,
              params: state.queryParams,
            );
      case AppPage.planLessonChangePage:
        return (context, state) => PlanLessonChangePage(
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? "1") ?? 1,
              classId: int.tryParse(state.queryParams['classId'] ?? ""),
              courseId: int.tryParse(state.queryParams['courseId'] ?? ""),
            );
      case AppPage.partnerMessagePage:
        return (context, state) => PartnerMessagePage(
              key: state.pageKey,
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? "1") ?? 1,
            );
      case AppPage.planServiceAddTeacherPage:
        return (context, state) => PlanAddTeacherPageModel(
              dataJsonString: state.queryParams['dataJsonString'],
            );
      case AppPage.planStudyPreferencePage:
        return (context, state) => PlanStudyPreferencePageModel(
              classId: int.tryParse(state.queryParams['classId'] ?? "0") ?? 0,
              courseId: int.tryParse(state.queryParams['courseId'] ?? "0") ?? 0,
              dataJsonString: state.queryParams['dataJsonString'],
            );
      case AppPage.myPartnersPage:
        return (context, state) => const MyPartnersPage();
      case AppPage.planActivityDetailPage:
        return (context, state) => PlanActivityDetailPageModel(
              classId: int.tryParse(state.queryParams['classId'] ?? "0"),
              courseId: int.tryParse(state.queryParams['courseId'] ?? "0"),
              activityId: int.tryParse(state.queryParams['activityId'] ?? "0"),
              pageId: int.tryParse(state.queryParams['pageId'] ?? "0"),
              buriedString: state.queryParams['buriedString'],
              subjectColor: state.queryParams['subjectColor'],
            );
      case AppPage.planActivityMyGiftPage:
        return (context, state) => ActivityMyGiftPageModel(
              classId: int.tryParse(state.queryParams['classId'] ?? "0"),
              courseId: int.tryParse(state.queryParams['courseId'] ?? "0"),
              activityId: int.tryParse(state.queryParams['activityId'] ?? "0"),
              pageId: int.tryParse(state.queryParams['pageId'] ?? "0"),
              buriedString: state.queryParams['buriedString'],
              subjectColor: state.queryParams['subjectColor'],
            );
      case AppPage.artMaterialPreview:
        return (context, state) => ArtMaterialPreviewPage(
              classKey: state.queryParams['classKey'] ?? "",
              segmentId:
                  int.tryParse(state.queryParams['segmentId'] ?? '0') ?? 0,
              lessonKey: state.queryParams['lessonKey'] ?? "",
              loadingScene:
                  int.tryParse(state.queryParams['loadingScene'] ?? '3'),
              subjectColor: state.queryParams['subjectColor'],
            );
      case AppPage.babyProfilePage:
        return (context, state) => BabyProfilePage(
              needCheckAge: "false" != state.queryParams['showAge'],
              needCheckGrade: "false" != state.queryParams['showGrade'],
            );
      case AppPage.teamStudySeaMapPage:
        return (context, state) {
          var subjectType =
              int.tryParse(state.queryParams['subjectType'] ?? '');
          return TeamStudySeaMapPage(subjectType: subjectType);
        };
      case AppPage.teamStudyActivityPage:
        return (context, state) => const TeamStudyActivityPage();
    }
  }
}
