import 'package:jojo_flutter_base/base.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 自定义的mock类
class JoJoBridgeCommonMockerCalendar extends NativeBridge {
  @override
  Future<JoJoBridgeResponse<void>> addAppWidget({required String? type}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> changeAppIcon(
      {required String? subjectType}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> closeAndJump({required String? url}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> closeMiniWindow({required String? jumpUrl}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> completedParentGuide(
      {required String icon,
      required String detailText,
      required String courseKey,
      required String classId,
      required String teacherId,
      required String teacherName}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> dial({required String phoneNumber}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<AppIconInfo>> getCurrentAppIconType() {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<DeviceInfo>> getDeviceInfo() {
    return Future.value(
      JoJoBridgeResponse<DeviceInfo>(
        200,
        'ok',
        const DeviceInfo(
          platform: 'ios',
          appBuild: '63381',
          appVersion: '1.78.0',
          channel: 'appstore',
          deviceOSVersion: '',
          packageName: 'com.shusheng.JoJoRead',
          appBundleIdentifier: 'com.shusheng.JoJoRead',
          deviceOS: 'ios',
          deviceUniqueIdentifier: '',
          manufacturer: '',
          productVersion: '',
        ),
      ),
    );
  }

  @override
  Future<JoJoBridgeResponse<Environment>> getEnvironment() {
    return Future.value(
      JoJoBridgeResponse<Environment>(
        200,
        'ok',
        const Environment(environment: 'FAT'),
      ),
    );
  }

  @override
  Future<JoJoBridgeResponse<SettingSwitchInfo>> getSettingSwitchInfo() {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<UserInfo>> getUserInfo() {
    return Future.value(
      JoJoBridgeResponse(
        200,
        'ok',
        const UserInfo(authToken: '', uid: '', regionCode: "CN"),
      ),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> insertCalendar(
      {required int dtRemindTime,
      required int dtStartTime,
      required String title,
      required int? dtEndTime,
      required String? desc}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  String get name => 'mock_bridge';

  @override
  Future<JoJoBridgeResponse<void>> navigate({required String url}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<NativeValue>> operationNativeValueGet(
      {required String key, bool? isUserData}) async {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', const NativeValue(value: "搜索0_搜索2")));
  }

  @override
  Future<JoJoBridgeResponse<void>> operationNativeValueSet(
      {required String key, required String value, bool? isUserData}) {
    return Future.value(
        JoJoBridgeResponse(200, 'ok', NativeValue(value: value)));
  }

  @override
  Future<JoJoBridgeResponse<void>> popInMiniWindow() {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  Future<JoJoBridgeResponse<void>> popToNative() {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> printNativeLog({
    required String? tag,
    required String? message,
    required String? level,
  }) {
    //

    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<PermissionInfo>> requestPermission(
      {required String permission, required String key}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> saveBabyInformation(
      {required String babyInfoStr}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> saveBase64Image({required String base64}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> setSettingSwitchInfo(
      {required bool? videoPause,
      required bool? readPasteBoard,
      required bool? cicadaLog}) {
    return Future.value(
      JoJoBridgeResponse(200, 'ok', null),
    );
  }

  @override
  Future<JoJoBridgeResponse<void>> shareToPlatform({
    required int platformType,
    required int scene,
    required int shareType,
    required String image,
    required String? title,
    required String? desc,
    required String? url,
  }) {
    return Future.value(
      JoJoBridgeResponse(200, '模拟分享', null),
    );
  }
}
