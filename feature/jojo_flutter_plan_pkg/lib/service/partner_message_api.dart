import 'dart:convert';
import 'dart:math';

import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:retrofit/http.dart';
import '../common/dio/use.dart';
import '../pages/partner_message/model/partner_message_data.dart';
part 'partner_message_api.g.dart';

@RestApi()
abstract class PartnerMessageApi {
  factory PartnerMessageApi(Dio dio, {String baseUrl}) = _PartnerMessageApi;

  // 获取消息列表 可以分页拉取
  @GET("/api/pagani/learning-friends-messages")
  Future<PartnerMessageModel> getPartnerMessages(
      @Query('offset') String offset, @Query('size') int size);
}

final partnerMessageApiMock = PartnerMessageApiMock(); // mock的时候使用

final partnerMessageApi = PartnerMessageApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);

class PartnerMessageApiMock implements PartnerMessageApi {
  // 测试消息类型的数据
  final testTypeMessages = [
    {
      "msgId": 832576249117972481,
      "content": "邀请你加入船队",
      "type": 4, //消息类型(4组队邀请)
      "teamStatus": 1, //小队状态(0未解锁,1组队中,2进行中,3已结束)
      "inviteRecordId": 1, //邀请记录id(同意/拒绝参数)
      "inviteUserId": 1, //邀请用户id(同意/拒绝参数)
      "teamMemberList": [
        {
          "memberId": 1, //分布式id
          "photo": "", //头像
          "nickname": "昵称",
          "dayCount": 1 //最近连续天数
        }
      ],
      "nickName": "自建排期",
      "avatarUrl":
          "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/812727199036508163/17504092746817tidf9.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
      "receiveTime": 1755141646308,
      "timeDesc": "08月14日",
      "friendsAcceptRequest": 3, //处理状态(1拒绝,2通过,3处理中)
      "jumpUrl":
          "tinman-router://cn.tinman.jojoread/flutter/personal/home?classKey=-1&partnerId=200532170"
    },
        {
      "msgId": 832576249117972481,
      "content": "邀请你加入船队",
      "type": 4, //消息类型(4组队邀请)
      "teamStatus": 1, //小队状态(0未解锁,1组队中,2进行中,3已结束)
      "inviteRecordId": 1, //邀请记录id(同意/拒绝参数)
      "inviteUserId": 1, //邀请用户id(同意/拒绝参数)
      "teamMemberList": [
        {
          "memberId": 1, //分布式id
          "photo": "", //头像
          "nickname": "昵称",
          "dayCount": 1 //最近连续天数
        }
      ],
      "nickName": "自建排期",
      "avatarUrl":
          "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/812727199036508163/17504092746817tidf9.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
      "receiveTime": 1755141646308,
      "timeDesc": "08月14日",
      "friendsAcceptRequest": 2, //处理状态(1拒绝,2通过,3处理中)
      "jumpUrl":
          "tinman-router://cn.tinman.jojoread/flutter/personal/home?classKey=-1&partnerId=200532170"
    },
        {
      "msgId": 832576249117972481,
      "content": "邀请你加入船队",
      "type": 4, //消息类型(4组队邀请)
      "teamStatus": 1, //小队状态(0未解锁,1组队中,2进行中,3已结束)
      "inviteRecordId": 1, //邀请记录id(同意/拒绝参数)
      "inviteUserId": 1, //邀请用户id(同意/拒绝参数)
      "teamMemberList": [
        {
          "memberId": 1, //分布式id
          "photo": "", //头像
          "nickname": "昵称",
          "dayCount": 1 //最近连续天数
        }
      ],
      "nickName": "自建排期",
      "avatarUrl":
          "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/812727199036508163/17504092746817tidf9.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
      "receiveTime": 1755141646308,
      "timeDesc": "08月14日",
      "friendsAcceptRequest": 1, //处理状态(1拒绝,2通过,3处理中)
      "jumpUrl":
          "tinman-router://cn.tinman.jojoread/flutter/personal/home?classKey=-1&partnerId=200532170"
    },
        {
      "msgId": 832576249117972481,
      "content": "邀请你加入船队",
      "type": 4, //消息类型(4组队邀请)
      "teamStatus": 1, //小队状态(0未解锁,1组队中,2进行中,3已结束)
      "inviteRecordId": 1, //邀请记录id(同意/拒绝参数)
      "inviteUserId": 1, //邀请用户id(同意/拒绝参数)
      "teamMemberList": [
        {
          "memberId": 1, //分布式id
          "photo": "", //头像
          "nickname": "昵称",
          "dayCount": 1 //最近连续天数
        }
      ],
      "nickName": "自建排期",
      "avatarUrl":
          "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/812727199036508163/17504092746817tidf9.png?checksumV2=md5Hex%3D0fadc4c02ea3641758ceb25e4ca84865",
      "receiveTime": 1755141646308,
      "timeDesc": "08月14日",
      "friendsAcceptRequest": 0, //处理状态(1拒绝,2通过,3处理中)
      "jumpUrl":
          "tinman-router://cn.tinman.jojoread/flutter/personal/home?classKey=-1&partnerId=200532170"
    },
    {
      "msgId": 123456, // 消息id
      "content": "为你的动态送了花花", // 消息内容
      "type": 1, // 1-送花花 2-戳一戳 3-学伴申请 // 消息类型
      "partnerId": 1002, // 学伴id
      "nickName": "测试学伴1", // 学伴昵称
      "avatarUrl":
          "http://img2.baidu.com/it/u=3906212634,2132778619&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500", // 学伴头像
      "receiveTime": 1750829937, // 消息接收时间
      "timeDesc": "刚刚", // 时间描述
      "friendsAcceptRequest": 0, //是否通过好友请求，仅学伴申请类型且操作后此字段才有值 0-否 1-是
    },
    {
      "msgId": 123456, // 消息id
      "content": "戳了你", // 消息内容
      "type": 2, // 1-送花花 2-戳一戳 3-学伴申请 // 消息类型
      "partnerId": 1002, // 学伴id
      "nickName": "测试学伴1", // 学伴昵称
      "avatarUrl":
          "http://img2.baidu.com/it/u=3906212634,2132778619&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500", // 学伴头像
      "receiveTime": 1750829937, // 消息接收时间
      "timeDesc": "刚刚", // 时间描述
      "friendsAcceptRequest": 0, //是否通过好友请求，仅学伴申请类型且操作后此字段才有值 0-否 1-是
    },
    {
      "msgId": 123457, // 消息id
      "content": "已成为你的学伴", // 消息内容
      "type": 3, // 1-送花花 2-戳一戳 3-学伴申请 // 消息类型
      "partnerId": 1003, // 学伴id
      "nickName": "测试学伴2", // 学伴昵称
      "avatarUrl":
          "https://img1.baidu.com/it/u=3050694377,2234178081&fm=253&fmt=auto&app=120&f=JPEG?w=800&h=800", // 学伴头像
      "receiveTime": 1750829937, // 消息接收时间
      "timeDesc": "5分钟前", // 时间描述
      "friendsAcceptRequest": 0, //是否通过好友请求，仅学伴申请类型且操作后此字段才有值 0-否 1-是
    },
    {
      "msgId": 123457, // 消息id
      "content": "申请和你成为学伴", // 消息内容
      "type": 3, // 1-送花花 2-戳一戳 3-学伴申请 // 消息类型
      "partnerId": 1003, // 学伴id
      "nickName": "测试学伴2", // 学伴昵称
      "avatarUrl":
          "https://img1.baidu.com/it/u=3050694377,2234178081&fm=253&fmt=auto&app=120&f=JPEG?w=800&h=800", // 学伴头像
      "receiveTime": 1750829937, // 消息接收时间
      "timeDesc": "5分钟前", // 时间描述
      "friendsAcceptRequest": 1, //1 拒绝
    },
    {
      "msgId": 123457, // 消息id
      "content": "申请和你成为学伴", // 消息内容
      "type": 3, // 1-送花花 2-戳一戳 3-学伴申请 // 消息类型
      "partnerId": 1003, // 学伴id
      "nickName": "测试学伴2", // 学伴昵称
      "avatarUrl":
          "https://img1.baidu.com/it/u=3050694377,2234178081&fm=253&fmt=auto&app=120&f=JPEG?w=800&h=800", // 学伴头像
      "receiveTime": 1750829937, // 消息接收时间
      "timeDesc": "5分钟前", // 时间描述
      "friendsAcceptRequest": 2, //同意
    },
    {
      "msgId": 123457, // 消息id
      "content": "申请和你成为学伴", // 消息内容
      "type": 3, // 1-送花花 2-戳一戳 3-学伴申请 // 消息类型
      "partnerId": 1003, // 学伴id
      "nickName": "测试学伴2", // 学伴昵称
      "avatarUrl":
          "https://img1.baidu.com/it/u=3050694377,2234178081&fm=253&fmt=auto&app=120&f=JPEG?w=800&h=800", // 学伴头像
      "receiveTime": 1750829937, // 消息接收时间
      "timeDesc": "5分钟前", // 时间描述
      "friendsAcceptRequest": 3, //未处理
    },
  ];
  late List testMoreMessages;

  PartnerMessageApiMock() {
    testMoreMessages = [];
    for (int i = 0; i < 45; i++) {
      final message = {
        "msgId": 1000 + i, // 消息id
        "content": "更多数据测试", // 消息内容
        "type": i % 3 + 1, // 1-送花花 2-戳一戳 3-学伴申请 // 消息类型
        "partnerId": 10000 + i, // 学伴id
        "nickName": "测试学伴$i", // 学伴昵称
        "avatarUrl":
            "https://img1.baidu.com/it/u=3050694377,2234178081&fm=253&fmt=auto&app=120&f=JPEG?w=800&h=800", // 学伴头像
        "receiveTime": 1750829937, // 消息接收时间
        "timeDesc": "5分钟前", // 时间描述
        "friendsAcceptRequest": Random().nextInt(3) //同意
      };
      testMoreMessages.add(message);
    }
  }
  @override
  Future<PartnerMessageModel> getPartnerMessages(
      String offset, int size) async {
    int type = 1; // 测试类型 1:消息类型  2: 加载更多

    if (type == 1) {
      final result = <String,dynamic>{
        "messages": testTypeMessages,
        "size": testTypeMessages.length
      };
      return PartnerMessageModel.fromJson(result);
    } else {
      List messages = [];
      int idx = 0;
      for (int i = 0; i < testMoreMessages.length; i++) {
        int msgId = testMoreMessages[i]['msgId'];
        if (msgId == int.parse(offset)) {
          idx = i;
          break;
        }
      }
      for (int i = idx; i < testMoreMessages.length; i++) {
        var message = testMoreMessages[i];
        if (messages.length >= size) {
          break;
        }
        messages.add(message);
      }
      final result = {
        "messages": messages,
        "size": messages.length
      };
      return PartnerMessageModel.fromJson(result);
    }
  }
}

// class MedalAPIMock implements MedalAPI {

//   @override
//   Future<MyMedalsData> getMedalsList(String classId) async {
//     String jsonString = """
//      {
//     "courseInfo": {
//       "subjectType": 2,
//       "courseId": 80,
//       "courseSegmentCode": 4,
//       "courseSegmentName": "L4",
//       "courseName": "《L4体验课内测》",
//       "courseType": 1
//     },
//     "shareInfo": {
//       "shareText": "和我一起在叫叫学习进步吧",
//       "scanCodeJumpUrl": "https://mp6b.cn/Z1ltdEGfBH?r_token=i6rjiw",
//       "scanCodeJumpOriginalUrl": "https://boom.fat.tinman.cn/activity/bind/i6rjiw?inviteCode=750498570487493634&channel=123-750498570487493634&appId=&activityType=REFERRAL&officialId=wx6bc1e63a0998b3b7&channelNo=&r_token=i6rjiw"
//     },
//     "medalList": [
//       {
//         "medalId": 174,
//         "medalName": "签到勋章",
//         "medalRemark": "",
//         "hasMedal": true,
//         "lockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750290838450899969.png",
//         "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750290876241579009.png",
//         "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750292374707014657/1735523642057/map_medal.zip.android",
//         "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750292391723305985/1735523645925/map_medal.zip.ios",
//         "resourceFlutter": "https://jojopublicuat.tinman.cn/edu/admin/teacher/752984439013893121/1736165480314/map_medal_0.zip.flutter",
//         "getTime": 1735557617090,
//         "description": "课时勋章描述，产品补充后再改"
//       },
//       {
//         "medalId": 175,
//         "medalName": "课时1勋章",
//         "medalRemark": "",
//         "hasMedal": false,
//         "lockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750292694396865537.png",
//         "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750292727519284225.png",
//         "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750292743684131841/1735523729907/map_medal.zip.android",
//         "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750292757080738817/1735523732981/map_medal.zip.ios",
//         "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750295231745264641/1735524323077/map_medal.zip.flutter",
//         "description": "课时勋章描述，产品补充后再改"
//       },
//       {
//         "medalId": 176,
//         "medalName": "课时2勋章",
//         "medalRemark": "1",
//         "hasMedal": false,
//         "lockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311476007832577.png",
//         "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311506781441025.png",
//         "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311676403289089/1735528244260/map_medal.zip.android",
//         "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311688029899777/1735528246552/map_medal.zip.ios",
//         "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311705750834177/1735528250792/map_medal.zip.flutter",
//         "description": "课时勋章描述，产品补充后再改"
//       },
//       {
//         "medalId": 177,
//         "medalName": "课时3勋章",
//         "medalRemark": "",
//         "hasMedal": false,
//         "lockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311810952367105.png",
//         "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311844670376961.png",
//         "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311859065228289/1735528287387/map_medal.zip.android",
//         "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311871471979521/1735528290287/map_medal.zip.ios",
//         "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311885313182721/1735528293564/map_medal.zip.flutter",
//         "description": "课时勋章描述，产品补充后再改"
//       },
//       {
//         "medalId": 178,
//         "medalName": "课时4勋章",
//         "medalRemark": "1",
//         "hasMedal": false,
//         "lockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750311995170393089.png",
//         "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750312019002428417.png",
//         "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750312038514330625/1735528330187/map_medal.zip.android",
//         "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750312050463902721/1735528333005/map_medal.zip.ios",
//         "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750312067165621249/1735528336949/map_medal.zip.flutter",
//         "description": "课时勋章描述，产品补充后再改"
//       },
//       {
//         "medalId": 179,
//         "medalName": "课时5勋章",
//         "medalRemark": "",
//         "hasMedal": false,
//         "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750312209147005953.png",
//         "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750312225202801665/1735528374727/map_medal.zip.android",
//         "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750312239455046657/1735528378052/map_medal.zip.ios",
//         "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/750312252511915009/1735528381159/map_medal.zip.flutter",
//         "description": "课时勋章描述，产品补充后再改"
//       }
//     ]
//   }
//     """;
//     return MyMedalsData.fromJson(json.decode(jsonString));
//   }