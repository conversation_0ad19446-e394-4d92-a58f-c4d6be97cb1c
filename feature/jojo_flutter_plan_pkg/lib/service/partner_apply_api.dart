import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/base_namespace.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojo_flutter_plan_pkg/common/dio/use.dart';
part 'partner_apply_api.g.dart';


@RestApi()
abstract class PartnerApplyApi {

  factory PartnerApplyApi(Dio dio, {String baseUrl}) = _PartnerApplyApi;
  
  // 获取消息列表 可以分页拉取
  @PATCH("/api/pagani/learning-friends/{id}")
  Future<dynamic> handleApply(@Path('id') int id,@Body() Map<String, dynamic> map);

  @PATCH("/api/pagani/study-team-invitations/{id}")
  Future<dynamic> handleGroupInvited(@Path('id') int id,@Body() Map<String, dynamic> map);
}

final partnerApplyApi = PartnerApplyApi(pageDio, baseUrl: BaseAddress.baseCommonApiPath);