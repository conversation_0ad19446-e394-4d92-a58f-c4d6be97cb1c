<svg width="375" height="565" viewBox="0 0 375 565" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_298_9505)">
<g opacity="0.34">
<path d="M207.625 134.362L164.014 137.786C162.485 137.906 161.118 136.851 160.849 135.342L132.318 -24.6264L176.354 -24.3748L207.625 134.362Z" fill="#DBAF00"/>
<path d="M207.625 134.362L164.014 137.786C162.485 137.906 161.118 136.851 160.849 135.342L132.318 -24.6264L176.354 -24.3748L207.625 134.362Z" fill="url(#paint0_linear_298_9505)"/>
</g>
<g filter="url(#filter0_ddd_298_9505)">
<rect x="20" y="274" width="335" height="271" rx="24" fill="white"/>
<g clip-path="url(#clip1_298_9505)">
<g filter="url(#filter1_d_298_9505)">
<path d="M331 120C344.255 120 355 130.745 355 144V250C355 263.255 344.255 274 331 274H44C30.7452 274 20 263.255 20 250V144C20 130.745 30.7452 120 44 120H331ZM168.139 130.017C163.644 130.017 160 133.66 160 138.154C160 142.649 163.644 146.293 168.139 146.293H206.861C211.356 146.293 215 142.649 215 138.154C215 133.66 211.356 130.017 206.861 130.017H168.139Z" fill="#FCF3BA"/>
<path d="M331 120C344.255 120 355 130.745 355 144V250C355 263.255 344.255 274 331 274H44C30.7452 274 20 263.255 20 250V144C20 130.745 30.7452 120 44 120H331ZM168.139 130.017C163.644 130.017 160 133.66 160 138.154C160 142.649 163.644 146.293 168.139 146.293H206.861C211.356 146.293 215 142.649 215 138.154C215 133.66 211.356 130.017 206.861 130.017H168.139Z" fill="url(#paint1_radial_298_9505)" fill-opacity="0.3"/>
<path d="M331 120C344.255 120 355 130.745 355 144V250C355 263.255 344.255 274 331 274H44C30.7452 274 20 263.255 20 250V144C20 130.745 30.7452 120 44 120H331ZM168.139 130.017C163.644 130.017 160 133.66 160 138.154C160 142.649 163.644 146.293 168.139 146.293H206.861C211.356 146.293 215 142.649 215 138.154C215 133.66 211.356 130.017 206.861 130.017H168.139Z" fill="url(#paint2_linear_298_9505)"/>
</g>
<g clip-path="url(#clip2_298_9505)">
<line y1="-5.824" x2="248.976" y2="-5.824" transform="matrix(-1 0 0 1 456.656 128)" stroke="url(#paint3_linear_298_9505)" stroke-width="11.648" stroke-dasharray="11.65 11.65"/>
<line y1="-5.824" x2="248.976" y2="-5.824" transform="matrix(-1 0 0 1 490.144 139.648)" stroke="url(#paint4_linear_298_9505)" stroke-width="11.648" stroke-dasharray="11.65 11.65"/>
</g>
<g clip-path="url(#clip3_298_9505)">
<line x1="-81.6559" y1="122.176" x2="167.32" y2="122.176" stroke="url(#paint5_linear_298_9505)" stroke-width="11.648" stroke-dasharray="11.65 11.65"/>
<line x1="-115.144" y1="133.824" x2="133.832" y2="133.824" stroke="url(#paint6_linear_298_9505)" stroke-width="11.648" stroke-dasharray="11.65 11.65"/>
</g>
</g>
</g>
<path d="M240.101 -23.8047L192.992 -24.074L163.921 137.772L207.348 134.726C209.58 134.569 211.355 133.004 211.751 130.842L240.101 -23.8047Z" fill="white"/>
<path d="M240.101 -23.8047L192.992 -24.074L163.921 137.772L207.348 134.726C209.58 134.569 211.355 133.004 211.751 130.842L240.101 -23.8047Z" fill="url(#paint7_linear_298_9505)"/>
<rect width="375" height="120" fill="url(#paint8_linear_298_9505)"/>
</g>
<defs>
<filter id="filter0_ddd_298_9505" x="-4" y="96" width="383" height="473" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="12"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.389029 0 0 0 0 0.296744 0 0 0 0 0.112176 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_298_9505"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.862624 0 0 0 0 0.539254 0 0 0 0 0.0190513 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_298_9505" result="effect2_dropShadow_298_9505"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.95767 0 0 0 0 0.940874 0 0 0 0 0.789707 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_298_9505" result="effect3_dropShadow_298_9505"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_298_9505" result="shape"/>
</filter>
<filter id="filter1_d_298_9505" x="18" y="120" width="339" height="158" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.670673 0 0 0 0 0.524419 0 0 0 0 0.0438693 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_298_9505"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_298_9505" result="shape"/>
</filter>
<linearGradient id="paint0_linear_298_9505" x1="156.056" y1="-40.5267" x2="214.491" y2="125.517" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCF9E0"/>
<stop offset="1" stop-color="#FCF9E0" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint1_radial_298_9505" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(4.49999 223.5) rotate(-43.7114) scale(172.931 168.765)">
<stop stop-color="#FCCD34" stop-opacity="0"/>
<stop offset="1" stop-color="#FCE534"/>
</radialGradient>
<linearGradient id="paint2_linear_298_9505" x1="187.5" y1="120" x2="198.5" y2="245" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_298_9505" x1="0" y1="0.5" x2="248.976" y2="0.5" gradientUnits="userSpaceOnUse">
<stop offset="0.471154" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_298_9505" x1="0" y1="0.5" x2="248.976" y2="0.5" gradientUnits="userSpaceOnUse">
<stop offset="0.471154" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_298_9505" x1="-81.6559" y1="128.5" x2="167.32" y2="128.5" gradientUnits="userSpaceOnUse">
<stop offset="0.471154" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_298_9505" x1="-115.144" y1="140.148" x2="133.832" y2="140.148" gradientUnits="userSpaceOnUse">
<stop offset="0.471154" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_298_9505" x1="224.96" y1="-18.1263" x2="160.003" y2="125.613" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCF9E0"/>
<stop offset="1" stop-color="#FCF9E0" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint8_linear_298_9505" x1="188" y1="0" x2="188" y2="140.645" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCF9DE"/>
<stop offset="0.589209" stop-color="#FCF9DE" stop-opacity="0.76314"/>
<stop offset="1" stop-color="#FCF9DE" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_298_9505">
<rect width="375" height="565" fill="white"/>
</clipPath>
<clipPath id="clip1_298_9505">
<rect x="20" y="120" width="335" height="154" rx="24" fill="white"/>
</clipPath>
<clipPath id="clip2_298_9505">
<rect width="209.664" height="23.296" fill="white" transform="matrix(-1 0 0 1 450.832 116.352)"/>
</clipPath>
<clipPath id="clip3_298_9505">
<rect width="209.664" height="23.296" fill="white" transform="translate(-75.8319 116.352)"/>
</clipPath>
</defs>
</svg>
