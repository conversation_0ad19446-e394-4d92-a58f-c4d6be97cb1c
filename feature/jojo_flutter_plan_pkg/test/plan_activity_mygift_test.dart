// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/download/jojo_down_gray.dart';
import 'package:jojo_flutter_base/models/exception_data.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige_calendar.dart';
import 'package:jojo_flutter_plan_pkg/pages/activity_my_gift/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_activity_detail_page/model/activity_detail_data.dart';
import 'package:jojo_flutter_plan_pkg/service/plan_activity_detail_api.dart';
import 'package:mockito/mockito.dart';

class MockDownManager extends Mock implements AbsDownloadManager {
  @override
  Future<void> downloadUrl(List<String> urlList, {Function(double p1)? progressListener, Function(Map<String, String> p1)? successListener, Function(UnifiedExceptionData p1)? failListener, bool isNeedCancel = true}) {
    successListener?.call(
        {"url": "https://jojopublicfat.jojoread.com/vulcan/nuwa-admin/appicon/810171081143781379/1749799847003d45xdb.png?checksumV2=md5Hex%3D880b37ad6dc1c5f546b30cff90b63610", "path": "11.png"});
    return Future.value();
  }
}

/// Mock 下载管理器 - 可以控制成功/失败
class MockDownloadManager extends Mock implements AbsDownloadManager {
  final bool shouldSucceed;
  final bool shouldUnzip;
  final String? errorMessage;

  MockDownloadManager({
    this.shouldSucceed = true,
    this.shouldUnzip = false,
    this.errorMessage,
  });

  @override
  Future<void> downloadUrl(
      List<String> urlList, {
        Function(double)? progressListener,
        Function(Map<String, String>)? successListener,
        Function(UnifiedExceptionData)? failListener,
        bool isNeedCancel = true,
      }) async {

    if (shouldSucceed) {
      // 模拟下载成功
      Map<String, String> result = {};
      for (String url in urlList) {
        String fileName = url.split('/').last;
        if (shouldUnzip && fileName.endsWith('.zip')) {
          // 模拟解压后的路径
          result[url] = "/mock/unzipped/path/$fileName";
        } else {
          // 模拟普通文件路径
          result[url] = "/mock/download/path/$fileName";
        }
      }
      successListener?.call(result);
    } else {
      // 模拟下载失败
      failListener?.call(UnifiedExceptionData(
          message: errorMessage ?? "下载失败",
          code: 1000
      ));
    }
  }
}

// 创建 Dio 的 Mock 类
class MockActivityDetailPageApiService extends Mock implements ActivityDetailApi {
  final PlanActivityData? activityData;
  final bool shouldThrowException;

  MockActivityDetailPageApiService(this.activityData, {this.shouldThrowException = false});

  @override
  Future<PlanActivityData> getCourseSegmentsInfo({required int activityId, required int classId, required int courseId, required int pageId}) {
    if (shouldThrowException) {
      throw Exception("网络请求失败");
    }
    return Future.value(MockActivityDetailPageApiService.mockPageData());
  }

  static PlanActivityData? mockPageData() {
    Map<String, dynamic> jsonDate = {
      "activityId": 2723,
      "activitySubject": "阶梯多奖励多节点",
      "themeCard": {
        "progressHeadColor": "#f5a623",
        "progressTailColor": "#f5a623",
        "backgroundRes": "https://jojopublicfat.jojoread.com/edu/admin/teacher/812028632118493187/1750242717738/bg_medal.zip.flutter"
      },
      "tasks": [
        {
          "taskId": 8255,
          "name": "阶梯任务多节点多奖励",
          "taskType": "STEPWISE",
          "isFinish": 1,
          "finishTime": 1752746763168,
          "isGet": 1,
          "conditions": [
            {
              "currentValue": 2,
              "targetValue": 2,
              "type": 52,
              "lessonIds": [
                "4279",
                "22282",
                "4285",
                "7046",
                "7104",
                "2085",
                "2258",
                "13600",
                "7103",
                "15785",
                "4286",
                "7048",
                "7047",
                "4287",
                "6154",
                "7096",
                "7097",
                "7098",
                "4288",
                "7101",
                "7102",
                "5573",
                "15623",
                "26730",
                "26719",
                "26720",
                "26731",
                "24634",
                "24639",
                "7094",
                "7089",
                "7045",
                "7095",
                "13652",
                "13597",
                "13599",
                "13598",
                "13673",
                "13674",
                "16646"
              ]
            }
          ],
          "rewards": [
            {
              "rewardId": 150,
              "type": 5,
              "typeValue": null,
              "lockImage": "https://oss-fat-common-public.jojoread.com/motivate-edu/766708127542510593.png",
              "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/692689219178257409.png",
              "isGet": 1,
              "getTime": 1752746763426,
              "isPopup": 0,
              "resourceIos": "https://jojopublicfat.jojoread.com/edu/admin/teacher/692689171514186753/1721789968674/meal.zip.ios",
              "resourceAndroid": "https://jojopublicfat.jojoread.com/edu/admin/teacher/692689158360849409/1721789965731/meal.zip.android",
              "resourceFlutter": "https://oss-fat-common-public.jojoread.com/motivate-edu/766708226616165377/1739437485922/meal.zip.flutter",
              "bizId": "2723_8255_150",
              "rewardBizUrl": null
            }
          ],
          "taskExtendResource": {
            "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/edu/admin/teacher/822505259478461441/1752740533855/M_achievements.zip.flutter",
            "mainText": null,
            "subText": null,
            "rewardNodeTexts": null
          }
        },
        {
          "taskId": 8256,
          "name": "阶梯任务多节点多奖励",
          "taskType": "STEPWISE",
          "isFinish": 1,
          "finishTime": 1752746828643,
          "isGet": 0,
          "conditions": [
            {
              "currentValue": 4,
              "targetValue": 4,
              "type": 52,
              "lessonIds": [
                "4279",
                "22282",
                "4285",
                "7046",
                "7104",
                "2085",
                "2258",
                "13600",
                "7103",
                "15785",
                "4286",
                "7048",
                "7047",
                "4287",
                "6154",
                "7096",
                "7097",
                "7098",
                "4288",
                "7101",
                "7102",
                "5573",
                "15623",
                "26730",
                "26719",
                "26720",
                "26731",
                "24634",
                "24639",
                "7094",
                "7089",
                "7045",
                "7095",
                "13652",
                "13597",
                "13599",
                "13598",
                "13673",
                "13674",
                "16646"
              ]
            }
          ],
          "rewards": [
            {
              "rewardId": 2322531195220791298,
              "type": 11,
              "typeValue": "1",
              "lockImage": null,
              "unlockImage": null,
              "isGet": 1,
              "getTime": 1752746828966,
              "isPopup": 0,
              "resourceIos": null,
              "resourceAndroid": null,
              "resourceFlutter": null,
              "bizId": "2723_8256_2322531195220791298",
              "rewardBizUrl": "https://boom.fat.tinman.cn/activity/d4ld_7/uCjxk2Q22"
            },
            {
              "rewardId": 2322531195224985601,
              "type": 12,
              "typeValue": "1",
              "lockImage": null,
              "unlockImage": null,
              "isGet": 0,
              "getTime": 0,
              "isPopup": 0,
              "resourceIos": null,
              "resourceAndroid": null,
              "resourceFlutter": null,
              "bizId": "2723_8256_2322531195224985601",
              "rewardBizUrl": "tinman-router://cn.tinman.jojoread/webview?url=https%3A%2F%2Fmall.fat.tinman.cn%2Faddress%2Fedit%3Fredirect_url%3Dhttps%25253A%25252F%25252Fjojoread.fat.tinman.cn%25252Fmedal%25252FaddOrder%25253FbizType%25253DINCENTIVE%252526userTaskId%25253D2322531195166265345%252526classTagActivityId%25253D2723%252526skuId%25253D14078"
            },
            {
              "rewardId": 2322531195224985602,
              "type": 13,
              "typeValue": "1",
              "lockImage": null,
              "unlockImage": null,
              "isGet": 1,
              "getTime": 1752746828966,
              "isPopup": 0,
              "resourceIos": null,
              "resourceAndroid": null,
              "resourceFlutter": null,
              "bizId": "2723_8256_2322531195224985602",
              "rewardBizUrl": null
            }
          ],
          "taskExtendResource": {
            "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/mock/825406459219661825/1753432234929.zip",
            "mainText": "文档",
            "subText": null,
            "rewardNodeTexts": [
              {
                "rewardType": 11,
                "mainText": "文档",
                "subText": null
              },
              {
                "rewardType": 13,
                "mainText": "俄文",
                "subText": "123123 "
              }
            ]
          }
        },
        {
          "taskId": 8257,
          "name": "阶梯任务多节点多奖励",
          "taskType": "STEPWISE",
          "isFinish": 1,
          "finishTime": 1752746931251,
          "isGet": 1,
          "conditions": [
            {
              "currentValue": 7,
              "targetValue": 7,
              "type": 52,
              "lessonIds": [
                "4279",
                "22282",
                "4285",
                "7046",
                "7104",
                "2085",
                "2258",
                "13600",
                "7103",
                "15785",
                "4286",
                "7048",
                "7047",
                "4287",
                "6154",
                "7096",
                "7097",
                "7098",
                "4288",
                "7101",
                "7102",
                "5573",
                "15623",
                "26730",
                "26719",
                "26720",
                "26731",
                "24634",
                "24639",
                "7094",
                "7089",
                "7045",
                "7095",
                "13652",
                "13597",
                "13599",
                "13598",
                "13673",
                "13674",
                "16646"
              ]
            }
          ],
          "rewards": [
            {
              "rewardId": 2322531195224985603,
              "type": 14,
              "typeValue": "1",
              "lockImage": null,
              "unlockImage": null,
              "isGet": 1,
              "getTime": 1752746931674,
              "isPopup": 0,
              "resourceIos": null,
              "resourceAndroid": null,
              "resourceFlutter": null,
              "bizId": "2723_8257_2322531195224985603",
              "rewardBizUrl": null
            },
            {
              "rewardId": 230,
              "type": 5,
              "typeValue": null,
              "lockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/784091080199298049.png",
              "unlockImage": "https://jojopublicfat.jojoread.com/edu/admin/teacher/784091107567131649.png",
              "isGet": 1,
              "getTime": 1752746931674,
              "isPopup": 0,
              "resourceIos": null,
              "resourceAndroid": null,
              "resourceFlutter": "https://jojopublicfat.jojoread.com/edu/admin/teacher/784086976630676481/1743580899100/meal.zip.flutter",
              "bizId": "2723_8257_230",
              "rewardBizUrl": null
            }
          ],
          "taskExtendResource": {
            "rewardDisplayUrl": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/mock/825406459219661825/1753432234929.zip",
            "mainText": null,
            "subText": null,
            "rewardNodeTexts": null
          }
        }
      ],
      "freePages": [
        {
          "id": 64,
          "pageType": "CUSTOM_PAGE",
          "pageName": "阅读打卡活动",
          "components": [
            {
              "orderNum": 0,
              "componentType": "IMG",
              "imgUrl": "https://oss-fat-common-public.jojoread.com/motivate-edu/816621362543016961.png"
            },
            {
              "orderNum": 1,
              "componentType": "TASK_PROGRESS",
              "topImg": "https://oss-fat-common-public.jojoread.com/motivate-edu/816621390745517057.png",
              "surroundImg": "https://oss-fat-common-public.jojoread.com/motivate-edu/816621422412512257.png",
              "bottomImg": "https://oss-fat-common-public.jojoread.com/motivate-edu/816621439298780161.png"
            },
            {
              "orderNum": 2,
              "componentType": "IMG",
              "imgUrl": "https://oss-fat-common-public.jojoread.com/motivate-edu/816631992784206849.png"
            },
            {
              "orderNum": 3,
              "componentType": "VIDEO",
              "videoUrl": "https://oss-fat-common-public.jojoread.com/motivate-edu/816721641644436481.mp4",
              "videoCoverImg": "https://oss-fat-common-public.jojoread.com/motivate-edu/816621473113259009.png",
              "videoBgImg": "https://oss-fat-common-public.jojoread.com/motivate-edu/816621490473483265.png"
            }
          ]
        }
      ]
    }
    ;
    PlanActivityData test = PlanActivityData.fromJson(jsonDate);
    return test;
  }
}

void main() {
  late ActivityMyGiftCtrl controller;
  late MockActivityDetailPageApiService mockApiService;
  setUp(() {

    mockApiService =
        MockActivityDetailPageApiService(MockActivityDetailPageApiService.mockPageData());
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMockerCalendar());
    controller = ActivityMyGiftCtrl(
      api: mockApiService,
      courseId: 1333,
      pageId: 122,
      activityId: 111,
      subjectColor: null,
      classId: 111,
      buriedString: null,
    );
  });

  group('activity_controller tests:', () {
    WidgetsFlutterBinding.ensureInitialized();

    test('getDetailInfoData test', () async {
      controller.requestSuccess = false;
      await controller.getDetailInfoData();
      controller.requestSuccess = true;
      await controller.getDetailInfoData();
    });

    test('showSuccessPage test', () {
      controller.showSuccessPage();
      expect(controller.state.pageStatus == PageStatus.error , false);
    });

    test('showLoadingPage test', () {
      controller.showLoadingPage();
      expect(controller.state.pageStatus == PageStatus.error , false);
    });

    test('showErrorPage test', () async {
      controller.showErrorPage(Exception("test"), "测试代码");
      expect(controller.state.pageStatus == PageStatus.error , true);
    });

    test('onAllResourcesDownloaded test', () async {
      PlanActivityData data = await mockApiService.getCourseSegmentsInfo(activityId: 111, classId: 222, courseId: 111, pageId: 11);
      PlanActivityDetailData? detailData = PlanActivityDetailData.getMyGiftPageModel(data, 0);
      controller.onAllResourcesDownloaded(detailData!);
    });

    test('isEssentialAnimationResourcesDownloaded test', () async {
      PlanActivityData data = await mockApiService.getCourseSegmentsInfo(activityId: 111, classId: 222, courseId: 111, pageId: 11);
      PlanActivityDetailData? detailData = PlanActivityDetailData.getMyGiftPageModel(data, 0);
      controller.downloadResource(data, null, true);
      expect(controller.state.pageStatus == PageStatus.error , true);
      controller.downloadResource(data, detailData, true);
      expect(controller.state.pageStatus == PageStatus.error , true);
      bool result1 = controller.isEssentialAnimationResourcesDownloaded(detailData!, true);
      expect(result1 == false , true);
    });

    test('requestData test', () async {
      controller = ActivityMyGiftCtrl(
        api: mockApiService,
        courseId: 0,
        pageId: 0,
        activityId: 0,
        subjectColor: null,
        classId: 0,
        buriedString: null,
      );
      controller.callBackFailedListener("logTxt", () => {});
      PlanActivityData? data = await controller.requestData();
      expect(data == null , true);
    });

    test('getDetailInfoData exception test - requestData throws exception', () async {
      // 创建一个会抛出异常的 mock API 服务
      MockActivityDetailPageApiService exceptionApiService =
      MockActivityDetailPageApiService(null, shouldThrowException: true);

      // 创建新的 controller 使用会抛异常的 API
      ActivityMyGiftCtrl exceptionController = ActivityMyGiftCtrl(
        api: exceptionApiService,
        courseId: 1333,
        pageId: 122,
        activityId: 111,
        subjectColor: null,
        classId: 111,
        buriedString: null,
      );

      // 验证初始状态不是错误状态
      expect(exceptionController.state.pageStatus == PageStatus.error, false);

      // 调用 getDetailInfoData，应该会捕获异常并调用 showErrorPage
      await exceptionController.getDetailInfoData();

      // 验证状态变为错误状态
      expect(exceptionController.state.pageStatus == PageStatus.error, true);
      expect(exceptionController.state.exception != null, true);
      expect(exceptionController.state.exception.toString().contains("接口请求失败"), true);
    });
  });
}
