import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/art_material_preview/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/art_material_preview/model/art_material_preview_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/art_material_preview/service/art_material_preview_api.dart';
import 'package:jojo_flutter_plan_pkg/pages/question_bank_list/model/segments_data.dart';

void main() {
  late ArtMaterialPreviewController controller;
  late ArtMaterialPreviewController errorController;

  setUp(() {
    controller = ArtMaterialPreviewController(
        ArtMaterialPreviewMockApi(), '23614_516', 11166, '31222_0128');
    errorController = ArtMaterialPreviewController(
        ArtMaterialPreviewErrorMockApi(), '23614_516', 11166, '31222_0128');
  });

  group('ArtMaterialPreviewController Tests:', () {
    test('requestMaterialPreviewData', () async {
      var data = await controller.requestSegments();
      expect(data, isNotNull);
      data = await errorController.requestSegments();
      expect(data, isNotNull);
    });

    test('requestMaterialPreviewData', () async {
      var data = await controller.requestMaterialPreviewData(11166);
      expect(data, isNotNull);
    });

    test('getMaterialPreviewData', () async {
      var data = await controller.getMaterialPreviewData(11166);
      expect(data, isNotNull);
    });

    test('refreshAllData', () async {
      ArtMaterialPreviewController.withDefault(
        classKey: '23614_516',
        initialLessonKey: '31222_0128',
        initialSegmentId: 11166,
      );
      controller.currentSegmentId = 11166;
      await controller.refreshAllData(
          segmentId: 11166, lessonKey: '31222_0128');
      print('refreshAllData ${controller.materialPreviewData}');
      print('refreshAllData ${controller.state.lessonKey}');
      expect(controller.materialPreviewData[11166], isNotNull);
      expect(controller.state.lessonKey, '31222_0128');
    });

    test('preRequest', () async {
      controller.currentPageIndex = 1;
      await controller.getSegments();
      await controller.preRequest();
      expect(controller.materialPreviewData[11166], isNotNull);
      expect(controller.materialPreviewData[4987], isNotNull);
    });

    test('childRefreshData', () async {
      controller.currentPageIndex = 4;
      controller.currentSegmentId = 2657;
      await controller.childRefreshData(2657);
      expect(controller.materialPreviewData[2657], isNotNull);
    });

    test('childRefreshData1', () async {
      controller.currentPageIndex = 4;
      controller.currentSegmentId = 2657;
      await controller.childRefreshData(2657);
      await controller.childRefreshData(2657);
      expect(controller.materialPreviewData[2657], isNotNull);
    });

    test('getSegmentIdByIndex', () async {
      await controller.getSegments();
      int id = controller.getSegmentIdByIndex(1);
      expect(id, 2656);
    });

    test('initialRefreshData', () async {
      controller.currentSegmentId = controller.initialSegmentId;
      await controller.initialRefreshData();
      expect(controller.materialPreviewData[controller.initialSegmentId],
          isNotNull);
      expect(controller.state.lessonKey, controller.initialLessonKey);
    });

    test('errorRefreshData', () async {
      controller.currentSegmentId = 2657;
      await controller.errorRefreshData();
      expect(controller.materialPreviewData[2657], isNotNull);
      expect(controller.state.lessonKey, isNull);
    });

    test('changeToPage', () async {
      controller.currentSegmentId = controller.initialSegmentId;
      await controller.initialRefreshData();
      await controller.changeToPage(1, needSensor: false, needJump: false);
      expect(controller.materialPreviewData[2656], isNotNull);
      expect(controller.state.lessonKey, isNull);
    });

    test('dispose', () async {
      controller.currentSegmentId = controller.initialSegmentId;
      await controller.initialRefreshData();
      controller.dispose();
      expect(controller.initialSegmentData, isNull);
    });
  });

  group('ArtMaterialPreviewController Error Tests:', () {
    test('requestSegments', () async {
      await errorController.requestSegments();
      expect(errorController.state.exception, isNotNull);
    });

    test('requestMaterialPreviewData', () async {
      await errorController.requestMaterialPreviewData(11166);
      expect(errorController.state.exception, isNotNull);
    });
  });
}

class ArtMaterialPreviewMockApi implements ArtMaterialPreviewApi {
  @override
  Future<SegmentsData> getSegments(String classKey, String sceneType) {
    return Future.value(SegmentsData.fromJson(json.decode(
        '{"modeType":1,"itemList":[{"type":"SEGMENT","segmentInfo":{"segmentId":11166,"segmentKey":"23614_14","segmentOrderDes":"主题1","segmentOrder":1,"segmentName":"鸿蒙整体流程回归","segmentDescription":"测试主题1","labelName":"","labelCode":0,"resource":{"coverImage":"","selectedImage":"","segmentDimensionResource":{"screenVideo":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/826492283453558785/1753691123167b5b87bf9590ca8d260ac4d7e9e2a75a2.mp4","videoAdaptType":1,"screenVideoRepeat":1,"screenVideoStyle":1},"artBgImgResource":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/703296580892833793/172431897162100ca830b216a4a8bfa1a285922b81001.jpeg","artMiddleBgImgResource":"","artFrontBgImgResource":"","artNpc":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/642419407785156609/1709804722375eb33c3b666bc6fe115567cff646b8787.svga","backgroundColor":"","highlightColor":"","backgroundImage":"","introVideo":"","bgm":"","tipAudio":""}},"extra":{"finishLessonCount":0,"unlockLessonCount":6,"totalLessonCount":6,"needMakeupLessonCount":6}},{"type":"SEGMENT","segmentInfo":{"segmentId":2656,"segmentKey":"23614_01","segmentOrderDes":"主题2","segmentOrder":2,"segmentName":"一月测试测试2334","segmentDescription":"12311","labelName":"","labelCode":0,"resource":{"coverImage":"","selectedImage":"","segmentDimensionResource":{"screenVideo":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/388643944836543488/164929993686494ee1d315c1ab5f910e1685b1a9d3ad3.%29.mp4","videoAdaptType":2,"screenVideoRepeat":1,"screenVideoStyle":1},"artBgImgResource":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/373109125998309376/1645596147998d3c17f6160afe5b6926193ebdbd41a2f.png","artMiddleBgImgResource":"","artFrontBgImgResource":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/370978276767032320/16450883950731ebea05a0efa5ca81e0fd06e37489408.png","artNpc":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/642419407785156609/1709804722375eb33c3b666bc6fe115567cff646b8787.svga","backgroundColor":"","highlightColor":"","backgroundImage":"","introVideo":"","bgm":"","tipAudio":""}},"extra":{"finishLessonCount":0,"unlockLessonCount":14,"totalLessonCount":14,"needMakeupLessonCount":14}},{"type":"SEGMENT","segmentInfo":{"segmentId":4987,"segmentKey":"23614_09","segmentOrderDes":"主题3","segmentOrder":3,"segmentName":"综合线","segmentDescription":"啦啦啦","labelName":"","labelCode":0,"resource":{"coverImage":"","selectedImage":"","segmentDimensionResource":{"screenVideo":"","videoAdaptType":0,"screenVideoRepeat":1,"screenVideoStyle":1},"artBgImgResource":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/373109125998309376/16455962290014892d1b335cef8bc26d23d1f97e18f71.png","artMiddleBgImgResource":"","artFrontBgImgResource":"","artNpc":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/642419407785156609/1709804722375eb33c3b666bc6fe115567cff646b8787.svga","backgroundColor":"","highlightColor":"","backgroundImage":"","introVideo":"","bgm":"","tipAudio":""}},"extra":{"finishLessonCount":0,"unlockLessonCount":13,"totalLessonCount":13,"needMakeupLessonCount":13}},{"type":"SEGMENT","segmentInfo":{"segmentId":2657,"segmentKey":"23614_02","segmentOrderDes":"主题4","segmentOrder":4,"segmentName":"二月","segmentDescription":"234","labelName":"","labelCode":0,"resource":{"coverImage":"","selectedImage":"","segmentDimensionResource":{"screenVideo":"","videoAdaptType":0,"screenVideoRepeat":1,"screenVideoStyle":1},"artBgImgResource":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/370978276767032320/16450883950731ebea05a0efa5ca81e0fd06e37489408.png","artMiddleBgImgResource":"","artFrontBgImgResource":"","artNpc":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/642419407785156609/1709804722375eb33c3b666bc6fe115567cff646b8787.svga","backgroundColor":"","highlightColor":"","backgroundImage":"","introVideo":"","bgm":"","tipAudio":""}},"extra":{"finishLessonCount":0,"unlockLessonCount":8,"totalLessonCount":8,"needMakeupLessonCount":8}},{"type":"SEGMENT","segmentInfo":{"segmentId":2678,"segmentKey":"23614_06","segmentOrderDes":"主题8","segmentOrder":8,"segmentName":"六月","segmentDescription":"6789","labelName":"","labelCode":0,"resource":{"coverImage":"","selectedImage":"","artBgImgResource":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/373109125998309376/1645596147998d3c17f6160afe5b6926193ebdbd41a2f.png","artMiddleBgImgResource":"","artFrontBgImgResource":"","artNpc":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/642419407785156609/1709804722375eb33c3b666bc6fe115567cff646b8787.svga","backgroundColor":"","highlightColor":"","backgroundImage":"","introVideo":"","bgm":"","tipAudio":""}},"extra":{"finishLessonCount":0,"unlockLessonCount":5,"totalLessonCount":5,"needMakeupLessonCount":5}},{"type":"SEGMENT","segmentInfo":{"segmentId":5959,"segmentKey":"23614_11","segmentOrderDes":"主题11","segmentOrder":11,"segmentName":"九月","segmentDescription":"九月","labelName":"","labelCode":0,"resource":{"coverImage":"","selectedImage":"","segmentDimensionResource":{"screenVideo":"","videoAdaptType":0,"screenVideoRepeat":1,"screenVideoStyle":1},"artBgImgResource":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/642419383072317441/17098047163922d5782edf8f3d2b34dce32398b51e565.png","artMiddleBgImgResource":"","artFrontBgImgResource":"","artNpc":"https://jojopublicfat.jojoread.com/cc/cc-admin/course/642419407785156609/1709804722375eb33c3b666bc6fe115567cff646b8787.svga","backgroundColor":"","highlightColor":"","backgroundImage":"","introVideo":"","bgm":"","tipAudio":""}},"extra":{"finishLessonCount":0,"unlockLessonCount":12,"totalLessonCount":12,"needMakeupLessonCount":12}}]  }')));
  }

  @override
  Future<ArtMaterialPreviewListModel> getStudyPrepareResources(
      String classKey, String segmentIds) {
    return Future.value(ArtMaterialPreviewListModel.fromJson(json.decode('''
      {
          "lessonResList": [
            {
              "classId": 48527,
              "segmentId": 5959,
              "lessonId": 24878,
              "lessonKey": "31222_0128",
              "lessonName": "获取卡牌副本4",
              "lessonOrderDesc": "第78次",
              "lessonPreparationImage": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/636222513128001537/1708327267682310eaf2165838476e4a34541d0b1ffc5.png",
              "lessonPreparationAudio": ""
            },
            {
              "classId": 48527,
              "segmentId": 5959,
              "lessonId": 24879,
              "lessonKey": "31222_0129",
              "lessonName": "获取卡牌副本5",
              "lessonOrderDesc": "第79次",
              "lessonPreparationImage": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/636222513065086977/170832726765167204490517cd834bd0f4e015c7e1e68.png",
              "lessonPreparationAudio": ""
            },
            {
              "classId": 48527,
              "segmentId": 5959,
              "lessonId": 24880,
              "lessonKey": "31222_0130",
              "lessonName": "获取卡牌副本6",
              "lessonOrderDesc": "第80次",
              "lessonPreparationImage": "https://jojopublicfat.jojoread.com/cc/cc-admin/course/636222512914092033/1708327267671f885cd13a32bed45c3c0fdee885d00c4.png",
              "lessonPreparationAudio": ""
            }
          ]
        }
        ''')));
  }
}

class ArtMaterialPreviewErrorMockApi implements ArtMaterialPreviewApi {
  @override
  Future<SegmentsData> getSegments(String classKey, String sceneType) {
    throw Exception("getSegments error");
  }

  @override
  Future<ArtMaterialPreviewListModel> getStudyPrepareResources(
      String classKey, String segmentIds) {
    throw Exception("getStudyPrepareResources error");
  }
}
