import 'dart:convert';

import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/mobile_for_token/jojo_user.dart';
import 'package:jojo_flutter_base/models/graiy_info.dart';
import 'package:jojo_flutter_base/models/nuwa_config_data.dart';
import 'package:jojo_flutter_base/models/baby_grade.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading.dart';

import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/baby_profile_state.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/model/mine_date.dart';
import 'package:jojo_flutter_plan_pkg/pages/baby_profile/model/baby_profile_gray_data.dart';
import 'package:jojo_flutter_plan_pkg/service/baby_info_api.dart';
import 'package:mockito/mockito.dart';

import '../plan_pure_enjoy/mocker.dart';

// 使用本地 Fake 实现替代 Mockito
class FakeBabyInfoApi extends Mock implements BabyInfoApi {
  late Future<WrapMineDataInfo> Function() getMineDataInfoImpl;
  late Future<MineGradeInfo> Function() getLoginSummaryInfoImpl;
  Future<Map<String, ConfigData>> Function(String)? getNuwaBabyConfigImpl;
  Future<BabyProfileGrayData> Function(int)? getBabyProfileGrayDataImpl;
  bool gradeNull = false;

  FakeBabyInfoApi({
    required this.getMineDataInfoImpl,
    required this.getLoginSummaryInfoImpl,
  });

  @override
  Future<WrapMineDataInfo> getMineDataInfo() => getMineDataInfoImpl();

  @override
  Future<MineGradeInfo> getLoginSummaryInfo() => getLoginSummaryInfoImpl();

  @override
  Future<Map<String, ConfigData>> getNuwaBabyConfig(String configKey) {
    if(getNuwaBabyConfigImpl != null){
      return getNuwaBabyConfigImpl!(configKey);
    }
    // 模拟后端返回 BABYINFGRAY 配置数据
    final configData = ConfigData(
      configKey: configKey,
      configValue: {
        "defaultAvatarList": [
          {
            "ossUrl":
                base64Encode(utf8.encode("https://example.com/avatar1.png")),
            "httpUrl": "https://example.com/avatar1.png"
          },
          {
            "ossUrl":
                base64Encode(utf8.encode("https://example.com/avatar2.png")),
            "httpUrl": "https://example.com/avatar2.png"
          }
        ]
      },
    );

    return Future.value({
      configKey: configData,
    });
  }

  @override
  Future<BabyGradeInfo> getBabyGradeInfo() {
    if(gradeNull == true){
      throw Exception("GradeDictList is null");
    }
    return Future(() => BabyGradeInfo(gradeDictList: [
          GradeInfo(),
        ]));
  }

  @override
  Future<BabyProfileGrayData> getBabyProfileGrayData(int scene) async{
    if (getBabyProfileGrayDataImpl != null) {
      return getBabyProfileGrayDataImpl!(scene);
    }
    return Future.value(null);
  }
// 其他方法根据需要补充
}

void main() {
  group('Test BabyInfoEidtController nickname selection logic', () {
    late BabyProfileController controller;

    TestWidgetsFlutterBinding.ensureInitialized();
    JoJoNativeBridge.registerMocker(PureEnjoyTestJoJoBridgeCommonMocker());

    // Helper 方法：构造 WrapMineDataInfo 模拟数据
    Future<WrapMineDataInfo> _wrapMineDataInfo({
      String nickname = "Default Nickname",
      bool babyNickNameInReview = false,
      String? reviewingNickName = "Reviewing NickName",
    }) {
      return Future(() => WrapMineDataInfo(
            userPageInfo: MineDataInfo(
              babyInfo: MineBabyInfo(
                nickname: nickname,
                sex: 1,
                // 1 表示男，0 表示女
                avatarUrl: "https://example.com/avatar.png",
                birthday: "2020-01-01",
                grade: 2,
                userId: 1234567890,
                location: "Beijing",
                family: "FamilyName",
                registerTime: "2023-01-01T12:00:00Z",
                userPoints: 1000,
                userPointsLink: "https://points.example.com",
                reviewingBabyNickname: reviewingNickName,
                babyNickNameInReview: babyNickNameInReview,
              ),
            ),
          ));
    }

    setUp(() {
      final fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => _wrapMineDataInfo(),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: BabyInfo())),
      );

      controller = BabyProfileController(
          BabyProfileState(pageStatus: PageStatus.loading),
          fakeService,
          fakeService,
          fakeService);
    });

    tearDown(() {
      // controller.close();
    });

    test("", () async {
      FakeBabyInfoApi fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => _wrapMineDataInfo(
          babyNickNameInReview: true,
          reviewingNickName: "Reviewing NickName",
        ),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: null)),
      );
      controller = BabyProfileController(
          BabyProfileState(pageStatus: PageStatus.loading),
          fakeService,
          fakeService,
          fakeService);
      await controller.refreshBabyProfileData(true);
      fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => _wrapMineDataInfo(
          babyNickNameInReview: true,
          reviewingNickName: "Reviewing NickName",
        ),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: null)),
      );
      fakeService.getNuwaBabyConfigImpl = (String configKey) {
        return Future.value({
          configKey: ConfigData(
              configKey: configKey,
              configValue: {

              }
          )
        });
      };
      await controller.refreshBabyProfileData(true);
      fakeService.gradeNull = true;
      await controller.refreshBabyProfileData(true);
      fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => _wrapMineDataInfo(
          babyNickNameInReview: true,
          reviewingNickName: "Reviewing NickName",
        ),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: BabyInfo())),
      );
      controller = BabyProfileController(
          BabyProfileState(pageStatus: PageStatus.loading),
          fakeService,
          fakeService,
          fakeService);
      fakeService.gradeNull = true;
      await controller.refreshBabyProfileData(true);
    });

    test(
        'When babyNickNameInReview is true and reviewingNickName not empty, should use reviewingNickName',
        () async {
      final fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => _wrapMineDataInfo(
          babyNickNameInReview: true,
          reviewingNickName: "Reviewing NickName",
        ),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: BabyInfo())),
      );

          controller = BabyProfileController(
              BabyProfileState(pageStatus: PageStatus.loading),
              fakeService,
              fakeService,
              fakeService);
          await controller.refreshBabyProfileData(true);
          final babyMap = await controller.getBabyInfo()??{};
          expect(babyMap['nickname'], equals("Reviewing NickName"));
        });

    test('When babyNickNameInReview is false, should use default nickname',
        () async {
      final fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => _wrapMineDataInfo(
          babyNickNameInReview: false,
          nickname: "Default Nickname",
        ),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: BabyInfo())),
      );

      controller = BabyProfileController(
          BabyProfileState(pageStatus: PageStatus.loading),
          fakeService,
          fakeService,
          fakeService);

          final babyMap = await controller.getBabyInfo()??{};
          expect(babyMap['nickname'], equals("Default Nickname"));
        });

    test(
        'When babyNickNameInReview is true but reviewingNickName empty, fallback to default nickname',
        () async {
      final fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => _wrapMineDataInfo(
          babyNickNameInReview: false,
          reviewingNickName: "",
        ),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: BabyInfo())),
      );

      controller = BabyProfileController(
          BabyProfileState(pageStatus: PageStatus.loading),
          fakeService,
          fakeService,
          fakeService);

          final babyMap = await controller.getBabyInfo()??{};
          expect(babyMap['nickname'], equals("Default Nickname"));
        });

    test(
        'When babyNickNameInReview is true but reviewingNickName null, fallback to default nickname',
        () async {
      final fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => _wrapMineDataInfo(
          babyNickNameInReview: true,
          reviewingNickName: "checkNickName",
        ),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: BabyInfo())),
      );

      controller = BabyProfileController(
          BabyProfileState(pageStatus: PageStatus.loading),
          fakeService,
          fakeService,
          fakeService);

          final babyMap = await controller.getBabyInfo()??{};
          expect(babyMap['nickname'], equals("checkNickName"));
        });

    test(
        'When babyNickNameInReview is true but reviewingNickName null, fallback to default nickname',
            () async {
          final fakeService = FakeBabyInfoApi(
            getMineDataInfoImpl: () => throw Exception("test"),
            getLoginSummaryInfoImpl: () =>
                Future.value(MineGradeInfo(babyInfo: BabyInfo())),
          );

          controller = BabyProfileController(
              BabyProfileState(pageStatus: PageStatus.loading),
              fakeService,
              fakeService,
              fakeService);

          final babyMap = await controller.getBabyInfo();
          expect(babyMap, null);
        });

    test('When wrapMineDataInfo is empty object, should return empty map',
        () async {
      final fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => Future.value(WrapMineDataInfo()),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: BabyInfo())),
      );

      controller = BabyProfileController(
          BabyProfileState(pageStatus: PageStatus.loading),
          fakeService,
          fakeService,
          fakeService);

      final babyMap = await controller.getBabyInfo();
      expect(babyMap, isNotEmpty);
    });
  });

  group('getBabyProfileGrayData tests', () {
    late BabyProfileController controller;
    late FakeBabyInfoApi fakeService;

    setUp(() {
      fakeService = FakeBabyInfoApi(
        getMineDataInfoImpl: () => Future.value(WrapMineDataInfo()),
        getLoginSummaryInfoImpl: () =>
            Future.value(MineGradeInfo(babyInfo: BabyInfo())),
      );

      controller = BabyProfileController(
        BabyProfileState(pageStatus: PageStatus.loading),
        fakeService,
        fakeService,
        fakeService,
      );
    });

    test('getBabyProfileGrayData should return data when API succeeds', () async {
      // Arrange
      final expectedData = BabyProfileGrayData(
        babyProfileStyle: 1,
        showBabyProfile: true,
        babyProfileStyleGray: null,
      );

      fakeService.getBabyProfileGrayDataImpl = (int scene) {
        return Future.value(expectedData);
      };

      // Act
      final result = await controller.getBabyProfileGrayData();

      // Assert
      expect(result, isNotNull);
      expect(result?.babyProfileStyle, equals(1));
      expect(result?.showBabyProfile, isTrue);
      expect(result?.babyProfileStyleGray, isNull);
    });

    test('getBabyProfileGrayData should handle all fields properly', () async {
      // Arrange
      final grayInfo = GrayInfo(

      );

      final expectedData = BabyProfileGrayData(
        babyProfileStyle: 2,
        showBabyProfile: false,
        babyProfileStyleGray: grayInfo,
      );

      fakeService.getBabyProfileGrayDataImpl = (int scene) {
        return Future.value(expectedData);
      };

      // Act
      final result = await controller.getBabyProfileGrayData();

      // Assert
      expect(result, isNotNull);
      expect(result?.babyProfileStyle, equals(2));
      expect(result?.showBabyProfile, isFalse);
      expect(result?.babyProfileStyleGray, isNotNull);
    });

    test('getBabyProfileGrayData should return null when API fails', () async {
      // Arrange
      fakeService.getBabyProfileGrayDataImpl = (int scene) {
        throw Exception('Network error');
      };

      // Act
      final result = await controller.getBabyProfileGrayData();

      // Assert
      expect(result, isNull);
    });

    test('getBabyProfileGrayData should return null when no data', () async {
      // Arrange
      fakeService.getBabyProfileGrayDataImpl = (int scene) {
        return Future.value(null);
      };

      // Act
      final result = await controller.getBabyProfileGrayData();

      // Assert
      expect(result, isNull);
    });
  });
}
