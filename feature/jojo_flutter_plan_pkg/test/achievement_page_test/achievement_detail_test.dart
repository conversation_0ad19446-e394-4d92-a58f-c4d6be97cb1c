import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/common/bridge/mock_brige.dart';
import 'package:jojo_flutter_plan_pkg/pages/achievements/achievement_detail/controller.dart';

void main() {
  late AchievementDetailController controller;

  setUp(() {
    JoJoNativeBridge.registerMocker(JoJoBridgeCommonMocker());
    controller = AchievementDetailController.withDefault(medalId: 1);
  });

    group('fetchUserInfo Tests', () {
      test(
          'userinfo shuld be not null when API call is successful',
          () async {
    
        await controller.fetchUserInfo();
        expect(controller.userInfo, isNotNull);
      });
    });
}
