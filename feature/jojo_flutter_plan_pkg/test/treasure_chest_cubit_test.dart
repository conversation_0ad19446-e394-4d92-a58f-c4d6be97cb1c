import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/finish_course_settle_accounts/continue_learn/widget/finish_course_enum.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_cubit.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_state.dart';

void main() {
  group('TreasureChestCubit', () {
    late TreasureChestCubit cubit;

    setUp(() {
      cubit = TreasureChestCubit();
    });

    tearDown(() {
      cubit.close();
    });

    // 测试 switchSpine 方法
    blocTest<TreasureChestCubit, TreasureChestState>(
      'should emit state with updated spineName when switchSpine is called',
      build: () => cubit,
      act: (cubit) => cubit.switchSpine(BoxSpineNames.boxOpen),
      expect: () => [
        const TreasureChestState(spineName: BoxSpineNames.boxOpen, isOpen: false),
      ],
    );

    // 测试 openTreasureChest 方法
    blocTest<TreasureChestCubit, TreasureChestState>(
      'should emit state with isOpen set to true when openTreasureChest is called',
      build: () => cubit,
      act: (cubit) => cubit.openTreasureChest(),
      expect: () => [
        TreasureChestState(spineName: cubit.state.spineName, isOpen: true),
      ],
    );

    // 测试 convertNumToList 方法
    group('convertNumToList', () {
      test('should convert number to list with "x" prefix', () {
        final result = cubit.convertNumToList(123);
        expect(result, equals(['x', '1', '2', '3']));
      });

      test('should handle single digit input', () {
        final result = cubit.convertNumToList(5);
        expect(result, equals(['x', '5']));
      });

      test('should cap input at 999', () {
        final result = cubit.convertNumToList(1000);
        expect(result, equals(['x', '9', '9', '9']));
      });

      test('should handle zero input', () {
        final result = cubit.convertNumToList(0);
        expect(result, equals(['x', '0']));
      });

      test('should throw FormatException for negative input', () {
        expect(
              () => cubit.convertNumToList(-1),
          throwsA(isA<FormatException>().having(
                (e) => e.message,
            'message',
            '输入必须是非负整数',
          )),
        );
      });
    });
  });
}