// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/model/lesson_change_data.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/model/lesson_change_result.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_lesson_change/model/lesson_change_result_query.dart';
import 'package:jojo_flutter_plan_pkg/service/home_lesson_change_api.dart';
import 'package:mockito/mockito.dart';

// 创建 Dio 的 Mock 类
class MockChangeHomePageApiService extends Mock implements HomeLessonChangeApi {
  final LessonChangeData _courseHomePageData;

  MockChangeHomePageApiService(this._courseHomePageData);

  static LessonChangeData? mockSkuData(){
    Map<String, dynamic> jsonDate =  {
      "orderNo": "812735305619223553",
      "ownSkuId": 13424,
      "ownSkuName": "cheng美育赠课-勿动",
      "ownGoodsCode": "6777",
      "ownCourseId": 6777,
      "gift": true,
      "belongSkuId": 13422,
      "skuList": [{
        "skuId": 22162,
        "skuName": "朱堯美术课测试课程新建1",
        "goodsCode": "949",
        "courseId": 949,
        "courseKey": "21880",
        "own": false,
        "change": false,
        "selected": false
      }, {
        "skuId": 22332,
        "skuName": "易贤霖的单课",
        "goodsCode": "377",
        "courseId": 377,
        "courseKey": "21314",
        "own": false,
        "change": false,
        "selected": false
      }, {
        "skuId": 22280,
        "skuName": "cheng美育赠课-勿动",
        "goodsCode": "6777",
        "courseId": 6777,
        "courseKey": "50162",
        "own": true,
        "change": false,
        "selected": true
      }],
      "title": "全部内容"
    };
    LessonChangeData test = LessonChangeData.fromJson(jsonDate);
    return test;
  }

  static LessonChangeData? mockHomePageData() {
    Map<String, dynamic> jsonDate = {
      "list": [
        {
          "segmentName": "路径优化",
          "segmentId": 757,
          "lessonInfos": [
            {
              "lessonName": "第3001次 学习卡",
              "lessonId": 3493,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/241277672374321152/f0fb5dabe0db8e38faa687848c5466401614168558202.jpg",
              "lessonOrder": 3001
            },
            {
              "lessonName": "第3001次 学习流程优化-无游戏环节",
              "lessonId": 3518,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/245989483715687424/d4ea09125f36dd2d52500981b7f814141615288463307.jpg",
              "lessonOrder": 3001
            }
          ]
        },
        {
          "segmentName": "学习卡2",
          "segmentId": 758,
          "lessonInfos": [
            {
              "lessonName": "第4001次 学习卡",
              "lessonId": 3521,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/246688855977882624/ad37450af6da09effa91b87157f695321615455345382.jpg",
              "lessonOrder": 4001
            },
            {
              "lessonName": "第4001次 学习卡",
              "lessonId": 3494,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/241277672374321152/e4eedbcd92a14c9132e1d6fc9fc409c31614168594329.jpg",
              "lessonOrder": 4001
            },
            {
              "lessonName": "第4002次 字卡验证",
              "lessonId": 3522,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/246688855977882624/f0fb5dabe0db8e38faa687848c5466401615455379761.jpg",
              "lessonOrder": 4002
            },
            {
              "lessonName": "第4003次 type21支持学习卡",
              "lessonId": 3626,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/253584165391670272/e4eedbcd92a14c9132e1d6fc9fc409c31617099175146.jpg",
              "lessonOrder": 4003
            },
            {
              "lessonName": "第4004次 字卡升级",
              "lessonId": 3898,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273462923833746432/f9c8d46af34fa329c219c73ed1fa23d71621838753528.png",
              "lessonOrder": 4004
            }
          ]
        },
        {
          "segmentName": "UGC风控",
          "segmentId": 759,
          "lessonInfos": [
            {
              "lessonName": "第2001次 狐假虎威",
              "lessonId": 689,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259157168218112.png",
              "lessonOrder": 2001
            },
            {
              "lessonName": "第2001次 学习卡",
              "lessonId": 3495,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/241292778025959424/319b4e1197dd11b21181ff763ae52e9e1614168680698.jpg",
              "lessonOrder": 2001
            },
            {
              "lessonName": "第2002次 画鸡内容副标题画鸡内容副标题画鸡内容副标题画鸡内容副标题",
              "lessonId": 690,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259484508479488.png",
              "lessonOrder": 2002
            },
            {
              "lessonName": "第2003次 曾子杀猪",
              "lessonId": 691,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259850969014272.png",
              "lessonOrder": 2003
            },
            {
              "lessonName": "第2004次 曹冲称象曹冲称象曹冲称象曹冲称象曹冲称象",
              "lessonId": 692,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259126050676736.png",
              "lessonOrder": 2004
            },
            {
              "lessonName": "第2005次 狐假虎威副本1",
              "lessonId": 3531,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259157168218112.png",
              "lessonOrder": 2005
            },
            {
              "lessonName": "第2006次 画鸡副本1",
              "lessonId": 3532,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259484508479488.png",
              "lessonOrder": 2006
            },
            {
              "lessonName": "第2007次 曾子杀猪副本1",
              "lessonId": 3533,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259850969014272.png",
              "lessonOrder": 2007
            },
            {
              "lessonName": "第2008次 曹冲称象副本1",
              "lessonId": 3534,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259126050676736.png",
              "lessonOrder": 2008
            },
            {
              "lessonName": "第2009次 名师视频课副本1",
              "lessonId": 3535,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259964122947584.png",
              "lessonOrder": 2009
            },
            {
              "lessonName": "第2010次 名师视频课",
              "lessonId": 693,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/162259964122947584.png",
              "lessonOrder": 2010
            }
          ]
        },
        {
          "segmentName": "学习卡4",
          "segmentId": 760,
          "lessonInfos": [
            {
              "lessonName": "第5001次 趣拍摄",
              "lessonId": 3753,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/267242979558940672/d4ea09125f36dd2d52500981b7f814141620356849353.jpg",
              "lessonOrder": 5001
            },
            {
              "lessonName": "第5001次 学习卡",
              "lessonId": 3497,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/241292778025959424/ad37450af6da09effa91b87157f695321614168769591.jpg",
              "lessonOrder": 5001
            },
            {
              "lessonName": "第5002次 上传作业、视频",
              "lessonId": 3751,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/267242979558940672/f0fb5dabe0db8e38faa687848c5466401620355690884.jpg",
              "lessonOrder": 5002
            },
            {
              "lessonName": "第5003次 验证不同课程相同字卡",
              "lessonId": 5054,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/283599208749147136/5c076c3f7d613bb7177d413d03cefa1f1624255320095.png",
              "lessonOrder": 5003
            }
          ]
        },
        {
          "segmentName": "学习卡5",
          "segmentId": 761,
          "lessonInfos": [
            {
              "lessonName": "第6001次 周一收集子卡",
              "lessonId": 3875,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273413253270971392/4919575fab5b6cdd5867042b11856cfc1621826799438.jpg",
              "lessonOrder": 6001
            },
            {
              "lessonName": "第6001次 学习卡4",
              "lessonId": 3496,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/241292778025959424/d4ea09125f36dd2d52500981b7f814141614168719508.jpg",
              "lessonOrder": 6001
            },
            {
              "lessonName": "第6002次 周二收集字卡",
              "lessonId": 3905,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273480735033762816/f0fb5dabe0db8e38faa687848c5466401621842887407.jpg",
              "lessonOrder": 6002
            },
            {
              "lessonName": "第6003次 周三收集字卡",
              "lessonId": 3906,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273480735033762816/f0fb5dabe0db8e38faa687848c5466401621842923435.jpg",
              "lessonOrder": 6003
            },
            {
              "lessonName": "第6004次 周四收集字卡",
              "lessonId": 3907,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273480735033762816/e4eedbcd92a14c9132e1d6fc9fc409c31621842961085.jpg",
              "lessonOrder": 6004
            },
            {
              "lessonName": "第6005次 周五升级字卡",
              "lessonId": 3908,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/273480735033762816/e4eedbcd92a14c9132e1d6fc9fc409c31621842986934.jpg",
              "lessonOrder": 6005
            },
            {
              "lessonName": "第6006次 L2字卡—周一",
              "lessonId": 3925,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/274210702864352256/e4eedbcd92a14c9132e1d6fc9fc409c31622017983683.jpg",
              "lessonOrder": 6006
            },
            {
              "lessonName": "第6007次 周二—L2字卡",
              "lessonId": 3926,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/274210702864352256/f0fb5dabe0db8e38faa687848c5466401622018051214.jpg",
              "lessonOrder": 6007
            },
            {
              "lessonName": "第6008次 升级字卡L2",
              "lessonId": 3927,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/274210702864352256/c0d092888983f2579c3d801339773a761622018084373.jpg",
              "lessonOrder": 6008
            },
            {
              "lessonName": "第6009次 学习卡1",
              "lessonId": 3928,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/274210702864352256/4919575fab5b6cdd5867042b11856cfc1622018128828.jpg",
              "lessonOrder": 6009
            },
            {
              "lessonName": "第6010次 学习卡升级",
              "lessonId": 3929,
              "icon":
                  "https://jojopublicfat.jojoread.com/cc/cc-admin/course/274210702864352256/c0d092888983f2579c3d801339773a761622018158709.jpg",
              "lessonOrder": 6010
            }
          ]
        }
      ]
    };
    LessonChangeData test = LessonChangeData.fromJson(jsonDate);
    return test;
  }

  static LessonChangeResult mockLessonChangeResult() {
    Map<String, dynamic> jsonDate = {
      "code": "SUCCESS",
      "message": "ok",
      "subCode": null,
      "subMessage": null,
      "data": {
        "success": false,
        "orderNo": null,
        "courseId": null,
        "contactRoute": null
      },
      "action": null,
      "actionData": null
    };
    LessonChangeResult test = LessonChangeResult.fromJson(jsonDate);
    return test;
  }

  @override
  Future<LessonChangeData?> getUserSkus(int? courseId, int? classId) {
    return Future.value(mockSkuData());
  }
}

void main() {
  late MockChangeHomePageApiService mockApiService;
  setUp(() {
    mockApiService = MockChangeHomePageApiService(
        MockChangeHomePageApiService.mockHomePageData()!);
  });

  group('plan_lesson_controller tests:', () {
    WidgetsFlutterBinding.ensureInitialized();
    // 设置mock对象的行为
    test("onRefresh", () async {
      var controller =
          ChangeController(api: mockApiService, classId: 1212, courseId: 12312);
      await controller.onRefresh();
      expect(controller.state.currentIndex == 2, true);
    });

    test("lessonChange", () async {
      var controller =
          ChangeController(api: mockApiService, classId: 1212, courseId: 12312);
      await controller.lessonChange("", 1, 23, 3, false, 13, "测试", null);
    });

    test("retryRequest", () async {
      var controller =
          ChangeController(api: mockApiService, classId: 1212, courseId: 12312);
      Future<LessonChangeResultQuery> _changeResult() async {
        var query = const LessonChangeResultQuery(simpleCourseCardList: [
          SimpleCourseCardList(
              classId: 1111,
              courseId: 111,
              courseKey: "12123",
              classKey: "12312")
        ]);
        return query;
      }

      var result = await controller.retryRequest(_changeResult, null, null);
      expect(result?.simpleCourseCardList?[0]?.classKey, "12312");
    });
  });
}
