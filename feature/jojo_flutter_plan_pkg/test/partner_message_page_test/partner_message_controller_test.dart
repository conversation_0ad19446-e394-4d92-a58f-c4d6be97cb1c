import 'package:flutter_test/flutter_test.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/partner_message/model/partner_message_data.dart';
import 'package:mockito/mockito.dart';
import 'package:jojo_flutter_plan_pkg/service/partner_apply_api.dart';
import 'package:jojo_flutter_plan_pkg/service/partner_message_api.dart';

class MockPartnerApplyApi extends Mock implements PartnerApplyApi {
  @override
  Future<dynamic> handleApply(int id, Map<String, dynamic> map) async {
    if (id == 200) {
      return {'code': 200};
    } else {
      return null;
    }
  }
}

void main() {
  late PartnerMessageApi mockMessageApi;
  late MockPartnerApplyApi mockApplyApi;
  late PartnerMessageController controller;

  setUp(() {
    mockMessageApi = PartnerMessageApiMock();
    mockApplyApi = MockPartnerApplyApi();
    controller = PartnerMessageController(
        messageApi: mockMessageApi, applyApi: mockApplyApi);
  });

  group('refreshMessages', () {
    test(
        'should update state with messages and set refreshController when API call is successful',
        () async {
      controller.refreshMessages();
    });
    test(
        'should update state with messages and set refreshController when API call is successful',
        () async {
      controller.loadMoreMessages();
    });
    test(
        'should update state with messages and set refreshController when API call is successful',
        () async {
      controller.fetchMessages(true);
      controller.fetchMessages(false);
    });
  });

  group('agreeClick', () {
    test('should update the message when applyApi call is successful',
        () async {
      var msg = PartnerMessage(type: 3,partnerId: 200);
      controller.refuseClick(msg);
      
      msg = PartnerMessage(type: 3,partnerId: 300);
      controller.refuseClick(msg);
      
      msg = PartnerMessage(type: 2,partnerId: 123);
      controller.refuseClick(msg);

      msg = PartnerMessage(type: 2);
      controller.refuseClick(msg);
    });
    test('should update the message when applyApi call is successful',
        () async {
      var msg = PartnerMessage(type: 3,partnerId: 200);
      controller.agreeClick(msg);
      
      msg = PartnerMessage(type: 3,partnerId: 300);
      controller.agreeClick(msg);

      msg = PartnerMessage(type: 2,partnerId: 123);
      controller.agreeClick(msg);

      msg = PartnerMessage(type: 2);
      controller.agreeClick(msg);
    });
    test('should update the message when applyApi call is successful',
        () async {
      var msg = PartnerMessage(type: 2);
      controller.handleApply(msg, 'appove');

      msg = PartnerMessage(type: 3);
      controller.handleApply(msg, 'appove');

      msg = PartnerMessage(type: 3, partnerId: 123);
      controller.handleApply(msg, 'appove');

      msg = PartnerMessage(type: 4);
      controller.handleApply(msg, 'appove');

      msg = PartnerMessage(type: 4, inviteRecordId: 123);
      controller.handleApply(msg, 'appove');
    });

    test('should update the message when applyApi call is successful',
        () async {
      var msg = PartnerMessage(jumpUrl: '');
      controller.visitPartnerHome(msg);

      msg = PartnerMessage(jumpUrl: 'testRouter', partnerId: 123);
      controller.visitPartnerHome(msg);

      msg = PartnerMessage(partnerId: 123);
      controller.visitPartnerHome(msg);
    });
  });
}
