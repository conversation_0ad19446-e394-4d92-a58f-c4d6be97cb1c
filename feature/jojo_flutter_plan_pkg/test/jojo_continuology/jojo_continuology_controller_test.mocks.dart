// Mocks generated by Mockito 5.4.0 from annotations
// in jojo_flutter_plan_pkg/test/jojo_continuology/jojo_continuology_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:jojo_flutter_base/base.dart' as _i2;
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/model/jojo_continuology_api_data.dart'
    as _i4;
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/util/countdown_timer_helper.dart'
    as _i5;
import 'package:jojo_flutter_plan_pkg/pages/jojo_continuology/util/study_guide_audio_impl.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAudioPlayer_0 extends _i1.SmartFake implements _i2.AudioPlayer {
  _FakeAudioPlayer_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [StudyGuideAudioImpl].
///
/// See the documentation for Mockito's code generation for more information.
class MockStudyGuideAudioImpl extends _i1.Mock
    implements _i3.StudyGuideAudioImpl {
  MockStudyGuideAudioImpl() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AudioPlayer get audioPlayer => (super.noSuchMethod(
        Invocation.getter(#audioPlayer),
        returnValue: _FakeAudioPlayer_0(
          this,
          Invocation.getter(#audioPlayer),
        ),
      ) as _i2.AudioPlayer);

  @override
  void playAudioWithUrl(String? url) => super.noSuchMethod(
        Invocation.method(
          #playAudioWithUrl,
          [url],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void pauseAudio() => super.noSuchMethod(
        Invocation.method(
          #pauseAudio,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void stopAudio() => super.noSuchMethod(
        Invocation.method(
          #stopAudio,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void resume() => super.noSuchMethod(
        Invocation.method(
          #resume,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  String findPlayAudioUrl({
    required int? scene,
    required List<_i4.GuideResource>? guideResourceList,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #findPlayAudioUrl,
          [],
          {
            #scene: scene,
            #guideResourceList: guideResourceList,
          },
        ),
        returnValue: '',
      ) as String);
}

/// A class which mocks [CountdownTimerHelper].
///
/// See the documentation for Mockito's code generation for more information.
class MockCountdownTimerHelper extends _i1.Mock
    implements _i5.CountdownTimerHelper {
  MockCountdownTimerHelper() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get remainingSeconds => (super.noSuchMethod(
        Invocation.getter(#remainingSeconds),
        returnValue: 0,
      ) as int);

  @override
  int get totalSeconds => (super.noSuchMethod(
        Invocation.getter(#totalSeconds),
        returnValue: 0,
      ) as int);

  @override
  bool get isRunning => (super.noSuchMethod(
        Invocation.getter(#isRunning),
        returnValue: false,
      ) as bool);

  @override
  bool get isPaused => (super.noSuchMethod(
        Invocation.getter(#isPaused),
        returnValue: false,
      ) as bool);

  @override
  bool get isCompleted => (super.noSuchMethod(
        Invocation.getter(#isCompleted),
        returnValue: false,
      ) as bool);

  @override
  void startCountdown(int? seconds) => super.noSuchMethod(
        Invocation.method(
          #startCountdown,
          [seconds],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void pause() => super.noSuchMethod(
        Invocation.method(
          #pause,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void resume() => super.noSuchMethod(
        Invocation.method(
          #resume,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void stop() => super.noSuchMethod(
        Invocation.method(
          #stop,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void reset([int? newSeconds]) => super.noSuchMethod(
        Invocation.method(
          #reset,
          [newSeconds],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void restart([int? newSeconds]) => super.noSuchMethod(
        Invocation.method(
          #restart,
          [newSeconds],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
